<template>
  <v-container>
    <v-card
      flat
      dark
      class="d-flex align-center justify-center top_bg mb-3"
    >
      <div style="background-image: url('../assets/img/icons/affiliate.svg') no-repeat right center;">
        <div class="ma-3 my-md-8 text-center py-5">
          <v-icon x-large>
            mdi-controller
          </v-icon>
          <h3>Casual Games</h3>
        </div>
      </div>
    </v-card>

    <v-row>
      <v-col
        cols="6"
        sm="4"
        md="3"
        lg="2"
      >
        <v-card
          flat
          color="box_bg"
          @click="h5game(30755)"
        >
          <v-img
            src="../assets/img/pic/mini_games/30755.webp"
            aspect-ratio="1"
          >
            <v-icon
              x-large
              class="d-flex align-center justify-center opacity-8 white--text"
              style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);"
            >
              mdi-play-circle
            </v-icon>
          </v-img>
          <v-card-text class="pa-2">
            Blocky Blast Puzzle
          </v-card-text>
        </v-card>
      </v-col>

      <v-col
        cols="6"
        sm="4"
        md="3"
        lg="2"
      >
        <v-card
          flat
          color="box_bg"
          @click="h5game(30668)"
        >
          <v-img
            src="../assets/img/pic/mini_games/30668.webp"
            aspect-ratio="1"
          >
            <v-icon
              x-large
              class="d-flex align-center justify-center opacity-8 white--text"
              style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);"
            >
              mdi-play-circle
            </v-icon>
          </v-img>
          <v-card-text class="pa-2">
            Level Devil
          </v-card-text>
        </v-card>
      </v-col>

      <v-col
        cols="6"
        sm="4"
        md="3"
        lg="2"
      >
        <v-card
          flat
          color="box_bg"
          @click="h5game(30838)"
        >
          <v-img
            src="../assets/img/pic/mini_games/30838.webp"
            aspect-ratio="1"
          >
            <v-icon
              x-large
              class="d-flex align-center justify-center opacity-8 white--text"
              style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);"
            >
              mdi-play-circle
            </v-icon>
          </v-img>
          <v-card-text class="pa-2">
            Marble Run 3D
          </v-card-text>
        </v-card>
      </v-col>

      <v-col
        cols="6"
        sm="4"
        md="3"
        lg="2"
      >
        <v-card
          flat
          color="box_bg"
          @click="h5game(30500)"
        >
          <v-img
            src="../assets/img/pic/mini_games/30500.webp"
            aspect-ratio="1"
          >
            <v-icon
              x-large
              class="d-flex align-center justify-center opacity-8 white--text"
              style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);"
            >
              mdi-play-circle
            </v-icon>
          </v-img>
          <v-card-text class="pa-2">
            Fun Water Sorting
          </v-card-text>
        </v-card>
      </v-col>

      <v-col
        cols="6"
        sm="4"
        md="3"
        lg="2"
      >
        <v-card
          flat
          color="box_bg"
          @click="h5game(29145)"
        >
          <v-img
            src="../assets/img/pic/mini_games/29145.webp"
            aspect-ratio="1"
          >
            <v-icon
              x-large
              class="d-flex align-center justify-center opacity-8 white--text"
              style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);"
            >
              mdi-play-circle
            </v-icon>
          </v-img>
          <v-card-text class="pa-2">
            Stickman Hook
          </v-card-text>
        </v-card>
      </v-col>

      <v-col
        cols="6"
        sm="4"
        md="3"
        lg="2"
      >
        <v-card
          flat
          color="box_bg"
          @click="h5game(31455)"
        >
          <v-img
            src="../assets/img/pic/mini_games/31455.webp"
            aspect-ratio="1"
          >
            <v-icon
              x-large
              class="d-flex align-center justify-center opacity-8 white--text"
              style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);"
            >
              mdi-play-circle
            </v-icon>
          </v-img>
          <v-card-text class="pa-2">
            Happy Glass
          </v-card-text>
        </v-card>
      </v-col>

      <v-col
        cols="6"
        sm="4"
        md="3"
        lg="2"
      >
        <v-card
          flat
          color="box_bg"
          @click="h5game(30370)"
        >
          <v-img
            src="../assets/img/pic/mini_games/30370.webp"
            aspect-ratio="1"
          >
            <v-icon
              x-large
              class="d-flex align-center justify-center opacity-8 white--text"
              style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);"
            >
              mdi-play-circle
            </v-icon>
          </v-img>
          <v-card-text class="pa-2">
            Drive Mad
          </v-card-text>
        </v-card>
      </v-col>

      <v-col
        cols="6"
        sm="4"
        md="3"
        lg="2"
      >
        <v-card
          flat
          color="box_bg"
          @click="h5game(25273)"
        >
          <v-img
            src="../assets/img/pic/mini_games/25273.webp"
            aspect-ratio="1"
          >
            <v-icon
              x-large
              class="d-flex align-center justify-center opacity-8 white--text"
              style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);"
            >
              mdi-play-circle
            </v-icon>
          </v-img>
          <v-card-text class="pa-2">
            Moto X3M
          </v-card-text>
        </v-card>
      </v-col>

      <v-col
        cols="6"
        sm="4"
        md="3"
        lg="2"
      >
        <v-card
          flat
          color="box_bg"
          @click="h5game(23233)"
        >
          <v-img
            src="../assets/img/pic/mini_games/23233.webp"
            aspect-ratio="1"
          >
            <v-icon
              x-large
              class="d-flex align-center justify-center opacity-8 white--text"
              style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);"
            >
              mdi-play-circle
            </v-icon>
          </v-img>
          <v-card-text class="pa-2">
            Murder
          </v-card-text>
        </v-card>
      </v-col>

      <v-col
        cols="6"
        sm="4"
        md="3"
        lg="2"
      >
        <v-card
          flat
          color="box_bg"
          @click="h5game(30875)"
        >
          <v-img
            src="../assets/img/pic/mini_games/30875.webp"
            aspect-ratio="1"
          >
            <v-icon
              x-large
              class="d-flex align-center justify-center opacity-8 white--text"
              style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);"
            >
              mdi-play-circle
            </v-icon>
          </v-img>
          <v-card-text class="pa-2">
            Color Pencil Run
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    <template v-if="showdialog">
      <Login
        v-model="loginreg"
        @closeloginreg="closeloginreg"
        @showforget="showforget"
      />
    </template>
    <template v-if="forget">
      <Forget
        @closeforget="closeforget"
      />
    </template>
    <template v-if="showdialogh5game">
      <H5game
        v-model="gameid"
        @closeh5game="closeh5game"
      />
    </template>
  </v-container>
</template>

<script>
import Login from '@/components/Login.vue'
import Forget from '@/components/Forget.vue'
import H5game from '@/components/H5game.vue'
export default {
  components: {
    Login,
    Forget,
    H5game
  },
  data: () => ({
    showdialog: false,
    loginreg: false,
    forget: false,
    member_id: 0,
    list: [],
    showdialogh5game: false,
    gameid: 0
  }),
  computed: {

  },
  watch: {

  },
  created() {
    this.videos()
  },
  mounted() {
    if(this.getStorage('member_id')){
      this.member_id = this.getStorage('member_id')
    }
  },
  beforeDestroy() {

  },
  methods: {
    videos() {
			try {
				let paramObj = {
          member_id: this.getStorage('member_id')
        }
        this.$server.videos(paramObj).then((response) => {
          if(response.code==200){
            this.list = response.data.data
          }
        })
			} catch (error) {

			}
		},
    showloginreg(tab) {
      this.loginreg = tab
      this.showdialog = true
    },
    closeloginreg() {
      this.loginreg = false
      this.showdialog = false
    },
    showforget() {
      this.forget = true
    },
    closeforget() {
      this.forget = false
    },
    h5game(gameid) {
      if(this.member_id>0){
        this.gameid = gameid
        this.showdialogh5game = true
      }else{
        this.showloginreg('login')
      }
    },
    closeh5game() {
      this.showdialogh5game = false
    },
  }

}
</script>

<style>
.top_bg {
  background: 
    linear-gradient(-225deg, #b009fd 0%, rgb(65, 162, 192) 52%, #345de3 100%);
}
</style>
