<template>
  <div>
    <v-row dense>
      <v-col
        cols="12"
        md="4"
      >
        <v-sheet
          rounded
          color="box_bg"
          class="text-center pa-3 text--disabled"
        >
          <small>{{ $t('zyqjj') }}</small>
          <div>
            <small>₱</small>
            <strong class="ml-1 text--primary">{{ invites.invite_award }}</strong>
          </div>
        </v-sheet>
      </v-col>
      <v-col
        cols="6"
        md="4"
      >
        <v-sheet
          rounded
          color="box_bg"
          class="text-center pa-3 text--disabled"
        >
          <small>{{ $t('yxyqjj') }}</small>
          <div>
            <small>₱</small>
            <strong class="ml-1 text--primary">{{ invites.reg_valid_award }}</strong>
          </div>
        </v-sheet>
      </v-col>
      <v-col
        cols="6"
        md="4"
      >
        <v-sheet
          rounded
          color="box_bg"
          class="text-center pa-3 text--disabled"
        >
          <small>{{ $t('cunkuan') }} >500{{ $t('jiangjin') }}</small>
          <div>
            <small>₱</small>
            <strong class="ml-1 text--primary">{{ invites.depositexceed200_award }}</strong>
          </div>
        </v-sheet>
      </v-col>
    </v-row>

    <v-card
      flat
      color="box_bg"
      class="mt-3"
    >
      <v-card-text>
        <v-row dense>
          <v-col
            cols="6"
            md="3"
          >
            <v-menu
              ref="menu"
              v-model="menu"
              :close-on-content-click="false"
              :return-value.sync="dateRangeText"
              transition="scale-transition"
              offset-y
              min-width="auto"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  v-model="dateRangeText"
                  :label="$t('jjrq')"
                  readonly
                  v-bind="attrs"
                  dense
                  outlined
                  hide-details
                  v-on="on"
                />
              </template>
              <v-date-picker
                v-model="dates"
                no-title
                scrollable
                range
                color="indigo"
                locale="en-US"
                class="box_bg"
              >
                <v-btn
                  text
                  @click="menu = false"
                >
                  Cancel
                </v-btn>
                <v-spacer />
                <v-btn
                  text
                  color="primary"
                  @click="$refs.menu.save(dates)"
                >
                  OK
                </v-btn>
              </v-date-picker>
            </v-menu>
          </v-col>

          <v-col
            cols="6"
            md="3"
          >
            <v-btn
              depressed
              color="primary"
              @click="invite_award_list"
            >
              <v-icon left>
                mdi-magnify
              </v-icon>
              {{ $t('sousuo') }}
            </v-btn>
          </v-col>
        </v-row>
      </v-card-text>

      <v-divider />

      <div
        class="overflow-x-auto"
        style="white-space: nowrap;"
      >
        <div
          v-if="list.length==0"
          class="text-center text--disabled my-10"
        >
          <v-icon
            x-large
            class="text--disabled"
          >
            mdi-archive-search-outline
          </v-icon>
          <small class="d-block text-center">{{ $t('myrhjl') }}</small>
        </div>
        <v-simple-table v-if="list.length>0">
          <thead class="nav_bg">
            <tr>
              <th>{{ $t('jjrq') }}</th>
              <th>{{ $t('zcyhs') }}</th>
              <th>{{ $t('yxyq') }}</th>
              <th>{{ $t('cunkuan') }} >500</th>
              <th>{{ $t('tjjj') }}(₱)</th>
              <th>{{ $t('lqsj') }}</th>
              <th>{{ $t('beizhu') }}</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="(item, index) in list"
              :key="'i'+index"
            >
              <td>{{ item.day }}</td>
              <td>{{ item.reg_total }}</td>
              <td>{{ item.reg_valid }}</td>
              <td>{{ item.depositexceed200 }}</td>
              <td>
                <small class="mr-1 text--disabled">₱</small>
                <strong class="orange--text">{{ item.invite_award }}</strong>
              </td>
              <td>{{ item.received_time?formattime(item.received_time):'' }}</td>
              <td>-</td>
            </tr>
          </tbody>
        </v-simple-table>

        <v-divider />

        <v-card-actions>
          <v-spacer />
          <v-pagination
            v-model="page"
            :length="pagecount"
            :total-visible="7"
            @input="invite_award_list"
          />
          <v-spacer />
        </v-card-actions>
      </div>
    </v-card>
  </div>
</template>
<script>
export default {
  name: 'InviteAward',
  components:{
  },
  props: {

  },
  data: () => ({
    invites: {
      "reg_valid_award": 0,
      "depositexceed200_award": 0,
      "invite_award": 0
    },
    menu: false,
    dates: [],
    page: 1,
    pagecount: 1,
    list: [

    ]
  }),
  computed: {
    dateRangeText:{
      get: function () {
        return this.dates.join(' ~ ')
      },
      set: function (newVal) {

      }
    }
  },
  watch:{
    dateRangeText: {
      handler(newVal, oldVal) {
        this.dates.sort()
      }
    },
  },
  created() {
    this.invite_award()
  },
  mounted() {
    this.invite_award_list()
  },
  methods: {
    formattime(timestamp) {
      var date = new Date(timestamp * 1000);
      var Y = date.getFullYear();
      var M = String(date.getMonth() + 1).padStart(2, '0');
      var D = String(date.getDate()).padStart(2, '0');
      var h = String(date.getHours()).padStart(2, '0');
      var m = String(date.getMinutes()).padStart(2, '0');
      var s = String(date.getSeconds()).padStart(2, '0');
      return D+'/'+M+'/'+Y+' '+h+':'+m;
    },
    invite_award() {
      let paramObj = {
        member_id: this.getStorage('member_id'),
      }
      this.$server.invite_award(paramObj).then((response) => {
        if(response.code==200){
          this.invites = response.data
        }else{
          
        }
      })
    },
    invite_award_list() {
      let paramObj = {
        page:this.page,
        member_id: this.getStorage('member_id'),
        startday: this.dates[0],
        endday: this.dates[1],
      }
      this.$server.invite_award_list(paramObj).then((response) => {
        if(response.code==200){
          this.pagecount = response.data.last_page
          this.list = response.data.data
        }else{
          
        }
      })
    }
  },
};
</script>