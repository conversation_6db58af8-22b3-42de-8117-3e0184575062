<template>
  <v-container>
    <v-subheader>
      <v-btn
        icon
        small
        to="/"
      >
        <v-icon small>
          mdi-arrow-left
        </v-icon>
      </v-btn>
      <strong class="ml-2">{{ $t('tjbzq') }}</strong>
    </v-subheader>

    <v-card
      flat
      dark
      class="d-flex align-center justify-center top_bg mb-3"
    >
      <div style="background-image: url('../assets/img/icons/affiliate.svg') no-repeat right center;">
        <div class="my-3 my-md-8 text-center">
          <v-card-title class="text-h6 text-md-h4 pb-0">
            <v-spacer />
            {{ $t('yqpybhd') }}
            <v-spacer />
          </v-card-title>
          <v-card-subtitle class="my-1 text-subtitle-2 text-md-h6">
            {{ $t('yqjj') }}+{{ $t('tzyj') }}+{{ $t('ckyj') }}
          </v-card-subtitle>
        </div>
      </div>
    </v-card>

    <v-sheet
      v-if="member_id>0"
      rounded="pill"
      color="nav_bg"
    >
      <v-chip-group
        v-model="selchip"
        mandatory
        color="primary"
        class="my-3 px-2 rounded-lg font-weight-medium"
      >
        <v-chip
          value="home"
          @click="changechip('home')"
        >
          {{ $t('shouye') }}
        </v-chip>
        <v-chip
          value="activity"
          dark
          color="#0766ff"
          active-class="white--text"
          @click="changechip('activity')"
        >
          <v-icon left>
            mdi-facebook
          </v-icon>
          Post + ₱ 25
        </v-chip>
        <v-chip
          value="dashboard"
          @click="changechip('dashboard')"
        >
          {{ $t('yibiaopan') }}
        </v-chip>
        <v-chip
          value="team"
          @click="changechip('team')"
        >
          {{ $t('tdgl') }}
        </v-chip>
      <!--
        <v-chip
          value="bet"
          @click="changechip('bet')"
        >
          {{ $t('tzyj') }}
        </v-chip>
        <v-chip
          value="deposit"
          @click="changechip('deposit')"
        >
          {{ $t('ckyj') }}
        </v-chip>
        <v-chip
          value="award"
          @click="changechip('award')"
        >
          {{ $t('tjjj') }}
        </v-chip>
      -->
      </v-chip-group>
    </v-sheet>

    <template v-if="selchip=='award'">
      <InviteAward />
    </template>
    <template v-if="selchip=='bet'">
      <InviteBet />
    </template>
    <template v-if="selchip=='dashboard'">
      <InviteDashboard @changechip="changechip" />
    </template>
    <template v-if="selchip=='deposit'">
      <InviteDeposit />
    </template>
    <template v-if="selchip=='home'">
      <InviteHome @changechip="changechip" />
    </template>
    <template v-if="selchip=='team'">
      <InviteTeam />
    </template>
    <template v-if="selchip=='activity'">
      <InviteActivity />
    </template>
  </v-container>
</template>
<script>
import InviteAward from '@/views/pages/Invite_award.vue'
import InviteBet from '@/views/pages/Invite_bet.vue'
import InviteDashboard from '@/views/pages/Invite_dashboard.vue'
import InviteDeposit from '@/views/pages/Invite_deposit.vue'
import InviteHome from '@/views/pages/Invite_home.vue'
import InviteTeam from '@/views/pages/Invite_team.vue'
import InviteActivity from '@/views/pages/Invite_activity.vue'

export default {
  name: 'Invite',
  components: {
    InviteAward,InviteBet,InviteDashboard,InviteDeposit,InviteHome,InviteTeam,InviteActivity
  },
  data: () => ({
    selchip: 'home',
    member_id: 0,
  }),
  computed: {

  },
  watch: {

  },
  created() {
    this.member_id = this.getStorage('member_id')
  },
  mounted() {
    this.changechip(this.$route.query.chip??'home')
  },
  beforeDestroy() {

  },
  methods: {
    changechip(selchip){
      this.selchip = selchip
    },
  }

}
</script>
<style>
.top_bg{
  background-color: #9A62FF;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100%25'%3E%3Cdefs%3E%3ClinearGradient id='a' gradientUnits='userSpaceOnUse' x1='0' x2='0' y1='0' y2='100%25' gradientTransform='rotate(240)'%3E%3Cstop offset='0' stop-color='%239A62FF'/%3E%3Cstop offset='1' stop-color='%232994FF'/%3E%3C/linearGradient%3E%3Cpattern patternUnits='userSpaceOnUse' id='b' width='540' height='450' x='0' y='0' viewBox='0 0 1080 900'%3E%3Cg fill-opacity='0.1'%3E%3Cpolygon fill='%23444' points='90 150 0 300 180 300'/%3E%3Cpolygon points='90 150 180 0 0 0'/%3E%3Cpolygon fill='%23AAA' points='270 150 360 0 180 0'/%3E%3Cpolygon fill='%23DDD' points='450 150 360 300 540 300'/%3E%3Cpolygon fill='%23999' points='450 150 540 0 360 0'/%3E%3Cpolygon points='630 150 540 300 720 300'/%3E%3Cpolygon fill='%23DDD' points='630 150 720 0 540 0'/%3E%3Cpolygon fill='%23444' points='810 150 720 300 900 300'/%3E%3Cpolygon fill='%23FFF' points='810 150 900 0 720 0'/%3E%3Cpolygon fill='%23DDD' points='990 150 900 300 1080 300'/%3E%3Cpolygon fill='%23444' points='990 150 1080 0 900 0'/%3E%3Cpolygon fill='%23DDD' points='90 450 0 600 180 600'/%3E%3Cpolygon points='90 450 180 300 0 300'/%3E%3Cpolygon fill='%23666' points='270 450 180 600 360 600'/%3E%3Cpolygon fill='%23AAA' points='270 450 360 300 180 300'/%3E%3Cpolygon fill='%23DDD' points='450 450 360 600 540 600'/%3E%3Cpolygon fill='%23999' points='450 450 540 300 360 300'/%3E%3Cpolygon fill='%23999' points='630 450 540 600 720 600'/%3E%3Cpolygon fill='%23FFF' points='630 450 720 300 540 300'/%3E%3Cpolygon points='810 450 720 600 900 600'/%3E%3Cpolygon fill='%23DDD' points='810 450 900 300 720 300'/%3E%3Cpolygon fill='%23AAA' points='990 450 900 600 1080 600'/%3E%3Cpolygon fill='%23444' points='990 450 1080 300 900 300'/%3E%3Cpolygon fill='%23222' points='90 750 0 900 180 900'/%3E%3Cpolygon points='270 750 180 900 360 900'/%3E%3Cpolygon fill='%23DDD' points='270 750 360 600 180 600'/%3E%3Cpolygon points='450 750 540 600 360 600'/%3E%3Cpolygon points='630 750 540 900 720 900'/%3E%3Cpolygon fill='%23444' points='630 750 720 600 540 600'/%3E%3Cpolygon fill='%23AAA' points='810 750 720 900 900 900'/%3E%3Cpolygon fill='%23666' points='810 750 900 600 720 600'/%3E%3Cpolygon fill='%23999' points='990 750 900 900 1080 900'/%3E%3Cpolygon fill='%23999' points='180 0 90 150 270 150'/%3E%3Cpolygon fill='%23444' points='360 0 270 150 450 150'/%3E%3Cpolygon fill='%23FFF' points='540 0 450 150 630 150'/%3E%3Cpolygon points='900 0 810 150 990 150'/%3E%3Cpolygon fill='%23222' points='0 300 -90 450 90 450'/%3E%3Cpolygon fill='%23FFF' points='0 300 90 150 -90 150'/%3E%3Cpolygon fill='%23FFF' points='180 300 90 450 270 450'/%3E%3Cpolygon fill='%23666' points='180 300 270 150 90 150'/%3E%3Cpolygon fill='%23222' points='360 300 270 450 450 450'/%3E%3Cpolygon fill='%23FFF' points='360 300 450 150 270 150'/%3E%3Cpolygon fill='%23444' points='540 300 450 450 630 450'/%3E%3Cpolygon fill='%23222' points='540 300 630 150 450 150'/%3E%3Cpolygon fill='%23AAA' points='720 300 630 450 810 450'/%3E%3Cpolygon fill='%23666' points='720 300 810 150 630 150'/%3E%3Cpolygon fill='%23FFF' points='900 300 810 450 990 450'/%3E%3Cpolygon fill='%23999' points='900 300 990 150 810 150'/%3E%3Cpolygon points='0 600 -90 750 90 750'/%3E%3Cpolygon fill='%23666' points='0 600 90 450 -90 450'/%3E%3Cpolygon fill='%23AAA' points='180 600 90 750 270 750'/%3E%3Cpolygon fill='%23444' points='180 600 270 450 90 450'/%3E%3Cpolygon fill='%23444' points='360 600 270 750 450 750'/%3E%3Cpolygon fill='%23999' points='360 600 450 450 270 450'/%3E%3Cpolygon fill='%23666' points='540 600 630 450 450 450'/%3E%3Cpolygon fill='%23222' points='720 600 630 750 810 750'/%3E%3Cpolygon fill='%23FFF' points='900 600 810 750 990 750'/%3E%3Cpolygon fill='%23222' points='900 600 990 450 810 450'/%3E%3Cpolygon fill='%23DDD' points='0 900 90 750 -90 750'/%3E%3Cpolygon fill='%23444' points='180 900 270 750 90 750'/%3E%3Cpolygon fill='%23FFF' points='360 900 450 750 270 750'/%3E%3Cpolygon fill='%23AAA' points='540 900 630 750 450 750'/%3E%3Cpolygon fill='%23FFF' points='720 900 810 750 630 750'/%3E%3Cpolygon fill='%23222' points='900 900 990 750 810 750'/%3E%3Cpolygon fill='%23222' points='1080 300 990 450 1170 450'/%3E%3Cpolygon fill='%23FFF' points='1080 300 1170 150 990 150'/%3E%3Cpolygon points='1080 600 990 750 1170 750'/%3E%3Cpolygon fill='%23666' points='1080 600 1170 450 990 450'/%3E%3Cpolygon fill='%23DDD' points='1080 900 1170 750 990 750'/%3E%3C/g%3E%3C/pattern%3E%3C/defs%3E%3Crect x='0' y='0' fill='url(%23a)' width='100%25' height='100%25'/%3E%3Crect x='0' y='0' fill='url(%23b)' width='100%25' height='100%25'/%3E%3C/svg%3E");
  background-attachment: fixed;
  background-size: cover;
  background-position: center;
}
.card_box_bg{
  background-color: #000000;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100%25' height='100%25' viewBox='0 0 800 800'%3E%3Cg %3E%3Ccircle fill='%23000000' cx='400' cy='400' r='600'/%3E%3Ccircle fill='%23001456' cx='400' cy='400' r='500'/%3E%3Ccircle fill='%23002167' cx='400' cy='400' r='400'/%3E%3Ccircle fill='%23002f79' cx='400' cy='400' r='300'/%3E%3Ccircle fill='%230e3d8b' cx='400' cy='400' r='200'/%3E%3Ccircle fill='%232C4D9D' cx='400' cy='400' r='100'/%3E%3C/g%3E%3C/svg%3E");
  background-attachment: fixed;
  background-size: cover;
}
</style>
