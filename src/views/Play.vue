<template>
  <v-app :style="{background: $vuetify.theme.themes.light.background, color: $vuetify.theme.themes.dark.color}">
    <div
      class="d-flex align-center pa-4"
      style="position: fixed; top: 0; left: 0; right: 0; z-index: 100; background: linear-gradient(to top, rgba(0,0,0,0) 0%, rgba(0,0,0,0.7) 100%);"
    >
      <v-btn
        icon
        @click="$router.back()"
      >
        <v-icon large>
          mdi-chevron-left
        </v-icon>
      </v-btn>
    </div>

    <swiper
      ref="mySwiper"
      class="swiper"
      :options="swiperOption"
      @slide-change="slideChange"
    >
      <swiper-slide
        v-for="(item, index) in videoinfo.episodes"
        :key="index"
      >
        <div
          :id="'video-player' + index"
          @click="togglePlay"
        />
      </swiper-slide>
    </swiper>

    <div
      v-if="isPaused"
      class="center-play-button"
      @click="togglePlay"
    >
      <i class="play-icon" />
    </div>

    <div
      class="video-info pa-4 d-flex justify-space-between align-center"
      style="width: 100%;"
    >
      <!-- 左侧标题和简介 -->
      <div class="text-h6 grey--text text--lighten-4">
        {{ videoinfo.title }}
        <div class="text-body-2">
          ( <span class="primary--text font-weight-medium">{{ currentIndex + 1 }}</span> / {{ videoinfo.episodes.length }} )
        </div>
      </div>
			
      <!-- 右侧选集按钮 -->
      <div class="ml-4">
        <v-btn
          fab
          depressed
          @click="showEpisodeList"
        >
          <v-icon size="24">
            mdi-playlist-play
          </v-icon>
        </v-btn>
      </div>
    </div>

    <!-- 底部弹出选集列表 -->
    <v-bottom-sheet
      v-model="showPopup"
      class="nav_bg"
    >
      <v-card>
        <v-card-title class="d-flex justify-space-between align-center pa-4">
          <div>
            <span>{{ videoinfo.title }}</span>
            <div class="text-body-2">
              <span class="primary--text font-weight-medium">{{ currentIndex + 1 }}</span> / {{ videoinfo.episodes.length }}
            </div>
          </div>
          <v-btn
            icon
            @click="closePopup"
          >
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>
				
				
        <v-card-text
          class="pa-4 episode-list-container"
        >
          <div class="d-flex flex-wrap episode-scroll">
            <div
              v-for="(episode, index) in videoinfo.episodes"
              :key="index"
              style="width: 20%; padding: 4px;"
            >
              <div class="episode-btn-wrapper">
                <v-btn
                  block
                  depressed
                  :color="currentIndex === index ? 'primary' : 'btn_bg'"
                  height="48"
                  class="text-none"
                  @click="playEpisode(index)"
                >
                  {{ index + 1 }}
                  <v-icon
                    v-if="episode.locked"
                    size="16"
                    color="red"
                    class="lock-icon"
                  >
                    mdi-lock
                  </v-icon>
                </v-btn>
              </div>
            </div>
          </div>
        </v-card-text>
      </v-card>
    </v-bottom-sheet>

    <v-dialog
      v-model="dialogUnlock"
      max-width="290"
    >
      <v-alert type="warning">
        Complete a deposit to unlock all series.
      </v-alert>
    </v-dialog>
  </v-app>
</template>

<script>
import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
import 'swiper/css/swiper.css'
import Player from 'xgplayer'
import HlsPlugin from 'xgplayer-hls'
import 'xgplayer/dist/index.min.css'
export default {
  name: 'Play',
  components: {
    Swiper,
    SwiperSlide
  },
  data: () => ({
    vid: null,
    swiperOption: {
      direction: 'vertical',
      slidesPerView: 1,
      spaceBetween: 1
    },
    dataLoaded: false,
    currentIndex: 0,
    videoinfo: {
      episodes: []
    },
    controlsHidden: false,
    hideControlsTimer: null,
    isVideoPlaying: false,
    showPopup: false,
    dialogUnlock: false,
    isPaused: true,
    players: {}, // 用于存储播放器实例
    isIOS: false,
  }),
  computed: {
    currentEpisode() {
			return this.videoinfo.episodes[this.currentIndex]
		}
  },
  watch: {
    currentIndex(newValue, oldValue) {
			this.showControls()
      this.watchlog(this.videoinfo.episodes[this.currentIndex].id)
		}
  },
  created() {
    this.vid = this.$route.query.vid
    this.isIOS = this.isAppleDevice();
		this.episodes()
  },
  mounted() {
    this.showControls()
  },
  beforeDestroy() {
    if (this.hideControlsTimer) {
			clearTimeout(this.hideControlsTimer)
		}
	},
	methods: {
		episodes() {
			try {
				let paramObj = {
          member_id: this.getStorage('member_id'),
          vid: this.vid
        }
        this.$server.episodes(paramObj).then((response) => {
          if(response.code==200){
            this.videoinfo = response.data.videoinfo
            this.currentIndex = response.data.currentIndex
            this.dataLoaded = true;
            //数据加载后再挂在组件
            this.$nextTick(() => {
              this.initializePlayers()
              this.watchlog(this.videoinfo.episodes[this.currentIndex].id)
            });
          }
        })
			} catch (error) {

			}
		},
    watchlog(episode_id) {
			try {
				let paramObj = {
          member_id: this.getStorage('member_id'),
          episode_id: episode_id
        }
        this.$server.watchlog(paramObj).then((response) => {

        })
			} catch (error) {

			}
		},
		onVideoStateChange(isPlaying) {
			this.isVideoPlaying = isPlaying
			if (!isPlaying) {
				this.showControls()
			} else {
				this.resetHideControlsTimer()
			}
		},
		showControls() {
			this.controlsHidden = false
			this.resetHideControlsTimer()
		},
		hideControls() {
			this.controlsHidden = true
			if (this.hideControlsTimer) {
				clearTimeout(this.hideControlsTimer)
			}
		},
		resetHideControlsTimer() {
			if (this.hideControlsTimer) {
				clearTimeout(this.hideControlsTimer)
			}
			this.hideControlsTimer = setTimeout(() => {
				if (this.isVideoPlaying) {
					this.hideControls()
				}
			}, 3000)
		},
		showEpisodeList() {
			this.showPopup = true
		},
		closePopup() {
			this.showPopup = false
		},
		slideChange() {
      if (!this.$refs.mySwiper || !this.$refs.mySwiper.$swiper) {
        console.warn('Swiper not initialized yet');
        return;
      }
      // 获取当前活动的slide索引
      const current = this.$refs.mySwiper.$swiper.activeIndex;
			this.currentIndex = current;
			// 暂停所有视频
			this.pauseAllVideos();
			// 销毁不需要的播放器实例
			this.cleanupPlayers(current);
			// 加载当前、前一个和后一个视频
			this.initializePlayer(current);
			this.initializePlayer(current - 1);
			this.initializePlayer(current + 1);
			// 播放当前视频
      this.$nextTick(() => {
        this.playVideo(current);
      });
		},
		cleanupPlayers(current) {
			Object.keys(this.players).forEach(videoId => {
				const index = parseInt(videoId.replace('video-player', ''));
				// 只保留当前、前一个和后一个的实例
				if (index < current - 1 || index > current + 1) {
					this.players[videoId].destroy(); // 销毁播放器实例
					delete this.players[videoId]; // 删除实例
				}
			});
		},
		initializePlayers() {
			// 只初始化当前、前一个和后一个视频
			this.initializePlayer(this.currentIndex);
			this.initializePlayer(this.currentIndex - 1);
			this.initializePlayer(this.currentIndex + 1);
		},
		initializePlayer(index) {
			if (index < 0 || index >= this.videoinfo.episodes.length) return; // 确保索引有效
			const videoId = 'video-player' + index;
			if (!this.players[videoId]) {
				let config = {
					id: videoId,
					url: this.videoinfo.episodes[index].url,
					poster: this.videoinfo.image,
					height: window.innerHeight,
					width: window.innerWidth,
					videoFillMode: 'contain',
					playsinline: true,
					ignores: ['play', 'fullscreen', 'volume', 'playbackrate'],
					videoAttributes: {
						preload: 'auto',
						playsinline: true,
						'webkit-playsinline': true,
					},
					controls: {
						autoHide: false
					}
				};

				if (this.videoinfo.episodes[index].url.indexOf(".m3u8") > 0) {
					config.plugins = [HlsPlugin];
				}
				if (index === 0) {
					if (this.isIOS) {
						config.volume = 0;
					}
					config.autoplay = true;
					config.videoInit = true;
				}
				this.players[videoId] = new Player(config);
				this.players[videoId].on('play', () => {
					this.isPaused = false;
				});
				this.players[videoId].on('pause', () => {
					this.isPaused = true;
				});
				this.players[videoId].on('ended', () => {
					if (this.currentIndex < this.videoinfo.episodes.length - 1) {
						this.currentIndex += 1;
						this.$refs.mySwiper.$swiper.slideTo(this.currentIndex, 0, false)
						this.slideChange()
					}
				});
			}
		},
		playVideo(index) {
			const videoId = 'video-player' + index;
			this.players[videoId].play();
		},
		pauseAllVideos() {
			// 停止所有视频的播放
			Object.keys(this.players).forEach(videoId => {
				const player = this.players[videoId];
				if (player) {
					player.pause();
				}
			});
		},
		togglePlay() {
			const videoId = 'video-player' + this.currentIndex;
			const player = this.players[videoId];
			if (player) {
				if (player.paused) {
					player.play();
				} else {
					player.pause();
				}
				this.onVideoStateChange(!player.paused)
			}
		},
    playEpisode(id) {
      if(this.videoinfo.episodes[id].locked){
        this.dialogUnlock = true
      }else{
        this.$refs.mySwiper.$swiper.slideTo(id, 0, false)
        this.slideChange()
        this.closePopup()
      }
    },
		isAppleDevice() {
			const ua = navigator.userAgent.toLowerCase();
			return /iphone|ipad|phone|Mac/i.test(ua);
		},
		goBack() {

		},
	},
}
</script>
<style lang="scss" scoped>

.swiper {
	height: 100vh;
	height: calc(var(--vh, 1vh) * 100);
}

.swiper-slide {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100vh;
	height: calc(var(--vh, 1vh) * 100);
}
</style>

<style scoped>

.center-play-button {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 60px;
	height: 60px;
	background-color: rgba(0, 0, 0, 0.4);
	border-radius: 50%;
	display: flex;
	justify-content: center;
	align-items: center;
	cursor: pointer;
  z-index: 200;
}

.play-icon {
	width: 0;
	height: 0;
	border-style: solid;
	border-width: 15px 0 15px 26px;
	border-color: transparent transparent transparent #fff;
	margin-left: 5px;
}

.video-info{
	position: fixed; 
	bottom: 50px; 
	z-index: 100;           /* 添加 z-index 确保在其他元素上层 */
  /*
	background:linear-gradient(to bottom, 
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 0.7) 100%
  );   添加半透明背景提高可读性 */
}

.episode-list-container {
  max-height: 500px;
  overflow-y: auto;
}

.episode-btn-wrapper {
  position: relative;
}

.lock-icon {
  position: absolute;
  top: -12px;
  right: -12px;
  z-index: 1;
}
</style>