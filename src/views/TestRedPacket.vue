<template>
  <div class="test-page">
    <v-container>
      <v-row justify="center">
        <v-col cols="12" md="8">
          <v-card class="pa-5">
            <v-card-title class="text-center">
              红包雨性能测试页面
            </v-card-title>
            
            <!-- 性能监控信息 -->
            <v-card class="mb-4 pa-3" color="info" dark>
              <h4>性能监控</h4>
              <p>FPS: <span :class="fpsClass">{{ fps }}</span></p>
              <p>当前红包数量: {{ redPacketCount }}</p>
              <p>设备类型: {{ deviceType }}</p>
              <p>浏览器: {{ browserInfo }}</p>
              <p>屏幕尺寸: {{ screenSize }}</p>
              <p>优化模式: {{ optimizationMode }}</p>
            </v-card>
            
            <v-card-text class="text-center">
              <p>点击下方按钮测试红包雨效果的性能优化</p>
              <v-btn 
                color="primary" 
                large 
                @click="startRedPacketRain"
                :disabled="isRaining"
              >
                {{ isRaining ? '红包雨进行中...' : '开始红包雨测试' }}
              </v-btn>
              
              <v-btn 
                color="secondary" 
                class="ml-3"
                @click="stopRedPacketRain"
                :disabled="!isRaining"
              >
                停止测试
              </v-btn>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-container>

    <!-- 红包雨组件 -->
    <LuckyWheel 
      ref="luckyWheel"
      v-model="showLuckyWheel"
      @claim-red-packet="onClaimRedPacket"
    />
  </div>
</template>

<script>
import LuckyWheel from '@/components/LuckyWheel.vue'

export default {
  name: 'TestRedPacket',
  components: {
    LuckyWheel
  },
  data() {
    return {
      showLuckyWheel: false,
      isRaining: false,
      fps: 0,
      redPacketCount: 0,
      deviceType: '',
      browserInfo: '',
      screenSize: '',
      optimizationMode: '',
      fpsCounter: 0,
      lastTime: 0,
      monitoringId: null
    }
  },
  computed: {
    fpsClass() {
      if (this.fps >= 55) return 'fps-good'
      if (this.fps >= 40) return 'fps-medium'
      return 'fps-poor'
    }
  },
  mounted() {
    this.detectDevice()
    this.startFPSMonitoring()
  },
  beforeDestroy() {
    this.stopFPSMonitoring()
  },
  methods: {
    detectDevice() {
      const userAgent = navigator.userAgent
      const isIOS = /iPad|iPhone|iPod/.test(userAgent)
      const isAndroid = /Android/.test(userAgent)
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent) || window.innerWidth <= 768
      
      if (isIOS) {
        this.deviceType = 'iOS设备'
        this.optimizationMode = 'iOS优化模式 (最大8个红包)'
      } else if (isAndroid) {
        this.deviceType = 'Android设备'
        this.optimizationMode = 'Android优化模式 (最大12个红包)'
      } else if (isMobile) {
        this.deviceType = '其他移动设备'
        this.optimizationMode = '移动设备优化模式'
      } else {
        this.deviceType = '桌面设备'
        this.optimizationMode = '桌面模式 (最大25个红包)'
      }
      
      // 检测浏览器
      if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
        this.browserInfo = 'Safari'
      } else if (userAgent.includes('Chrome')) {
        this.browserInfo = 'Chrome'
      } else if (userAgent.includes('Firefox')) {
        this.browserInfo = 'Firefox'
      } else {
        this.browserInfo = '其他浏览器'
      }
      
      this.screenSize = `${window.innerWidth} x ${window.innerHeight}`
    },
    
    startFPSMonitoring() {
      const monitor = (currentTime) => {
        if (this.lastTime) {
          this.fpsCounter++
          const deltaTime = currentTime - this.lastTime
          
          // 每秒更新一次FPS
          if (deltaTime >= 1000) {
            this.fps = Math.round(this.fpsCounter * 1000 / deltaTime)
            this.fpsCounter = 0
            this.lastTime = currentTime
            
            // 更新红包数量
            if (this.$refs.luckyWheel && this.$refs.luckyWheel.redPackets) {
              this.redPacketCount = this.$refs.luckyWheel.redPackets.length
            }
          }
        } else {
          this.lastTime = currentTime
        }
        
        this.monitoringId = requestAnimationFrame(monitor)
      }
      
      this.monitoringId = requestAnimationFrame(monitor)
    },
    
    stopFPSMonitoring() {
      if (this.monitoringId) {
        cancelAnimationFrame(this.monitoringId)
        this.monitoringId = null
      }
    },
    
    startRedPacketRain() {
      this.isRaining = true
      this.showLuckyWheel = true
      
      // 等待组件渲染完成后触发红包雨
      this.$nextTick(() => {
        if (this.$refs.luckyWheel) {
          // 模拟红包抽奖活动触发
          this.$refs.luckyWheel.handleImageClick('redpacket')
        }
      })
    },
    
    stopRedPacketRain() {
      this.isRaining = false
      if (this.$refs.luckyWheel) {
        this.$refs.luckyWheel.stopRedPacketRain()
      }
      this.showLuckyWheel = false
      this.redPacketCount = 0
    },
    
    onClaimRedPacket(prize) {
      console.log('获得红包奖励:', prize)
      this.$snackbar.success(`恭喜获得 ${prize.displayAmount} 红包奖励！`)
      this.isRaining = false
    }
  }
}
</script>

<style scoped>
.test-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px 0;
}

/* FPS显示颜色 */
.fps-good {
  color: #4CAF50;
  font-weight: bold;
}

.fps-medium {
  color: #FF9800;
  font-weight: bold;
}

.fps-poor {
  color: #F44336;
  font-weight: bold;
}
</style> 