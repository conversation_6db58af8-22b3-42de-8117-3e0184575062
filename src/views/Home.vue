<template>
  <v-container>
    <swiper
      class="swiper"
      :options="swiperOption"
    >
      <swiper-slide
        v-for="(item, index) in topBanners"
        :key="'b_'+index"
      >
        <div
          v-if="item.id==12"
          @click="goose_open"
        >
          <img
            :src="item.banner"
            style="border-radius: 6px;width: 100%; height: auto;"
          >
        </div>
        <a
          v-else
          :title="item.title"
          :href="item.url?item.url:'javascript:;'"
          :target="(item.target>0)?'_blank':'_self'"
        >
          <img
            :src="item.banner"
            style="border-radius: 6px;width: 100%; height: auto;"
          >
        </a>
      </swiper-slide>
      <div
        slot="button-prev"
        class="swiper-button-prev swiper-navBtn"
      />
      <div
        slot="button-next"
        class="swiper-button-next swiper-navBtn"
      />

      <div
        slot="pagination"
        class="swiper-pagination mb-1"
      />
    </swiper>

    <v-sheet
      rounded="pill"
      color="box_bg"
    >
      <v-chip-group
        v-model="selchip"
        mandatory
        color="primary"
        class="mb-1 px-2 rounded-lg font-weight-medium"
      >
        <v-chip
          value="lobby"
          color="app_bg"
          @click="changechip('lobby')"
        >
          <v-icon
            left
          >
            mdi-home
          </v-icon>
          {{ $t('dating') }}
        </v-chip>
        <v-btn
          v-if="showchatbtn"
          rounded
          depressed
          color="app_bg"
          max-height="32"
          class="text-capitalize py-2 mt-1 mr-2"
          @click="gotochat"
        >
          <v-icon
            left
            color="teal"
          >
            mdi-chat-processing-outline
          </v-icon>
          {{ $t('Chat Room') }}
        </v-btn>
        <v-chip
          value="hot"
          color="app_bg"
          @click="changechip('hot')"
        >
          <v-icon
            left
            color="red"
          >
            mdi-fire
          </v-icon>
          {{ $t('rmyx') }}
        </v-chip>
        <v-chip
          value="pg"
          color="app_bg"
          @click="changechip('pg')"
        >
          <img
            width="24"
            class="mr-2"
            src="../assets/img/icons/pg.svg"
          >
          {{ $t('pgyx') }}
        </v-chip>
        <v-chip
          value="slots"
          color="app_bg"
          @click="changechip('slots')"
        >
          <v-icon
            left
            color="blue"
          >
            mdi-slot-machine
          </v-icon>
          {{ $t('slots') }}
        </v-chip>
        <v-chip
          value="skill"
          color="app_bg"
          @click="changechip('skill')"
        >
          <v-icon
            left
            color="red"
          >
            mdi-controller
          </v-icon>
          Skill
        </v-chip>
        <v-chip
          value="casino"
          color="app_bg"
          @click="changechip('casino')"
        >
          <v-icon
            left
            color="orange"
          >
            mdi-poker-chip
          </v-icon>
          {{ $t('zhenren') }}
        </v-chip>
        <v-chip
          value="fav"
          color="app_bg"
          to="/fav"
        >
          <v-icon
            left
            color="purple"
          >
            mdi-star-box
          </v-icon>
          {{ $t('scj') }}
        </v-chip>
      </v-chip-group>
    </v-sheet>

    <v-row
      dense
      class="my-1"
    >
      <v-col
        cols="4"
      >
        <v-card
          height="60"
          color="#6A1B9A"
          class="rounded-lg pa-2 pl-6 d-flex align-center justify-center bgimg_bc"
          to="/freespins"
        >
          <div class="font-weight-bold text-center">
            Free Spins
          </div>
        </v-card>
      </v-col>
      <v-col cols="4">
        <v-card
          height="60"
          color="#D74CAD"
          class="rounded-lg pa-2 pl-6 d-flex align-center justify-center bgimg_3h"
          to="/vip"
        >
          <div
            class="font-weight-bold text-center"
            style="line-height: 1.1;"
          >
            3H Click Bonus
          </div>
        </v-card>
      </v-col>
      <v-col
        cols="4"
      >
        <v-card
          height="60"
          color="red"
          class="rounded-lg pa-2 pl-6 d-flex align-center justify-center bgimg_wheel"
          @click="goose_open"
        >
          <div
            class="font-weight-bold text-center"
            style="line-height: 1.1;"
          >
            Lucky Mission Draw
          </div>
        </v-card>
      </v-col>

      <v-col cols="4">
        <v-card
          dark
          height="60"
          color="#0766ff"
          class="rounded-lg pa-2 pe-0 text-caption font-weight-medium overflow-x-visible d-flex align-center"
          to="/invite?chip=activity"
        >
          <div class="d-flex align-center">
            <div class="float-left">
              <v-icon>mdi-facebook</v-icon>
            </div>
            <div
              class="pl-1"
              style="line-height: 1rem !important;"
            >
              FB Post Reward
              <strong class="yellow--text ml-1">₱25</strong>
            </div>
          </div>
        </v-card>
      </v-col>
      <v-col cols="4">
        <v-card
          dark
          height="60"
          color="#25d366"
          class="rounded-lg pa-2 pe-0 text-caption font-weight-medium d-flex align-center"
          to="/whatsapp"
        >
          <div class="d-flex align-center">
            <div class="float-left">
              <v-icon>mdi-whatsapp</v-icon>
            </div>
            <div
              class="pl-1"
              style="line-height: 1rem !important;"
            >
              Whatsapp Share
              <strong class="yellow--text ml-1">₱20</strong>
            </div>
          </div>
        </v-card>
      </v-col>
      <v-col cols="4">
        <v-card
          dark
          height="60"
          class="rounded-lg pa-2 pe-0 text-caption font-weight-medium d-flex align-center"
          style="background: linear-gradient(rgb(62, 185, 231), rgb(27, 130, 170));"
          to="/code"
        >
          <div class="d-flex align-center">
            <div class="float-left">
              <v-icon style="transform: rotate(-45deg);">
                mdi-send-circle
              </v-icon>
            </div>
            <div
              class="pl-1"
              style="line-height: 1rem !important;"
            >
              Join Telegram
              <strong class="yellow--text ml-1">₱777</strong>
            </div>
          </div>
        </v-card>
      </v-col>

      <v-col cols="12">
        <v-chip
          outlined
          color="btn_bg"
          class="d-block"
        >
          <div class="announcement-container">
            <div class="announcement-content">
              <v-btn
                v-if="announcement"
                text
                color="primary"
                class="text-capitalize mx-2"
              >
                {{ announcement }}
              </v-btn>
              <v-btn
                text
                color="primary"
                class="text-capitalize mx-2"
                to="cashback"
              >
                🎁 {{ $t('marquee_t2') }}
              </v-btn>
            </div>
          </div>
        </v-chip>
      </v-col>
    </v-row>

    <!--
    <div class="text-center">
      <v-chip
        color="#0766ff"
        text-color="white"
        class="mt-3 mx-2"
        to="/invite?chip=activity"
      >
        <v-icon left>
          mdi-facebook
        </v-icon>
        {{ $t('post_ad1') }}
        <strong class="yellow--text mx-1">+ ₱ 2.5</strong>
      </v-chip>
      <v-chip
        v-if="tgbonus_show"
        color="#25d366"
        text-color="white"
        class="mt-3 mx-2"
        to="/whatsapp"
      >
        <v-icon left>
          mdi-whatsapp
        </v-icon>

        {{ $t('post_tg') }}
        <strong class="yellow--text mx-1">+ ₱ 6</strong>
      </v-chip>

      <v-chip
        v-if="tgbonus_show"
        text-color="white"
        class="mt-3 mx-2"
        style="background: linear-gradient(103deg, #25D366 10%,#1CAFEE 65%,#EC12FF 100%);"
        to="/upvideo"
      >
        {{ $t('upvideo') }}
      </v-chip>
    </div>
    -->

    <template v-if="selchip=='lobby'">
      <HomeLobby @changechip="changechip" />
    </template>
    <template v-if="selchip=='hot'">
      <HomeHot />
    </template>
    <template v-if="selchip=='pg'">
      <HomeP :platform="pg_platform" />
    </template>
    <template v-if="selchip=='slots'">
      <HomeSlots />
    </template>
    <template v-if="selchip=='casino'">
      <HomeCasino />
    </template>
    <template v-if="selchip=='skill'">
      <HomeSkill />
    </template>
    <template v-if="showdialog">
      <Login
        v-model="loginreg"
        @closeloginreg="closeloginreg"
        @showforget="showforget"
      />
    </template>

    <template v-if="forget">
      <Forget
        @closeforget="closeforget"
      />
    </template>
    
    <template v-if="goose">
      <Eggs
        @goose_close="goose_close"
      />
    </template>

    <template v-if="showdialogprofile">
      <Profile
        v-model="profiletype"
        @closeprofile="closeprofile"
      />
    </template>

    <template v-if="marquee_details">
      <Firstd
        @firstd_close="firstd_close"
      />
    </template>

    <v-btn
      v-if="!goose"
      v-show="goose_btn"
      fixed
      small
      bottom
      left
      fab
      depressed
      dark
      color="transparent"
      class="animat-swing"
      style="bottom: 90px;"
      @click="goose_open"
    >
      <v-img
        width="80"
        class="rounded-circle"
        src="../assets/img/icons/wheel.gif"
      />
    </v-btn>

    <v-btn
      v-if="count_down_btn"
      fixed
      bottom
      left
      fab
      depressed
      dark
      color="transparent"
      style="bottom: 120px; left: 30px;"
      @click.stop="count_down = true"
    >
      <v-img
        width="85"
        class="d-flex align-center"
        src="../assets/img/First_deposit.png"
      >
        <div class="text-h4 font-weight-black pt-1">
          {{ second }}
        </div>
      </v-img>
    </v-btn>

    <v-dialog
      v-model="count_down"
      max-width="400"
      content-class="rounded-xl"
    >
      <v-btn
        icon
        dark
        fixed
        right
        class="mt-n11 mr-5 opacity-6"
        @click.stop="count_down = false"
      >
        <v-icon large>
          mdi-close-circle
        </v-icon>
      </v-btn>

      <v-card
        flat
        dark
        class="pa-4 rounded-xl overflow-visible"
        style="background-image: linear-gradient(225deg, #47C74C 0%, #5356FB 100%);"
      >
        <div
          class="text-center mx-auto"
          style="width: 90%;"
        >
          <h3 class="text-h4 font-weight-black mb-3">
            <span class="yellow--text">First</span> deposit
          </h3>
          <div
            class=" rounded-xl py-2 black--text"
            style="
              background-image: linear-gradient(to top, #abff80 0%, #FBF783 100%);
              border: 2px solid yellow;
            "
          >
            <span class="mr-2 font-weight-black">{{ hour }}</span>
            <v-chip
              label
              small
              color="green"
            >
              H
            </v-chip>
            <span class="mx-2 font-weight-black">{{ minute }}</span>
            <v-chip
              label
              small
              color="green"
            >
              Min
            </v-chip>
            <span class="mx-2 font-weight-black">{{ second }}</span>
            <v-chip
              label
              small
              color="green"
            >
              S'
            </v-chip>
          </div>
          <div
            class="rounded-xl my-3"
            style="border: 2px solid yellow;"
          >
            <v-simple-table class="rounded-xl">
              <tbody>
                <tr>
                  <td class="opacity-6 text-caption">
                    Deposits
                  </td>
                  <td class="opacity-6 text-caption">
                    Bonus
                  </td>
                  <td class="opacity-6 text-caption">
                    Free Spins
                  </td>
                </tr>
                <tr>
                  <td>
                    <span class="opacity-6 mr-1">₱</span>
                    <strong>100</strong>
                  </td>
                  <td>
                    <span class="opacity-6 mr-1">₱</span>
                    <strong class="yellow--text">50</strong>
                  </td>
                  <td>
                    <strong class="yellow--text">1</strong>
                    Free
                  </td>
                </tr>
                <tr>
                  <td>
                    <span class="opacity-6 mr-1">₱</span>
                    <strong>500</strong>
                  </td>
                  <td>
                    <span class="opacity-6 mr-1">₱</span>
                    <strong class="yellow--text">250</strong>
                  </td>
                  <td>
                    <strong class="yellow--text">5 </strong>
                    Free
                  </td>
                </tr>
                <tr>
                  <td>
                    <span class="opacity-6 mr-1">₱</span>
                    <strong>1000</strong>
                  </td>
                  <td>
                    <span class="opacity-6 mr-1">₱</span>
                    <strong class="yellow--text">500</strong>
                  </td>
                  <td>
                    <strong class="yellow--text">12</strong>
                    Free
                  </td>
                </tr>
              </tbody>
            </v-simple-table>
          </div>

          <v-btn
            rounded
            block
            large
            elevation="5"
            color="primary"
            style="background-image: linear-gradient(to top, #438224 0%, #ebe300 100%);"
            @click="showtab_deposit"
          >
            Deposit Now
          </v-btn>

          <v-divider class="mt-8" />
          <div class="text-left text-caption opacity-6">
            <v-subheader>Activity Description</v-subheader>
            <ol>
              <li>
                You can only participate in this activity once.
              </li>
              <li>
                The first recharge made before the countdown ends will receive the corresponding bonuses.
              </li>
              <li>
                The company reserves the right to make the final interpretation of this activity.
              </li>
            </ol>
          </div>
        </div>
      </v-card>
    </v-dialog>
    <template v-if="showdialogchat">
      <Chat
        v-model="token"
        @closechat="closechat"
      />
    </template>
  </v-container>
</template>


<script>
import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
import 'swiper/css/swiper.css'
import {store2} from '../store2.js'
import store from "../store/"
import HomeLobby from '@/views/pages/Home_lobby.vue'
import HomeHot from '@/views/pages/Home_hot.vue'
import HomeP from '@/views/pages/Home_p.vue'
import HomeSlots from '@/views/pages/Home_slots.vue'
import HomeCasino from '@/views/pages/Home_casino.vue'
import HomeSkill from '@/views/pages/Home_skill.vue'
import Eggs from '@/components/Eggs.vue'
import Firstd from '@/components/Firstd.vue'
import Chat from '@/components/Chat.vue'
import Login from '@/components/Login.vue'
import Forget from '@/components/Forget.vue'
import Profile from '@/components/Profile.vue'
import { Crisp } from "crisp-sdk-web"
export default {
  name: 'Home',
  components: {
    Swiper,
    SwiperSlide,
    HomeLobby,HomeHot,HomeP,HomeSlots,HomeCasino,HomeSkill,
    Eggs,Firstd,
    Login,
    Forget,
    Profile,
    Chat
  },
  data: () => ({
    topBanners: [],
    selchip: 'lobby',
    pg_platform: 4,
    marquee_details: false,
    swiperOption: {
      slidesPerView: 1.2,
      centeredSlides: true,
      loop: true,
      spaceBetween: 8,
      fade: true,
      grabCursor: true,
      autoplay: {
        delay: 2500,
        disableOnInteraction: false,
      },
      breakpoints: {
        640: {
          slidesPerView: 3,
          spaceBetween: 16,
          centeredSlides: false
        }
      },
      pagination: {
        el: '.swiper-pagination',
        clickable: true
      },
      navigation: {
        nextEl: '.swiper-button-next',
        prevEl: '.swiper-button-prev'
      }
    },
    ws: null,
    wstimer: null,
    countdowntimer: null,
    allSecond: 86400*2,
    hour: 0,
    minute: 0,
    second: 0,
    tgbonus_show: false,
    goose_btn: false,
    goose: false,
    count_down_btn: false,
    count_down: false,
    firstdgift: {
      money: 0
    },
    forget: false,
    showdialog: false,
    showdialogprofile: false,
    showchatbtn: false,
    showdialogchat: false,
    announcement: ''
  }),
  computed: {
    token() {
      return this.getStorage('token')
    },
  },
  watch: {

  },
  created() {
    console.log('webp_feature:'+this.$store.state.webp_feature)
    this.home_slide()
    //this.ws = new WebSocket("ws://*************:8282/?room=1")
    this.ws = new WebSocket("wss://wss.bonus.game/wss")
    this.ws.onopen = function() {}
    this.ws.onmessage = function(e) {
        //console.log("收到服务端的消息：" + e.data)
        store.commit('betlog', JSON.parse(e.data))
    }
  },
  mounted() {
    this.changechip('lobby')

    if(this.getStorage('member_id')){
      this.getmeminfo()
      this.tgbonus_show = true
      this.firstdepositgift()
    }else{
      this.count_down_btn = true
    }

    this.wstimer = setInterval(() => {
      this.ws.send(new Date().getTime())
    }, 30000)

    this.countdowntimer = setInterval(() => {
      this.hour = parseInt(this.allSecond / 3600)
      this.minute = parseInt(this.allSecond / 60 % 60)
      this.second = parseInt(this.allSecond % 60)
      this.allSecond--
      //console.log(this.hour, this.minute, this.second)
    }, 1000);
  },
  beforeDestroy() {
    this.ws.close()
    clearInterval(this.wstimer)
    clearInterval(this.countdowntimer)
  },
  methods: {
    getmeminfo() {
      let paramObj = {
        member_id: this.getStorage('member_id'),
      }
      this.$server.getmeminfo(paramObj).then((response) => {
        if(response.code==200){
          store.commit('balance', response.data.total_balance)
          if( response.data.deposited==1 ){
            this.goose_btn = true
            this.showchatbtn = true
          }else{
            this.count_down_btn = true
          }
        }
      })
    },
    home_slide() {
      this.$server.conf().then((response) => {
        this.announcement = response.data.announcement
      })
      let paramObj = {
        
      }
      this.$server.home_slide(paramObj).then((response) => {
        if(response.code==200){
          this.topBanners = response.data.ads
        }else{
          
        }
      })
    },
    changechip(selchip){
      this.selchip = selchip
    },
    goose_open() {
      //this.goose = true
      store.commit('luckywheel', true)
    },
    goose_close() {
      this.goose = false
    },
    firstd_open() {
      this.marquee_details = true
    },
    firstd_close() {
      this.marquee_details = false
    },
    firstdepositgift() {
      let paramObj = {
        member_id: this.getStorage('member_id')??0
      }
      this.$server.firstdepositgift(paramObj).then((response) => {
        if(response.code==200){
          this.firstdgift = response.data
        }else{
          
        }
      })
    },
    showloginreg(tab) {
      this.loginreg = tab
      this.showdialog = true
    },
    closeloginreg() {
      this.loginreg = false
      this.showdialog = false
      this.getmeminfo()
    },
    showforget() {
      this.forget = true
    },
    closeforget() {
      this.forget = false
    },
    showprofile(type) {
      this.profiletype = type
      this.showdialogprofile = true
    },
    closeprofile() {
      this.profiletype = false
      this.showdialogprofile = false
    },
    showtab_deposit() {
      if(this.getStorage('member_id')){
        store2.wallet_currtab = 'deposit'
        this.showprofile('wallet')
      }else{
        this.showloginreg('reg')
      }
    },
    gotochat() {
      Crisp.chat.hide()
      this.showdialogchat = true
    },
    closechat() {
      Crisp.chat.show()
      this.showdialogchat = false
    }
  }

}
</script>

<style>
.bgimg_bc {
    background: url('../assets/img/icons/coin-dollar.png') -45px center no-repeat;
}
.bgimg_goose {
    background: url('../assets/img/icons/goose.png') -50% center / 50% no-repeat;
}
.bgimg_wheel {
    background: url('../assets/img/icons/wheel.gif') -50% center / 50% no-repeat;
}
.bgimg_3h {
    background: url('../assets/img/icons/coins.svg') -50% center / 50% no-repeat;
}

.announcement-container {
  width: 100%;
  overflow: hidden;
  position: relative;
}

.announcement-content {
  display: inline-flex;
  white-space: nowrap;
  animation: scroll-announcement 15s linear infinite;
  padding-left: 100%;
}

@keyframes scroll-announcement {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-100%);
  }
}

/* 添加媒体查询针对iOS设备优化动画 */
@supports (-webkit-overflow-scrolling: touch) {
  .announcement-content {
    animation-duration: 20s; /* 为iOS设备减慢速度提高流畅度 */
    will-change: transform; /* 启用硬件加速 */
  }
}
</style>