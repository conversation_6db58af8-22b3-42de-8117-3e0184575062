<template>
  <v-container>
    <div class="text-center">
      <v-card
        color="box_bg"
      >
        <v-card-text
          class="text-center pt-3 pb-0"
        >
          <div class="d-flex align-center justify-center">
            <span class="text-h6">Coupon Code</span>
          </div>
        </v-card-text>

        <v-card-text>
          <v-text-field
            v-model="redemptionCode"
            flat
            solo
            background-color="nav_bg"
            prepend-inner-icon="mdi-ticket-confirmation"
            placeholder="What's today's code?"
            hide-details="auto"
            class="mb-2 redemption-input"
            :error-messages="errorMessage"
          >
            <template v-slot:append>
              <v-btn
                small
                color="primary"
                class="check-btn"
                :loading="loading"
                @click="checkRedeemQualification"
              >
                {{ $t('Check') }}
              </v-btn>
            </template>
          </v-text-field>

          <div class="text-center mt-6">
            <div>Access Our Official Telegram channel</div>
            <div>Get Redemption Code</div>
            <div>
              Up to 
              <span class="text-h6 orange--text text--lighten-2">₱777</span>
            </div>
            <v-btn
              block
              rounded
              class="mt-3"
              style="background: linear-gradient(rgb(62, 185, 231), rgb(27, 130, 170));"
              @click="openTelegramChannel"
            >
              <v-icon
                class="rotate-icon"
                left
                size="28"
              >
                mdi-send-circle
              </v-icon>
              JOIN
            </v-btn>
          </div>
        </v-card-text>

        <v-divider class="opacity-3 my-2" />
        
        <v-card-title class="py-3 text-body-1">
          {{ $t('Instructions') }}
        </v-card-title>

        <v-card-text class="text-left">
          <ul class="pl-4">
            <li><strong>The Redeem Codes on the Telegram channel!</strong></li>
            <li>Enter the code to get a prize</li>
            <li>If you have not made a deposit today, you may not be able to redeem the code</li>
            <li>Each code is unique and each user can use it only once</li>
            <li>If the codes run out, stay tuned to the messages for more</li>
            <li><strong>Codes will be distributed 3 times a day (morning/afternoon/evening) on Saturdays and Sundays, and Once daily Monday to Friday.</strong></li>
          </ul>
        </v-card-text>
      </v-card>
    </div>
    
    <!-- 红包对话框 -->
    <v-dialog
      v-model="showRedPacket"
      max-width="320"
      persistent
      content-class="red-packet-dialog"
    >
      <div class="red-packet-container">
        <div class="red-packet">
          <div class="red-packet-content">
            <!-- 红包顶部 Logo 区域 -->
            <div class="red-packet-logo">
              <v-icon size="30">
                mdi-gift
              </v-icon>
            </div>
            
            <!-- 红包文案区域 -->
            <div class="red-packet-message">
              Congratulations
            </div>
          </div>
          
          <!-- 红包底部封口 -->
          <div
            class="red-packet-seal"
            @click="claimReward"
          />
        </div>
        
        <!-- 底部关闭按钮 -->
        <v-btn
          fab
          small
          color="white"
          elevation="0"
          class="bottom-close-btn mt-6"
          @click="closeRedPacket"
        >
          <v-icon color="black">
            mdi-close
          </v-icon>
        </v-btn>
      </div>
    </v-dialog>
    
    <v-snackbar
      v-model="showSuccessSnackbar"
      color="success"
      timeout="3000"
      top
    >
      {{ successMessage }}
    </v-snackbar>
    
    <template v-if="showdialog">
      <Login
        v-model="loginreg"
        @closeloginreg="closeloginreg"
        @showforget="showforget"
      />
    </template>
    <template v-if="forget">
      <Forget
        @closeforget="closeforget"
      />
    </template>
  </v-container>
</template>

<script>
import Login from '@/components/Login.vue'
import Forget from '@/components/Forget.vue'

export default {
  components: {
    Login,
    Forget
  },
  data: () => ({
    redemptionCode: '',
    errorMessage: '',
    successMessage: '',
    showSuccessSnackbar: false,
    showHelpText: false,
    loading: false,
    showdialog: false,
    loginreg: false,
    forget: false,
    codeInfo: null,
    // 红包相关状态
    showRedPacket: false,
    redPacketOpened: false,
    rewardAmount: 0,
  }),
  computed: {
    allRequirementsMet() {
      if (!this.codeInfo || !this.codeInfo.requirements) return false;
      return this.codeInfo.requirements.every(req => req.fulfilled);
    }
  },
  watch: {
    redemptionCode() {
      // 用户输入新内容时清除错误信息
      this.errorMessage = '';
    },
    showRedPacket(val) {
      // 当红包关闭时，重置红包状态
      if (!val) {
        setTimeout(() => {
          this.redPacketOpened = false;
        }, 300);
      }
    }
  },
  created() {
    
  },
  mounted() {
    
  },
  methods: {
    toggleHelpText() {
      this.showHelpText = !this.showHelpText;
    },
    
    // 检查兑换码是否存在并获取兑换条件
    checkRedemptionCode() {
      // 检查用户是否登录
      if(!this.getStorage('member_id')){
        this.showloginreg('login')
        return false
      }
      
      // 验证兑换码不为空
      if (!this.redemptionCode.trim()) {
        this.errorMessage = 'Please enter the redemption code'
        return false
      }
      
      this.loading = true

      let paramObj = {
        member_id: this.getStorage('member_id'),
        code: this.redemptionCode.trim()
      }
      this.$server.check_couponcode(paramObj).then((response) => {
        if(response.code==404){
          this.errorMessage = 'Redeem code does not exist. Please re-enter or join Telegram channel to get more redeem codes';
          this.codeInfo = null;
        }
        if(response.code==405){
          this.errorMessage = 'Redemption code has expired';
          this.codeInfo = null;
        }
        if(response.code==406){
          this.errorMessage = 'Redemption code has been used up';
          this.codeInfo = null;
        }
        if(response.code==407){
          this.errorMessage = 'No deposit today. Redemption unavailable';
          this.codeInfo = null;
        }
        if(response.code==408){
          this.errorMessage = 'Same-day bet should >= ₱'+response.data.bet_require;
          this.codeInfo = null;
        }
        if(response.code==409){
          this.errorMessage = 'Already received';
          this.codeInfo = null;
        }
        if(response.code==200){
          this.showRedPacket = true;
        }
        this.loading = false; // 移到这里确保API请求完成后才设置loading状态
      }).catch(error => {
        console.error('Error checking coupon code:', error);
        this.errorMessage = 'Network error, please try again';
        this.loading = false; // 错误处理中也需要设置loading状态
      });
      
      return true
    },
    
    // 检查用户是否满足兑换资格
    checkRedeemQualification() {
      // 如果还没有获取过兑换码信息，先获取
      if (!this.codeInfo) {
        if (!this.checkRedemptionCode()) return;
        
        // 由于checkRedemptionCode是异步的，需要在回调中继续执行
        setTimeout(() => {
          if (this.codeInfo) this.checkRequirements();
        }, 1200);
        
        return;
      }
      
      this.checkRequirements();
    },
    
    // 检查兑换条件是否满足
    checkRequirements() {
      if (!this.allRequirementsMet) {
        // 找出未满足的条件
        const unfulfilled = this.codeInfo.requirements.filter(req => !req.fulfilled);
        const requirementText = unfulfilled.map(req => req.description).join('、');
        
        this.errorMessage = `Unsatisfied redemption conditions: ${requirementText}`;
        return;
      }
      
      // 所有条件都满足，显示红包
      this.showRedPacket = true;
    },
    
    // 打开红包
    openRedPacket() {
      if (!this.redPacketOpened) {
        this.redPacketOpened = true;
      }
    },
    
    // 关闭红包
    closeRedPacket() {
      this.showRedPacket = false;
    },
    
    // 领取奖励
    claimReward() {
      this.loading = true;
      this.showRedPacket = false;
      let paramObj = {
        member_id: this.getStorage('member_id'),
        code: this.redemptionCode.trim()
      }
      this.$server.receive_coupon(paramObj).then((response) => {
        if(response.code==200){
          this.rewardAmount = response.data.amount
          this.successMessage = response.data.message || `Congratulations, you have successfully received ₱${this.rewardAmount} reward！`;
          this.showSuccessSnackbar = true;
          this.redemptionCode = '';
          this.codeInfo = null;
        } else {
          this.errorMessage = response.msg || 'Failed to collect';
        }
        this.loading = false;
      }).catch(error => {
        this.errorMessage = 'Network error, please try again later';
        this.loading = false;
      })
    },
    
    showloginreg(tab) {
      this.loginreg = tab;
      this.showdialog = true;
    },
    closeloginreg() {
      this.loginreg = false;
      this.showdialog = false;
    },
    showforget() {
      this.forget = true;
    },
    closeforget() {
      this.forget = false;
    },
    // 添加Telegram频道打开方法
    openTelegramChannel() {
      // 打开Telegram频道链接
      window.open('https://t.me/moneycomingph', '_blank');
    },
  }
}
</script>

<style scoped>
.check-btn {
  margin-right: 4px;
  text-transform: none;
  height: 32px !important;
  min-width: 70px;
  z-index: 2; /* 确保按钮始终在最上层 */
}

.redemption-input >>> .v-input__append-inner {
  display: flex !important;
  margin-top: 0 !important;
  position: relative;
  z-index: 3;
}

.redemption-input >>> .v-input__slot:focus-within {
  padding-right: 80px; /* 为按钮预留足够空间 */
}

.rotate-icon {
  transform: rotate(-45deg);
}

.red-packet-dialog {
  background-color: transparent !important;
  box-shadow: none !important;
}

.red-packet-container {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.red-packet {
  position: relative;
  width: 280px;
  height: 350px;
  background: #f44336;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
  overflow: hidden;
  padding-bottom: 40px;
  border-radius: 12px;
}

.red-packet-content {
  padding: 20px;
  background: linear-gradient(135deg, #f44336, #d32f2f);
  border-radius: 12px 12px 50% 50% / 12px 12px 20% 20%;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 260px;
}

.red-packet-logo {
  margin-top: 20px;
  margin-bottom: 10px;
}

.red-packet-message {
  color: white;
  font-size: 20px;
}

.red-packet-amount {
  color: #fdd835;
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 30px;
}

.red-packet-amount span {
  font-size: 56px;
}

.red-packet-seal {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: radial-gradient(circle, #fdd835, #f9a825);
  border: 2px solid #ffb300;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  margin-bottom: 50px;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.red-packet-seal:hover {
  transform: translateX(-50%) scale(1.05);
  box-shadow: 0 6px 10px rgba(0, 0, 0, 0.3);
}

.red-packet-seal:active {
  transform: translateX(-50%) scale(0.95);
}

.red-packet-seal::after {
  content: 'Open';
  color: #5d4037;
  font-size: 24px;
  font-weight: bold;
}

.bottom-close-btn {
  margin-top: 20px;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.bottom-close-btn:hover {
  opacity: 1;
}

/* 红包装饰效果 */
.red-packet-content::before {
  content: '';
  position: absolute;
  top: 10px;
  left: 10px;
  right: 10px;
  bottom: 10px;
  border: 1px dashed rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  pointer-events: none;
}
</style>