<template>
  <div>
    <v-dialog
      v-model="showdialoggame"
      fullscreen
      hide-overlay
    >
      <v-card>
        <div class="full-minus-header">
          <iframe
            id="gameiframe"
            width="100%"
            height="100%"
            style="border: none;"
            :src="gameurl"
          />
        </div>
      </v-card>
      <v-dialog
        v-model="dialogalert"
        max-width="300"
      >
        <v-card>
          <v-card-title class="subtitle-2">
            🔥 Earn Rewards by Inviting Friends
          </v-card-title>
          <v-card-text class="text-center">
            <v-btn
              icon
              x-large
              color="#0766ff"
              href="https://www.facebook.com/sharer/sharer.php?u=https://www.moneycoming.ph/minigames"
              target="_blank"
              rel="noopener"
            >
              <v-icon>mdi-facebook</v-icon>
            </v-btn>
            <v-btn
              icon
              x-large
              color="#25d366"
              href="whatsapp://send?text=This game is so much fun! Drop everything and play with me—I've sent you the link! https://www.moneycoming.ph/minigames"
              target="_blank"
              rel="noopener"
            >
              <v-icon>mdi-whatsapp</v-icon>
            </v-btn>
            <v-btn
              icon
              x-large
              color="pink"
              href="https://www.instagram.com/create/story?text=This%20game%20is%20so%20much%20fun!%20Drop%20everything%20and%20play with me—I've sent you the link&url=https://www.moneycoming.ph/minigames"
              target="_blank"
              rel="noopener"
            >
              <v-icon>mdi-instagram</v-icon>
            </v-btn>
          </v-card-text>
          <v-card-actions>
            <v-spacer />
            <v-btn
              color="green darken-1"
              text
              :disabled="countdown > 0"
              @click="closealert"
            >
              {{ countdown > 0 ? `OK (${countdown}s)` : 'OK' }}
            </v-btn>
            <v-spacer />
          </v-card-actions>
        </v-card>
      </v-dialog>
    </v-dialog>
    <div 
      ref="draggableBtn" 
      class="draggable-btns pr-8"
      @touchstart.prevent="startDrag"
      @mousedown.prevent="startDrag"
    >
      <v-btn
        icon
        class="draggable-btn"
        @click.stop="closeh5game"
        @touchend.prevent.stop="closeh5game"
      >
        <v-icon>mdi-reply</v-icon>
      </v-btn>
    </div>
  </div>
</template>

<script>
export default {
  name: 'H5game',
  components: {},
  props: {
    value: {
      type: String, // 定义 props 的类型
      required: true
    }
  },
  data: () => ({
    showdialoggame: true,
    dialogalert: false,
    gameurl: '',
    countdown: 10,
    countdownTimer: null,
    dragTimer: null,
    isDragging: false,
    initialY: 0,
    initialTouch: null
  }),
  watch: {
    showdialoggame: function (nv, ov) {
      console.log(nv, ov);
    }
  },
  created() {
    console.log(this.value);
    this.gameurl = '/h5play/' + this.value + '/index.html';
  },
  mounted() {
    // 添加消息监听器
    window.addEventListener('message', this.handleMessage);
  },
  beforeDestroy() {
    // 组件销毁前移除监听器
    window.removeEventListener('message', this.handleMessage);
  },
  methods: {
    handleMessage(event) {
      console.log('Received message from iframe:', event.data);
      // 处理来自 iframe 的消息
      if (event.data.type === 'SHOW_SHARE_DIALOG') {
        this.dialogalert = true;
        this.startCountdown();
      }
    },
    closeh5game() {
      this.showdialoggame = false;
      this.$emit('closeh5game');
    },
    closealert() {
      const iframe = document.getElementById('gameiframe');
      iframe.contentWindow.postMessage(
        {
          type: 'TEST',
          data: {
            code: 1
          }
        },
        '*'
      );

      this.dialogalert = false;
      this.countdown = 10;
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer);
        this.countdownTimer = null;
      }
    },
    startCountdown() {
      this.countdown = 10;
      this.countdownTimer = setInterval(() => {
        if (this.countdown > 0) {
          this.countdown--;
        } else {
          clearInterval(this.countdownTimer);
          this.countdownTimer = null;
        }
      }, 1000);
    },
    startDrag(event) {
      // 如果点击的是按钮本身，不处理拖动
      if (event.target.closest('.v-btn')) {
        return;
      }
      
      // 阻止默认行为
      event.preventDefault();
      event.stopPropagation();
      
      const touch = event.type.startsWith('touch') ? event.touches[0] : event;
      this.initialTouch = {
        clientY: touch.clientY,
        btnTop: this.$refs.draggableBtn.offsetTop
      };
      
      // 设置定时器，如果按住超过100ms才开始拖动
      this.dragTimer = setTimeout(() => {
        this.isDragging = true;
        this.addDragListeners();
      }, 100);

      if (event.type.startsWith('touch')) {
        document.addEventListener('touchend', this.checkQuickTap, { passive: false });
      } else {
        document.addEventListener('mouseup', this.checkQuickTap);
      }
    },

    checkQuickTap(event) {
      if (event) {
        event.preventDefault();
        event.stopPropagation();
      }
      
      clearTimeout(this.dragTimer);
      if (this.isDragging) {
        this.stopDrag();
      }
      
      document.removeEventListener('mouseup', this.checkQuickTap);
      document.removeEventListener('touchend', this.checkQuickTap);
    },

    addDragListeners() {
      document.addEventListener('mousemove', this.onDrag);
      document.addEventListener('touchmove', this.onDrag, { passive: false });
      document.addEventListener('mouseup', this.stopDrag);
      document.addEventListener('touchend', this.stopDrag);
    },

    removeDragListeners() {
      document.removeEventListener('mousemove', this.onDrag);
      document.removeEventListener('touchmove', this.onDrag);
      document.removeEventListener('mouseup', this.stopDrag);
      document.removeEventListener('touchend', this.stopDrag);
    },

    onDrag(event) {
      if (!this.isDragging || !this.initialTouch) return;
      
      event.preventDefault();
      const touch = event.type.startsWith('touch') ? event.touches[0] : event;
      const deltaY = touch.clientY - this.initialTouch.clientY;
      let newY = this.initialTouch.btnTop + deltaY;
      
      // 限制范围
      const btns = this.$refs.draggableBtn;
      const minY = 0;
      const maxY = window.innerHeight - btns.offsetHeight;
      newY = Math.max(minY, Math.min(newY, maxY));
      
      btns.style.top = `${newY}px`;
    },

    stopDrag() {
      clearTimeout(this.dragTimer);
      this.isDragging = false;
      this.initialTouch = null;
      this.removeDragListeners();
    }
  }
};
</script>

<style>
.full-minus-header {
  height: calc(100vh - 0px);
  overflow: auto;
}
.draggable-btns {
  position: fixed;
  left: 10px;
  top: 10%;
  z-index: 9999;
  display: flex;
  align-items: center;
  padding: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(5px);
  touch-action: none;
  user-select: none;
  -webkit-user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}
.draggable-btn {
  cursor: pointer;
}
</style>