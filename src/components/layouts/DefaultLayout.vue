<template>
  <v-app :style="{background: theme_background, color: $vuetify.theme.themes.dark.color}">
    <v-navigation-drawer
      v-model="drawer"
      app
      floating
      color="box_bg"
      height="100%"
      :width="drawer_width"
    >
      <v-toolbar
        width="100%"
        color="app_bg"
        class="logo"
        style="position: sticky; top: 0; z-index: 1;"
      >
        <v-toolbar-title>
          <router-link
            to="/home"
            class="d-flex align-center text--primary text-decoration-none"
          >
            <v-img
              alt="MoneyComing2025"
              class="shrink"
              contain
              :src="logoimg"
              transition="scale-transition"
              :max-width="logoimgw"
            />
            <!-- <span class="ml-2 mb-n2 font-weight-bold text--primary opacity-8">{{ domainarr[1] }}</span><span class="mb-n2 primary--text">{{ domainarr[0] }}</span> -->
          </router-link>
        </v-toolbar-title>
        <v-spacer />
        <v-btn
          icon
          class="d-flex d-sm-none"
          @click="drawer = false"
        >
          <v-icon>mdi-menu-open</v-icon>
        </v-btn>
      </v-toolbar>

      <v-container class="pb-0">
        <v-row
          dense
        >
          <v-col cols="12">
            <v-list-item
              dense
              light
              class="rounded-pill brown--text text-center box-no-bg"
              style="background: linear-gradient(56deg, rgb(149, 116, 45), rgb(245, 216, 138) 35%, rgb(193, 148, 53) 75%, rgb(241, 208, 131));"
              @click="openLuckyWheel"
            >
              <v-list-item-avatar
                width="30"
                height="30"
                tile
                class="mr-2 animat-breath"
              >
                <img
                  src="../../assets/img/icons/wheel.gif"
                >
              </v-list-item-avatar>
              <v-list-item-content>
                <v-list-item-title>Lucky Mission Draw</v-list-item-title>
              </v-list-item-content>
              <v-list-item-action>
                <v-icon>
                  mdi-chevron-right
                </v-icon>
              </v-list-item-action>
            </v-list-item>
          </v-col>

          <v-col cols="12">
            <v-list-item
              dense
              dark
              class="rounded-pill white--text text-center box-no-bg"
              style="background: linear-gradient(56deg, rgb(180 141 241), rgb(106 44 159) 34%, rgb(119 22 216) 67%, rgb(80 17 148));"
              to="/freespins"
            >
              <v-list-item-avatar
                size="30"
                tile
                class="mr-2"
              >
                <v-img src="../../assets/img/icons/slots.png" />
              </v-list-item-avatar>
              <v-list-item-content>
                <v-list-item-title>{{ $t('freespins_t1') }}</v-list-item-title>
              </v-list-item-content>
              <v-list-item-action>
                <v-icon>
                  mdi-chevron-right
                </v-icon>
              </v-list-item-action>
            </v-list-item>
          </v-col>

          <v-col cols="6">
            <v-badge
              v-model="badgeshow"
              dot
              overlap
              color="yellow"
              offset-x="20"
              offset-y="15"
              style="width: 100%;"
              label=""
            >
              <v-list-item
                dense
                dark
                class="rounded-pill white--text box-no-bg"
                style="background: linear-gradient(56deg, rgb(242 180 234), rgb(198 102 165) 34%, rgb(226 61 176) 67%, rgb(237 138 216));"
                to="/vip"
              >
                <v-list-item-avatar
                  size="30"
                  tile
                  class="mr-1"
                >
                  <img src="../../assets/img/icons/coins.svg">
                </v-list-item-avatar>

                <v-list-item-content>
                  <v-list-item-title>{{ $t('sxsjj') }}</v-list-item-title>
                </v-list-item-content>
              </v-list-item>
            </v-badge>
          </v-col>
          <v-col cols="6">
            <v-list-item
              dense
              dark
              class="rounded-pill white--text box-no-bg"
              style="background: linear-gradient(66deg, #fff430, #e9a929 34%, #ffc740 67%, #fff430);"
              to="/vip"
            >
              <v-list-item-avatar
                size="30"
                tile
                class="mr-1"
              >
                <v-img src="../../assets/img/icons/piggy-bank.svg" />
              </v-list-item-avatar>
              <v-list-item-content>
                <v-list-item-title>
                  {{ $t('cxg') }}
                </v-list-item-title>
              </v-list-item-content>
            </v-list-item>
          </v-col>
          <v-col cols="12">
            <v-list-item
              dense
              dark
              class="rounded-pill white--text text-center box-no-bg"
              style="background: linear-gradient(56deg, #52f377, #329f2c 34%, #2dd816 67%, #259411);"
              to="cashback"
            >
              <v-list-item-avatar
                size="30"
                tile
                class="mr-2"
              >
                <v-img src="../../assets/img/icons/money.svg" />
              </v-list-item-avatar>
              <v-list-item-content>
                <v-list-item-title>{{ $t('cashback_25') }}</v-list-item-title>
              </v-list-item-content>
              <v-list-item-action>
                <v-icon>
                  mdi-chevron-right
                </v-icon>
              </v-list-item-action>
            </v-list-item>
          </v-col>
          <v-col cols="12">
            <v-list-item
              dense
              dark
              class="rounded-pill white--text text-center box-no-bg"
              style="background: linear-gradient(56deg, rgb(183 247 255), rgb(47 181 208) 34%, rgb(98 216 228) 67%, #1ba1cc);"
              to="/vip"
            >
              <v-list-item-avatar
                size="30"
                tile
                class="mr-2"
              >
                <v-img src="../../assets/img/icons/money-bag.svg" />
              </v-list-item-avatar>
              <v-list-item-content>
                <v-list-item-title>{{ $t('wagering') }}</v-list-item-title>
              </v-list-item-content>
              <v-list-item-action>
                <v-icon>
                  mdi-chevron-right
                </v-icon>
              </v-list-item-action>
            </v-list-item>
          </v-col>
          <v-col cols="12">
            <v-list-item
              dense
              dark
              class="rounded-pill white--text text-center box-no-bg"
              style="background: linear-gradient(56deg, #5291f3, #3f56cf 34%, #4e9dff 67%, #1967ca);"
              to="/invite"
            >
              <v-list-item-avatar
                size="30"
                tile
                class="mr-2"
              >
                <v-img src="../../assets/img/icons/affiliate.svg" />
              </v-list-item-avatar>
              <v-list-item-content>
                <v-list-item-title>{{ $t('tgzq') }}</v-list-item-title>
              </v-list-item-content>
              <v-list-item-action>
                <v-icon>
                  mdi-chevron-right
                </v-icon>
              </v-list-item-action>
            </v-list-item>
          </v-col>
        </v-row>
      </v-container>

      <div class="pa-3">
        <v-list
          nav
          color="app_bg rounded"
        >
          <v-list-item
            link
            class="h-36"
            to="/hot"
          >
            <v-list-item-avatar
              tile
              size="24"
              class="my-0"
            >
              <v-icon>mdi-fire</v-icon>
            </v-list-item-avatar>
            <v-list-item-title>
              {{ $t('rmyx') }}
            </v-list-item-title>
          </v-list-item>
          <v-list-item
            link
            class="h-36"
            to="/p/4"
          >
            <v-list-item-avatar
              tile
              size="24"
              class="my-0"
            >
              <img
                width="24"
                class="pt-1"
                src="../../assets/img/icons/pg.svg"
              >
            </v-list-item-avatar>
            <v-list-item-title>
              {{ $t('pgyx') }}
            </v-list-item-title>
          </v-list-item>
          <v-list-item
            link
            class="h-36"
            to="/slots"
          >
            <v-list-item-avatar
              tile
              size="24"
              class="my-0"
            >
              <v-icon>mdi-slot-machine</v-icon>
            </v-list-item-avatar>
            <v-list-item-title>
              {{ $t('slots') }}
            </v-list-item-title>
          </v-list-item>

          <v-list-item
            link
            class="h-36"
            to="/fav"
          >
            <v-list-item-avatar
              tile
              size="24"
              class="my-0"
            >
              <v-icon>mdi-star-box</v-icon>
            </v-list-item-avatar>
            <v-list-item-title>
              {{ $t('scj') }}
            </v-list-item-title>
          </v-list-item>
        </v-list>
      </div>

      <div class="pa-3 pt-0">
        <v-list
          nav
          color="app_bg rounded"
        >
          <v-list-item
            link
            class="h-36"
            to="/promo"
          >
            <v-list-item-avatar
              tile
              size="24"
              class="my-0"
            >
              <v-icon>mdi-ticket-percent</v-icon>
            </v-list-item-avatar>
            <v-list-item-title>
              {{ $t('yhhd') }}
            </v-list-item-title>
          </v-list-item>
          <v-list-item
            link
            class="h-36"
            to="/invite"
          >
            <v-list-item-avatar
              tile
              size="24"
              class="my-0"
            >
              <v-icon>mdi-account-cash</v-icon>
            </v-list-item-avatar>
            <v-list-item-title>
              {{ $t('tgzq') }}
            </v-list-item-title>
          </v-list-item>
          <v-list-item
            link
            class="h-36"
            to="/vip"
          >
            <v-list-item-avatar
              tile
              size="24"
              class="my-0"
            >
              <v-icon>mdi-crown</v-icon>
            </v-list-item-avatar>
            <v-list-item-title>
              {{ $t('vipgb') }}
            </v-list-item-title>
          </v-list-item>
          <v-list-item
            v-if="!$store.state.is_pc"
            link
            class="h-36"
            to="/download"
          >
            <v-list-item-avatar
              tile
              size="24"
              class="my-0"
            >
              <v-icon>mdi-cellphone-arrow-down</v-icon>
            </v-list-item-avatar>
            <v-list-item-title>
              {{ $t('xiazai') }}
            </v-list-item-title>
          </v-list-item>
        </v-list>
      </div>

      <div class="pa-3 pt-0">
        <v-list
          nav
          color="app_bg rounded"
        >
          <!--
          <v-list-item
            link
            class="h-36"
            to="/help"
          >
            <v-list-item-avatar
              tile
              size="24"
              class="my-0"
            >
              <v-icon>mdi-frequently-asked-questions</v-icon>
            </v-list-item-avatar>
            <v-list-item-title>
              {{ $t('help') }}
            </v-list-item-title>
          </v-list-item>
        -->
          <v-list-item
            link
            class="h-36"
            to="/license"
          >
            <v-list-item-avatar
              tile
              size="24"
              class="my-0"
            >
              <v-icon>mdi-octagram</v-icon>
            </v-list-item-avatar>
            <v-list-item-title>
              {{ $t('license_nav') }}
            </v-list-item-title>
          </v-list-item>

          <!--
          <v-list-item
            v-if="!$store.state.is_pc"
            link
            class="h-36"
            to="/TG"
          >
            <v-list-item-avatar
              tile
              size="24"
              class="my-0 opacity-8"
            >
              <svg
                width="24"
                height="24"
                stroke-width="1.5"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              > <path
                d="M18 8L5 12.5L9.5 14M18 8L9.5 14M18 8L14 18.5L9.5 14"
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
              /> <path
                d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
              /> </svg>
            </v-list-item-avatar>
            <v-list-item-title>
              Official Channel
            </v-list-item-title>
          </v-list-item>
          -->
        </v-list>
      </div>
    </v-navigation-drawer>

    <v-system-bar
      v-if="showbar && showbar_conf==1"
      app
      light
      height="64"
      style="background-image: linear-gradient(to right, #43e97b 0%, #38f9d7 100%);"
    >
      <v-row align="center">
        <v-col class="flex-grow-0">
          <v-btn
            icon
            @click="showbar=false"
          >
            <v-icon class="mr-0">
              mdi-close
            </v-icon>
          </v-btn>
        </v-col>
        <v-col class="grow">
          <strong>Install APP</strong>
          <div class="text-caption">
            {{ $t('xiazai_tip_text') }}
          </div>
        </v-col>
        <v-col class="shrink">
          <v-btn
            v-if="ios"
            @click="iosios"
          >
            {{ $t('xiazai') }}
          </v-btn>
          <v-btn
            v-else
            :href="downloadurl"
            target="_blank"
          >
            {{ $t('xiazai') }}
          </v-btn>
        </v-col>
      </v-row>
    </v-system-bar>
    <v-app-bar
      app
      color="app_bg"
    >
      <v-app-bar-nav-icon
        @click.stop="changeDrawer"
      />
      
      <router-link
        to="/home"
        class="shrink text-decoration-none d-md-none"
      >
        <!--<v-img
          alt="Bonus.game"
          class="shrink"
          contain
          src="../../assets/logo.svg"
          transition="scale-transition"
          height="30"
          width="30"
        />-->
        <!-- <div class="mb-n2 font-weight-bold subtitle-2">MoneyComing</div>
        <div class="mb-n2 subtitle-2">2025</div> -->
        <v-img
          alt="Bonus.game"
          class="shrink"
          contain
          :src="logoimg"
          transition="scale-transition"
          :width="logoimgw"
        />
      </router-link>

      <v-spacer />

      <template v-if="meminfo.id>0">
        <v-tooltip 
          v-model="show_tip"
          bottom
          open-on-focus
          content-class="primary tooltip-bottom"
        >
          <template v-slot:activator="{ }">
            <v-chip
              color="nav_bg"
              @click="showtab_deposit"
            >
              <v-avatar
                left
                class="mr-1"
              >
                <v-img 
                  max-width="20"
                  max-height="20"
                  src="./../../assets/img/icons/PHP.png"
                />
              </v-avatar>
              <strong class="orange--text text--lighten-2">{{ balance }}</strong>
              <v-icon
                small
                right
                color="grey"
              >
                mdi-plus-circle-outline
              </v-icon>
            </v-chip>
          </template>
          <span>🎁 {{ meminfo.deposited==1?'Deposit':$t('free5') }}</span>
        </v-tooltip>

        <v-btn
          icon
          class="mx-md-5 mr-1"
          @click="showprofile('promo')"
        >
          <img
            height="36"
            class="mt-n2"
            src="../../assets/img/icons/bonus-casino-anim.svg"
          >
        </v-btn>

        <v-menu
          right
          offset-y
          max-width="350"
          origin="top right"
          transition="scale-transition"
        >
          <template v-slot:activator="{ on, attrs }">
            <v-btn
              icon
              small
              v-bind="attrs"
              class="mr-1 mr-md-3"
              v-on="on"
              @click="getmeminfo"
            >
              <v-avatar size="36">
                <v-img :src="require('../../assets/img/avatar/'+meminfo.avatar+'.png')" />
              </v-avatar>
            </v-btn>
          </template>
          <v-card>
            <v-list color="app_bg">
              <v-list-item three-line>
                <v-list-item-avatar
                  size="56"
                >
                  <v-img :src="require('../../assets/img/avatar/'+meminfo.avatar+'.png')" />
                </v-list-item-avatar>
                <v-list-item-content>
                  <div>
                    <v-chip
                      x-small
                      light
                      color="orange"
                      text-color="white"
                    >
                      <v-icon
                        left
                        small
                      >
                        mdi-crown
                      </v-icon>
                      VIP {{ meminfo.level }}
                    </v-chip>
                  </div>
                  <v-list-item-title class="mb-1">
                    {{ meminfo.area_code }} {{ meminfo.phone }}
                  </v-list-item-title>
                  <v-list-item-subtitle class="text--disabled">
                    UID: <span>{{ meminfo.id }}</span>
                    <v-btn
                      icon
                      x-small
                      @click="doCopy(''+meminfo.id)"
                    >
                      <v-icon x-small>
                        mdi-content-copy
                      </v-icon>
                    </v-btn>
                  </v-list-item-subtitle>
                </v-list-item-content>
                <v-list-item-icon>
                  <v-btn
                    icon
                    @click="showmy"
                  >
                    <v-icon>mdi-cog-outline</v-icon>
                  </v-btn>
                </v-list-item-icon>
              </v-list-item>
            </v-list>

            <v-card-text class="box_bg pt-0">
              <v-row>
                <v-col
                  cols="12"
                  class="pb-0"
                >
                  <v-list
                    dark
                    class="rounded-t-lg"
                    style="background: linear-gradient(56deg, rgb(255, 164, 130), rgb(223, 136, 255) 34%, rgb(155, 134, 255) 67%, rgb(170, 200, 255));"
                  >
                    <v-list-item
                      to="/vip"
                      class="box-no-bg"
                    >
                      <v-list-item-avatar>
                        <v-icon large>
                          mdi-crown
                        </v-icon>
                      </v-list-item-avatar>
                      <v-list-item-content>
                        <div class="d-flex align-center justify-space-between body-2">
                          <span class="white--text">VIP{{ myvipinfo.mylevel }}</span>
                          <small>{{ myvipinfo.upgrade_progress }}%</small>
                          <span class="white--text">VIP{{ myvipinfo.mylevel+1 }}</span>
                        </div>
                        <v-progress-linear
                          rounded
                          striped
                          :value="myvipinfo.upgrade_progress"
                          color="amber"
                          class="my-2"
                        />
                        <div class="d-flex align-center justify-space-between">
                          <small style="width: 60%;">{{ $t('j30tck') }}</small>
                          <small>
                            <span class="yellow--text">{{ myvipinfo.last30_deposit }}</span>
                            / {{ myvipinfo.upgrade_deposit }}
                          </small>
                        </div>
                      </v-list-item-content>
                    </v-list-item>
                  </v-list>
                </v-col>

                <v-col
                  cols="12"
                  class="pt-0"
                >
                  <v-list
                    dense
                    color="app_bg"
                    class="rounded-b-lg"
                  >
                    <v-list-item-group>
                      <v-list-item @click="showtab_deposit">
                        <v-list-item-action>
                          <v-icon>mdi-wallet-plus-outline</v-icon>
                        </v-list-item-action>
                        <v-list-item-content>
                          <v-list-item-title>{{ $t('cunkuan') }}</v-list-item-title>
                        </v-list-item-content>
                        <v-list-item-action>
                          <v-icon class="text--disabled">
                            mdi-chevron-right
                          </v-icon>
                        </v-list-item-action>
                      </v-list-item>
                      <v-list-item @click="showtab_withdraw">
                        <v-list-item-action>
                          <v-icon>mdi-cash-fast</v-icon>
                        </v-list-item-action>
                        <v-list-item-content>
                          <v-list-item-title>{{ $t('tikuan') }}</v-list-item-title>
                        </v-list-item-content>
                        <v-list-item-action>
                          <v-icon class="text--disabled">
                            mdi-chevron-right
                          </v-icon>
                        </v-list-item-action>
                      </v-list-item>
                      <v-list-item @click="showprofile('promo')">
                        <v-list-item-action>
                          <v-icon>mdi-gift-outline</v-icon>
                        </v-list-item-action>
                        <v-list-item-content>
                          <v-list-item-title>{{ $t('jjzx') }}</v-list-item-title>
                        </v-list-item-content>
                        <v-list-item-action>
                          <v-icon class="text--disabled">
                            mdi-chevron-right
                          </v-icon>
                        </v-list-item-action>
                      </v-list-item>
                      <v-list-item @click="showprofile('record')">
                        <v-list-item-action>
                          <v-icon>mdi-history</v-icon>
                        </v-list-item-action>
                        <v-list-item-content>
                          <v-list-item-title>{{ $t('lsjl') }}</v-list-item-title>
                        </v-list-item-content>
                        <v-list-item-action>
                          <v-icon class="text--disabled">
                            mdi-chevron-right
                          </v-icon>
                        </v-list-item-action>
                      </v-list-item>
                      <v-list-item @click="showmy">
                        <v-list-item-action>
                          <v-icon>mdi-account-cog-outline</v-icon>
                        </v-list-item-action>
                        <v-list-item-content>
                          <v-list-item-title>{{ $t('grsz') }}</v-list-item-title>
                        </v-list-item-content>
                        <v-list-item-action>
                          <v-icon class="text--disabled">
                            mdi-chevron-right
                          </v-icon>
                        </v-list-item-action>
                      </v-list-item>
                      
                      <v-divider class="opacity-3" />

                      <v-list-item
                        to="/download"
                      >
                        <v-list-item-action>
                          <v-icon>mdi-cellphone-arrow-down</v-icon>
                        </v-list-item-action>
                        <v-list-item-content>
                          <v-list-item-title>
                            {{ $t('xiazai') }}
                          </v-list-item-title>
                        </v-list-item-content>
                      </v-list-item>

                      <v-divider class="opacity-3" />

                      <v-list-item @click="logout">
                        <v-list-item-action>
                          <v-icon>mdi-logout-variant</v-icon>
                        </v-list-item-action>
                        <v-list-item-content>
                          <v-list-item-title class="red--text">
                            {{ $t('tuichu') }}
                          </v-list-item-title>
                        </v-list-item-content>
                      </v-list-item>
                    </v-list-item-group>
                  </v-list>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-menu>
      </template>
      <template v-else>
        <v-btn
          v-if="nologinbtn"
          text
          class="mx-1"
          @click="showloginreg('login')"
        >
          {{ $t('denglu') }}
        </v-btn>
        <v-btn
          v-if="nologinbtn"
          depressed
          color="primary"
          class="mx-1"
          @click="showloginreg('reg')"
        >
          {{ $t('zhuce') }}
        </v-btn>
      </template>
      
      <template v-if="showdialog">
        <Login
          v-model="loginreg"
          @closeloginreg="closeloginreg"
          @showforget="showforget"
        />
      </template>

      <template v-if="forget">
        <Forget
          @closeforget="closeforget"
        />
      </template>

      <template v-if="goose">
        <Eggs
          @goose_close="goose_close"
        />
      </template>

      <template v-if="showdialogprofile">
        <Profile
          v-model="profiletype"
          @closeprofile="closeprofile"
        />
      </template>
      
      <template v-if="showdialogmy">
        <My
          @closemy="closemy"
        />
      </template>
    </v-app-bar>

    <v-main
      id="hide-on-scroll-main"
      class="pb-16 pb-sm-0"
    >
      <router-view />
      <!-- <v-btn
        v-if="show777"
        fixed
        bottom
        right
        fab
        depressed
        dark
        style="z-index: 10; bottom: 285px; background-image: linear-gradient(to top, #0fd850 0%, #f9f047 100%);"
        @click="freebonus_claim = true"
      >
        <v-img
          width="54"
          class="rounded-circle"
          src="../../assets/img/icons/bonus777.png"
        />
      </v-btn> -->
      <!--
      <v-btn
        v-if="tgbonus_show"
        fixed
        bottom
        right
        fab
        depressed
        dark
        color="transparent"
        style="z-index: 10; bottom: 215px;"
        to="/whatsapp"
      >
        <v-img
          width="54"
          class="rounded-circle"
          src="../../assets/img/post_ad2.gif"
        />
      </v-btn>-->

      <v-btn
        fixed
        bottom
        right
        fab
        depressed
        dark
        color="transparent"
        style="z-index: 10; bottom: 145px;"
        @click="showtab_deposit"
      >
        <v-img
          width="54"
          class="rounded-circle"
          src="../../assets/img/deposit-btn.gif"
        />
      </v-btn>

      <v-footer
        color="transparent"
      >
        <v-container>
          <div class="text-center text--disabled">
            {{ new Date().getFullYear() }} © <span class="font-weight-bold text--primary opacity-5">{{ domainarr[1] }}</span><span class="primary--text">.{{ domainarr[0] }}</span>

            <div
              v-if="!$store.state.is_pc"
              class="mt-3"
            >
              <a
                href="https://t.me/moneycomingph"
                target="_blank"
              >
                <img
                  height="24px"
                  class="mx-2"
                  src="../../assets/img/logos/ico-telegram.svg"
                  alt="Official Channel"
                >
              </a>
              <img
                height="24px"
                class="mx-2"
                src="../../assets/img/logos/ico-facebook.svg"
                alt="Official Facebook"
                @click="fblogin"
              >
              <img
                height="24px"
                class="mx-2"
                src="../../assets/img/logos/ico-instagram.svg"
                alt="Official Channel"
              >
              <img
                height="24px"
                class="mx-2"
                src="../../assets/img/logos/ico-twitter.svg"
                alt="Official Channel"
              >
            </div>
            <div class="d-flex justify-center">
              <span
                class="pa-2"
                style="width: 170px;"
              >
                <v-select
                  v-model="lang"
                  :items="items"
                  dense
                  height="24"
                  hide-details
                  outlined
                  no-data-text=""
                  item-text="title"
                  item-value="lang"
                  flat
                  class="text-body-2"
                >
                  <template v-slot:selection="{ item }">
                    <v-list-item
                      dense
                      class="px-0"
                    >
                      <v-list-item-avatar
                        width="24"
                        min-width="24"
                        height="24"
                      >
                        <img
                          :src="require(`../../assets/img/icons/${item.image}`)"
                          alt="language"
                        >
                      </v-list-item-avatar>
                      <v-list-item-content>
                        <v-list-item-title>{{ item.title }}</v-list-item-title>
                      </v-list-item-content>
                    </v-list-item>
                  </template>
                  <template v-slot:item="{ item, on }">
                    <v-list-item
                      dense
                      v-on="on"
                    >
                      <v-list-item-avatar
                        width="24"
                        min-width="24"
                        height="24"
                      >
                        <img
                          :src="require(`../../assets/img/icons/${item.image}`)"
                          alt="language"
                        >
                      </v-list-item-avatar>
                      <v-list-item-content>
                        <v-list-item-title>{{ item.title }}</v-list-item-title>
                      </v-list-item-content>
                    </v-list-item>
                  </template>
                </v-select>
              </span>
              <span
                class="pa-2"
                style="width: 170px;"
              >
                <v-select
                  v-model="themetoggle"
                  :items="themes"
                  dense
                  height="24"
                  hide-details
                  outlined
                  no-data-text=""
                  flat
                  :disabled="theme_group!='A'"
                  class="text-body-2"
                >
                  <template v-slot:selection="{ item }">
                    <v-list-item
                      dense
                      class="px-0"
                    >
                      <v-list-item-avatar
                        width="24"
                        min-width="24"
                        height="24"
                      >
                        <v-icon>{{ item.icon }}</v-icon>
                      </v-list-item-avatar>
                      <v-list-item-content>
                        <v-list-item-title>{{ item.text }}</v-list-item-title>
                      </v-list-item-content>
                    </v-list-item>
                  </template>
                  <template v-slot:item="{ item, on }">
                    <v-list-item
                      dense
                      v-on="on"
                    >
                      <v-list-item-avatar
                        width="24"
                        min-width="24"
                        height="24"
                      >
                        <v-icon>{{ item.icon }}</v-icon>
                      </v-list-item-avatar>
                      <v-list-item-content>
                        <v-list-item-title>{{ item.text }}</v-list-item-title>
                      </v-list-item-content>
                    </v-list-item>
                  </template>
                </v-select>
              </span>
            </div>
          </div>

          <v-divider
            class="my-3 opacity-3"
          />

          <div class="d-flex justify-center flex-wrap opacity-2">
            <span
              class="d-flex align-center"
            >
              <img
                src="../../assets/img/icons/18+.svg"
                height="24px"
                class="mx-2"
                @click="openLuckyWheel"
              >
            </span>
            <span
              class="d-flex align-center"
            >
              <a
                href="https://www.gamblersanonymous.org/ga/"
                target="_blank"
                rel="noindex,nofollow"
              >
                <img
                  src="../../assets/img/icons/gambleaware.svg"
                  height="24px"
                  class="mx-2"
                >
              </a>
            </span>
            <span
              class="d-flex align-center"
            >
              <a
                href="https://www.gamcare.org.uk/"
                target="_blank"
                rel="noindex,nofollow"
              >
                <img
                  src="../../assets/img/icons/gamecare.svg"
                  height="24px"
                  class="mx-2"
                >
              </a>
            </span>
          </div>
        </v-container>
      </v-footer>
    </v-main>

    <v-bottom-navigation
      app
      grow
      height="65"
      :value="bottomvalue"
      color="primary"
      background-color="box_bg"
      class="d-flex d-sm-none"
    >
      <v-btn
        to="/Home"
      >
        <span>{{ $t('shouye') }}</span>
        <v-icon>mdi-home</v-icon>
      </v-btn>

      <v-btn
        to="/promo"
      >
        <span>{{ $t('youhui') }}</span>
        <v-icon>mdi-gift</v-icon>
      </v-btn>

      <v-btn
        color="active_bg"
        position="relative"
        @click="showtab_deposit"
      >
        <v-chip
          v-if="!meminfo.id || meminfo.id<=0"
          color="error"
          small
          class="white--text font-weight-bold pulse-animation"
          style="position: absolute; top: -15px; z-index: 1; min-width: 70px; text-align: center;"
        >
          +₱188
        </v-chip>
        <span>{{ $t('cunkuan') }}</span>
        <v-icon>mdi-wallet-plus</v-icon>
      </v-btn>

      <v-btn
        to="/invite"
      >
        <span>{{ $t('tuiguang') }}</span>
        <v-icon>mdi-account-cash</v-icon>
      </v-btn>

      <!-- <v-btn
        to="/help"
      >
        <span>{{ $t('kefu') }}</span>
        <v-icon>mdi-headset</v-icon>
      </v-btn> -->
      <v-btn
        to="/video"
      >
        <span>Free Movies</span>
        <v-icon>mdi-movie-open-play</v-icon>
      </v-btn>
    </v-bottom-navigation>

    <template>
      <v-dialog
        v-model="iosapp"
        width="400"
        content-class="dialog-end"
      >
        <v-sheet
          dark
          color="nav_bg"
          class="pa-5 box-down-arrow rounded-lg"
        >
          <div class="position-relative">
            <v-btn
              fab
              icon
              small
              absolute
              top
              right
              color="grey darken-2"
              class="mr-n8"
              @click="iosapp = false"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </div>

          <div class="text-body-2 text--disabled">
            <div>
              {{ $t('azdsjzmnr1') }}
              <v-icon
                color="blue"
                class="mx-1 mt-n2"
              >
                mdi-export-variant
              </v-icon>
            </div>
            <div class="mt-2">
              {{ $t('azdsjzmnr2') }}
              <v-icon
                color="blue"
                class="mx-1 mt-n2"
              >
                mdi-plus-box-outline
              </v-icon>
              <strong>{{ $t('azdsjzmnr3') }}</strong>
            </div>
          </div>
          <v-img
            class="mt-2"
            src="../../assets/img/pic/help_ios.webp"
          />
        </v-sheet>
      </v-dialog>

      <v-dialog
        v-model="iosapp_help"
        width="400"
      >
        <v-card
          color="box_bg"
        >
          <v-card-title
            dense
            class="text-body-1"
          >
            {{ $t('rhcz') }}?
            <v-spacer />
            <v-btn
              fab
              icon
              small
              color="grey darken-2"
              @click="iosapp_help = false"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-card-title>
          <v-card-text>
            <v-carousel
              hide-delimiters
              :continuous="false"
              height="100%"
            >
              <v-carousel-item>
                <img
                  width="100%"
                  src="../../assets/img/pic/help-ios01.jpg"
                >
              </v-carousel-item>
              <v-carousel-item>
                <img
                  width="100%"
                  src="../../assets/img/pic/help-ios02.jpg"
                >
              </v-carousel-item>
              <v-carousel-item>
                <img
                  width="100%"
                  src="../../assets/img/pic/help-ios03.jpg"
                >
              </v-carousel-item>
            </v-carousel>
          </v-card-text>
        </v-card>
      </v-dialog>

      <v-dialog
        v-model="safaritip"
        width="400"
      >
        <v-card>
          <v-card-title />
          <v-card-text>
            {{ $t('safaritips') }}
          </v-card-text>
          <v-card-actions>
            <v-spacer />
            <v-btn
              color="primary"
              @click="safaritip = false"
            >
              OK
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
    </template>

    <template>
      <!-- <v-dialog
        v-model="freebonus_claim"
        width="400"
        content-class="rounded-xl"
      >
        <div
          class="text-center mt-n12"
          style="position: absolute; left: 50%; transform: translate(-50%); max-width: 400px; z-index: 2;"
        >
          <img
            src="@/assets/img/free-bonus.webp"
            width="120"
          >
        </div>

        <v-card
          color="freebonus-bg"
        >
          <v-list
            light
            class="text-body-2 px-4 mt-8"
          >
            <v-list-item
              class="rounded-lg mb-3"
              style="background-color: rgba(255, 255, 255, .5)"
            >
              <v-list-item-avatar
                tile
                size="32"
              >
                <v-img src="../../assets/img/icons/upload.png" />
              </v-list-item-avatar>
              <v-list-item-content>
                {{ $t('upappimg') }}
                <div
                  v-if="screenshot==-1"
                  class="text-caption mt-1 red--text"
                >
                  {{ $t('shsb_cxsc') }}
                </div>
              </v-list-item-content>
              <v-list-item-action>
                <div class="text-center">
                  <div class="mb-1 orange--text font-weight-medium">
                    + ₱ 9.77
                  </div>

                  <v-btn
                    v-if="screenshot==null || screenshot<0"
                    depressed
                    small
                    dark
                    @click="freebonus_upimg = true"
                  >
                    {{ $t('shangchuan') }}
                  </v-btn>

                  <div
                    v-if="screenshot===0"
                    class="text-caption mt-1 orange--text"
                  >
                    {{ $t('daishenhe') }}
                  </div>

                  <v-btn
                    v-if="screenshot===1"
                    depressed
                    small
                    dark
                    color="primary"
                    @click="receivescreenshotaward"
                  >
                    {{ $t('lingqu') }}
                  </v-btn>

                  <div
                    v-if="screenshot===2"
                    class="text-caption mt-1 green--text"
                  >
                    {{ $t('yilingqu') }}
                  </div>
                </div>
              </v-list-item-action>
            </v-list-item>
            
            <v-list-item
              class="rounded-lg mb-3"
              style="background-color: rgba(255, 255, 255, 0.5)"
            >
              <v-list-item-avatar
                tile
                size="32"
              >
                <v-img src="../../assets/img/icons/gift.png" />
              </v-list-item-avatar>
              <v-list-item-content>
                {{ $t('wcCPFsz') }}
              </v-list-item-content>
              <v-list-item-action>
                <div class="text-center">
                  <div class="mb-1 orange--text font-weight-medium">
                    + ₱ 3
                  </div>

                  <v-btn
                    v-if="!cpfset && meminfo.cpf==null"
                    depressed
                    small
                    dark
                    @click="gocpf"
                  >
                    {{ $t('shezhi') }}
                  </v-btn>

                  <v-btn
                    v-if="!cpfset && meminfo.cpf!=null"
                    depressed
                    small
                    dark
                    color="primary"
                    @click="claimfreebonus = true"
                  >
                    {{ $t('lingqu') }}
                  </v-btn>

                  <div
                    v-if="cpfset"
                    class="text-caption mt-1 green--text"
                  >
                    {{ $t('yilingqu') }}
                  </div>
                </div>
              </v-list-item-action>
            </v-list-item>
          </v-list>
          <v-btn
            fab
            small
            fixed
            class="mt-3 btn-center opacity-7"
            @click="freebonus_claim = false"
          >
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card>
      </v-dialog> -->

      <v-dialog
        v-model="claimfreebonus"
        max-width="400"
        scrollable
      >
        <v-card class="box_bg">
          <v-card-title class="text-subtitle-1">
            {{ $t('mfzs') }}
          </v-card-title>
          <v-card-text class="text-caption">
            <div class="mb-2">
              <v-badge
                inline
                content="1"
              />
              {{ $t('ywccpfsz') }}
              <v-icon
                small
                color="primary"
              >
                mdi-check
              </v-icon>
            </div>
            <div class="mb-2">
              <v-badge
                inline
                content="2"
              />
              <a
                v-if="ios"
                class="blue--text font-weight-medium text-decoration-none"
                @click="iosapp = true"
              >
                {{ $t('upappimg_rule1') }} 👈
              </a>
              <a
                v-else
                :href="whatsappurl1"
                target="_blank"
                class="blue--text font-weight-medium text-decoration-none"
              >
                {{ $t('upappimg_rule1') }} 👈
              </a>
            </div>

            <div class="mb-2 d-flex justify-space-between">
              <v-badge
                inline
                content="3"
              />
              <span class="pl-1">
                {{ $t('xcnr') }}
              </span>
            </div>

            <div class="">
              <v-badge
                inline
                content="4"
              />

              <span class="orange--text">
                {{ $t('srwz') }}
              </span>
            </div>

            <div class="ml-8">
              {{ $t('qzxfsr') }}:<strong class="primary--text ml-1">Bonus.Game</strong>
              <v-text-field
                v-model="website"
                filled
                solo
                flat
                :hide-details="!websiteerror"
                :error-messages="websiteerror?$t('websiteerror'):''"
                dense
                clearable
                autofocus
                background-color="nav_bg"
                prefix="https://"
                class="grey--text text-body-2 mt-2"
              />
              <div class="mt-3 mb-1" />
              <v-text-field
                v-if="verify_email"
                v-model="email"
                filled
                solo
                flat
                dense
                :readonly="hasemail"
                background-color="nav_bg"
                hide-details
                :placeholder="$t('qsryx')"
                class="grey--text text-body-2 mb-2"
              />
              <v-row
                v-if="verify_email"
                dense
              >
                <v-col cols="7">
                  <v-text-field
                    v-model="emailcode"
                    filled
                    solo
                    flat
                    dense
                    background-color="nav_bg"
                    hide-details
                    :placeholder="$t('qsryzm')"
                    class="grey--text text-body-2 mt-1"
                  />
                </v-col>
                <v-col
                  cols="5"
                  class="d-flex align-center justify-center pb-0"
                >
                  <v-btn
                    outlined
                    color="grey"
                    :disabled="emailcodebtndisabled"
                    @click="sendemail"
                  >
                    {{ emailcodebtntext }}
                  </v-btn>
                </v-col>
              </v-row>
              <v-btn
                dark
                color="green darken-1"
                depressed
                block
                class="mt-4"
                :disabled="website==null"
                :loading="loading"
                @dblclick="dblclick"
                @click="cpfbonus"
              >
                {{ $t('lingqu') }}
              </v-btn>
            </div>
          </v-card-text>
        </v-card>
      </v-dialog>
      
      <v-dialog
        v-model="freebonus_upimg"
        width="400"
        overlay-opacity="0.7"
      >
        <v-card
          color="box_bg"
        >
          <v-card-title class="text-body-1">
            {{ $t('upappimg') }}
            <v-spacer />
            <v-btn
              fab
              depressed
              small
              fixed
              right
              class="mt-n16 d-flex d-sm-none"
              @click="freebonus_upimg = false"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-card-title>
          <v-card-text class="text-center py-3">
            <div
              v-if="f1!=null"
              class="nav_bg pa-3 rounded mx-auto"
            >
              <div class="mb-2">
                <v-btn
                  depressed
                  fab
                  x-small
                  dark
                  color="red accent-2"
                  @click="delimg"
                >
                  <v-icon color="red1">
                    mdi-delete-forever
                  </v-icon>
                </v-btn>
              </div>
              
              <img
                v-if="f1==null"
                width="100"
                src="../../assets/img/pic/postimg-01.jpg"
              >
              <img
                v-if="f1!=null"
                width="100"
                :src="f1"
              >
            </div>
            
            <div
              v-if="f1==null"
              class="d-flex justify-center"
            >
              <v-avatar
                color="nav_bg"
                size="56"
              >
                <v-file-input
                  ref="ff1"
                  v-model="file1"
                  hide-input
                  accept="image/png, image/jpeg, image/jpg"
                  prepend-icon="mdi-camera-plus"
                  class="pa-0 ma-0 ml-2"
                  style="flex: none!important;"
                  @change="onSelectFile()"
                />
              </v-avatar>
            </div>
          </v-card-text>
          <v-card-actions>
            <v-btn
              depressed
              block
              color="primary"
              class="text-center"
              :loading="loading"
              @dblclick="dblclick"
              @click="savescreenshot"
            >
              {{ $t('shangchuan') }}
            </v-btn>
          </v-card-actions>
          <v-divider class="opacity-3 my-2" />
          <v-card-text>
            1.{{ $t('upappimg_rule1') }}
            <div class="text-center my-2">
              <v-btn
                outlined
                large
                dark
                rounded
                color="#25d366"
                :href="whatsappurl2"
              >
                <v-icon left>
                  mdi-whatsapp
                </v-icon>
                Chat on WhatsAPP
              </v-btn>
            </div>
            2.{{ $t('upappimg_rule2') }}

            <v-subheader class="px-0 subtitle-2">
              {{ $t('jlck') }}
            </v-subheader>
            <v-carousel
              cycle
              height="400"
            >
              <v-carousel-item
                v-for="p in 3"
                :key="'i'+p"
                :src="require('@/assets/img/pic/postimg-0'+p+'.jpg')"
              />
            </v-carousel>
          </v-card-text>

          <v-btn
            fab
            small
            fixed
            class="mt-3 btn-center opacity-7"
            @click="freebonus_upimg = false"
          >
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card>
      </v-dialog>
    </template>

    <template>
      <v-dialog
        v-model="firstdeposit"
        width="300"
        transition="dialog-bottom-transition"
        content-class="dialog-end mb-16"
      >
        <v-sheet
          dark
          color="nav_bg"
          class="pa-5 box-down-arrow1 rounded-lg text-center"
        >
          <div class="position-relative">
            <v-btn
              fab
              icon
              small
              absolute
              top
              right
              color="grey darken-2"
              class="mr-n8"
              @click="firstdeposit = false"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </div>

          <h5 class="pb-3 text--secondary">
            {{ $t('dlcgts') }}
          </h5>
          <div class="text-body-2 text--disabled">
            <p class="blue--text">
              {{ $t('ckts') }}
            </p>
            <div>
              <img
                width="72"
                src="../../assets/img/icons/bonus.svg"
              >
            </div>
            <p class="primary--text">
              {{ $t('sc100jj') }}
            </p>
          </div>
          <div class="mt-3 d-flex justify-space-around">
            <v-btn
              depressed
              color="primary"
              @click="showtab_deposit"
            >
              {{ $t('ljck') }}
            </v-btn>
          </div>
        </v-sheet>
      </v-dialog>
    </template>

    <template>
      <v-dialog
        v-model="popup"
        max-width="400"
        content-class="rounded-xl"
      >
        <div
          class="text-center mt-n16 popup-bg"
          style="position: absolute; left: 50%; transform: translate(-50%); max-width: 400px; z-index: 2;"
        >
          <img
            src="../../assets/img/icons/bonus-casino-anim.svg"
            width="70"
          >
        </div>

        <v-card
          flat
          dark
          class="pa-4 rounded-xl"
          style="background: linear-gradient(135deg, #86F87C 0%,#B19FFF 48%,#ECA1FE 100%);"
        >
          <div class="text-center pt-6">
            <h3 style="color: #5d39bf;">
              New benefits
            </h3>
            <div
              class="rounded-lg pa-3 mt-2 purple darken-4 opacity-8 text-body-2"
              style="background: linear-gradient(0deg, #a026d7 0%, #280783 100%);"
            >
              <router-link
                to="/promo"
                class="text-decoration-none white--text"
              >
                <strong class="amber--text">₱88</strong> Giveaway Sign Up Now
                <br>
                🎁 <strong class="amber--text">₱50</strong> Daily Bonus
                <br>
                🎁 <strong class="amber--text">₱100</strong> Daily Bonus
                <br>
                🎁 <strong class="amber--text">₱200</strong> Daily Bonus
                <br>
                ✨Free Journal <strong class="amber--text">₱50</strong> (2x on Sunday)✨
                <br>
                💕50% First Deposit Bonus
              </router-link>
              <v-divider class="my-2 opacity-5" />
              <a
                class="text-decoration-none white--text"
                @click="showtab_deposit"
              >
                <strong class="green--text text--accent-2">Top-up Now</strong>
                <br>
                <strong>Top-up and get bonuses</strong>
              </a>

              <v-list-item
                dense
                light
                class="rounded-pill brown--text text-center box-no-bg mt-3"
                style="background: linear-gradient(56deg, rgb(149, 116, 45), rgb(245, 216, 138) 35%, rgb(193, 148, 53) 75%, rgb(241, 208, 131));"
                @click="goose_open"
              >
                <v-list-item-avatar
                  width="25"
                  height="30"
                  tile
                  class="mr-2 animat-swing1"
                >
                  <img
                    src="../../assets/img/icons/goose.png"
                  >
                </v-list-item-avatar>
                <v-list-item-content>
                  <v-list-item-title>{{ $t('mrzjdbtn_i') }}</v-list-item-title>
                </v-list-item-content>
                <v-list-item-action>
                  <v-icon>
                    mdi-chevron-right
                  </v-icon>
                </v-list-item-action>
              </v-list-item>

              <v-list-item
                dense
                dark
                class="rounded-pill white--text text-center box-no-bg mt-3"
                style="background: linear-gradient(56deg, rgb(219 202 248), rgb(144 95 212) 34%, rgb(186 153 229) 67%, rgb(55 14 154));"
                @click="gotofreespin"
              >
                <v-list-item-avatar
                  size="30"
                  tile
                  class="mr-2"
                >
                  <v-img src="../../assets/img/icons/slots.png" />
                </v-list-item-avatar>
                <v-list-item-content>
                  <v-list-item-title>{{ $t('freespins_t1') }}</v-list-item-title>
                </v-list-item-content>
                <v-list-item-action>
                  <v-icon>
                    mdi-chevron-right
                  </v-icon>
                </v-list-item-action>
              </v-list-item>
            </div>
          </div>
        </v-card>
        <v-btn
          icon
          dark
          class="mt-5 opacity-5"
          style="position: absolute; left: 50%; transform: translate(-50%);"
          @click="popup = false"
        >
          <v-icon large>
            mdi-close-circle
          </v-icon>
        </v-btn>
      </v-dialog>
    </template>

    <template>
      <v-overlay
        :absolute="absolute"
        :value="overlay"
        opacity="0.95"
        z-index="9999999"
        class="align-start d-block"
      >
        <div class="browser_tip mt-8">
          <div class="d-flex justify-end mr-n5">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="166.134"
              height="131.854"
              viewBox="0 0 166.134 131.854"
              :class="'browser_tip_arrow ' + (ios?'browser_tip_arrow_bottom':'')"
            ><g
              id="img_browser_jt"
              transform="translate(-3.999 -5.345)"
            ><path
              id="Union_170"
              data-name="Union 170"
              d="M-3425.25,12618.8a1,1,0,0,1,.89-1.1c3.332-.352,6.66-.764,9.894-1.217a1,1,0,0,1,.742.191,1,1,0,0,1,.389.661,1,1,0,0,1-.852,1.129c-3.256.457-6.607.869-9.961,1.224a.8.8,0,0,1-.108.007A1,1,0,0,1-3425.25,12618.8Zm17.844-2.385a1,1,0,0,1,.168-.748,1,1,0,0,1,.648-.408c3.3-.561,6.594-1.185,9.786-1.858a1,1,0,0,1,1.185.772,1,1,0,0,1-.773,1.185c-3.218.678-6.537,1.308-9.862,1.875a1.074,1.074,0,0,1-.168.013,1,1,0,0,1-.989-.845Zm17.638-3.575a1,1,0,0,1,.117-.758,1,1,0,0,1,.618-.452c3.246-.8,6.48-1.669,9.609-2.585a1,1,0,0,1,1.241.678,1,1,0,0,1-.678,1.238c-3.157.929-6.42,1.806-9.7,2.613a1.029,1.029,0,0,1-.238.028,1,1,0,0,1-.971-.8Zm17.308-4.919a1,1,0,0,1,.627-1.264c3.17-1.064,6.315-2.211,9.353-3.408a1,1,0,0,1,.767.013,1,1,0,0,1,.533.552,1,1,0,0,1-.012.767,1,1,0,0,1-.552.532c-3.066,1.208-6.246,2.367-9.448,3.44a1,1,0,0,1-.317.055,1,1,0,0,1-.951-.707Zm16.808-6.426a1,1,0,0,1-.021-.766,1,1,0,0,1,.528-.557c3.047-1.351,6.062-2.8,8.963-4.3a1,1,0,0,1,.764-.063,1,1,0,0,1,.585.5,1,1,0,0,1-.431,1.346c-2.936,1.517-5.986,2.977-9.067,4.349a.964.964,0,0,1-.4.087,1,1,0,0,1-.915-.587Zm16.083-8.07a1,1,0,0,1,.361-1.364c2.876-1.666,5.713-3.434,8.434-5.252a1,1,0,0,1,1.387.276,1,1,0,0,1-.276,1.387c-2.755,1.844-5.631,3.63-8.542,5.317a1,1,0,0,1-.5.137,1,1,0,0,1-.865-.525Zm15.082-9.8a1,1,0,0,1,.2-1.4c2.651-1.979,5.258-4.069,7.75-6.21a1,1,0,0,1,1.409.107,1,1,0,0,1,.239.729,1,1,0,0,1-.347.684c-2.524,2.167-5.166,4.285-7.854,6.293a1,1,0,0,1-.6.2,1,1,0,0,1-.8-.428Zm13.8-11.535a1,1,0,0,1-.279-.713,1,1,0,0,1,.308-.7c2.378-2.284,4.706-4.678,6.917-7.12a1,1,0,0,1,1.413-.072,1,1,0,0,1,.07,1.412c-2.242,2.479-4.6,4.906-7.015,7.225a1,1,0,0,1-.69.278,1,1,0,0,1-.728-.4Zm12.26-13.162a1,1,0,0,1-.146-1.406c2.065-2.553,4.076-5.22,5.979-7.928a1,1,0,0,1,1.394-.243,1,1,0,0,1,.244,1.394c-1.929,2.745-3.968,5.447-6.065,8.034a.993.993,0,0,1-.776.37,1.008,1.008,0,0,1-.633-.248Zm10.543-14.575a1,1,0,0,1-.317-1.378c1.736-2.774,3.42-5.666,5-8.589a1,1,0,0,1,1.355-.4,1,1,0,0,1,.405,1.354c-1.6,2.961-3.3,5.888-5.064,8.7a1,1,0,0,1-.849.469,1.006,1.006,0,0,1-.526-.205Zm8.757-15.719a1,1,0,0,1-.469-1.333c1.409-2.945,2.762-6,4.026-9.086a1,1,0,0,1,1.3-.549,1,1,0,0,1,.543.54,1,1,0,0,1,0,.766c-1.276,3.119-2.648,6.214-4.073,9.193a1,1,0,0,1-.9.568.992.992,0,0,1-.432-.134Zm18.1-9.073-10.325-10.163-13.837.4a1.389,1.389,0,0,1-.852-1.054,1.388,1.388,0,0,1,.451-1.277l21.845-19.282a1.389,1.389,0,0,1,1.437-.242,1.389,1.389,0,0,1,.863,1.175l2.318,29.041a1.393,1.393,0,0,1-.364,1.052,1.392,1.392,0,0,1-1.019.448,1.367,1.367,0,0,1-.511-.158Z"
              transform="translate(3429.254 -12482.497)"
            /></g></svg>
          </div>
          <div class="mt-n5 px-5 ml-5">
            <h3>{{ $t('browser_tip1') }}</h3>
            <div class="my-3">
              <v-chip class="mr-2">
                1
              </v-chip>
              {{ $t('browser_tip_dianji') }}
              <v-icon>mdi-dots-horizontal</v-icon>
              {{ $t('browser_tip1_1') }}
            </div>
            <div class="my-3">
              <v-chip class="mr-2">
                2
              </v-chip>
              {{ $t('browser_tip_xuanze') }}
              <v-chip
                outlined
              >
                <v-icon left>
                  mdi-web
                </v-icon>
                {{ $t('browser_tip1_2') }}
              </v-chip>
            </div>

            <v-divider class="my-5" />

            <h3>{{ $t('browser_tip2') }}</h3>
            <div class="my-3">
              <v-chip class="mr-2">
                1
              </v-chip>
              {{ $t('browser_tip2_1') }}
              <v-chip
                outlined
              >
                {{ domain }}
              </v-chip>
              <v-btn
                icon
              >
                <v-icon
                  small
                  @click="doCopy(origurl)"
                >
                  mdi-content-copy
                </v-icon>
              </v-btn>
            </div>
            <div class="my-3">
              <v-chip class="mr-2">
                2
              </v-chip>
              {{ $t('browser_tip2_2') }}
            </div>

            <v-divider class="my-5" />
          </div>

          <div class="mt-10 text-center">
            <v-btn
              icon
              @click="overlay = false"
            >
              <v-icon
                large
                class="opacity-5"
              >
                mdi-close-circle
              </v-icon>
            </v-btn>
          </div>
        </div>
      </v-overlay>
    </template>
    <template>
      <v-dialog
        v-model="show_give_away"
        persistent
        max-width="300"
      >
        <v-card>
          <v-card-title>
            {{ $t('giveaway') }}
          </v-card-title>
          <v-card-actions>
            <v-spacer />
            <v-btn
              color="green darken-1"
              text
              @click="give_away_informed"
            >
              OK
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
    </template>
    <template v-if="showdialogfblogin">
      <Fblogin
        v-model="meminfo.id"
        @closefblogin="closefblogin"
      />
    </template>
    <template v-if="showdialogchat">
      <Chat
        v-model="token"
        @closechat="closechat"
      />
    </template>

    <!-- 大转盘弹层 -->
    <template v-if="showLuckyWheel">
      <LuckyWheel
        @close="closeLuckyWheel"
        @login="showloginreg('reg')"
        @win="onLuckyWin"
        @claim="onLuckyClaim"
        @claim-reward="onClaimReward"
        @claim-red-packet="onClaimRedPacket"
        @navigate-to="onNavigateTo"
      />
    </template>
  </v-app>
</template>

<script>
import { Crisp } from "crisp-sdk-web"
import { getDeviceId } from "../../module/storage"
import { getEventSender } from '@/utils/sender'
import { gtag } from '@/utils/gad'
import {store2} from './../../store2.js'
import store from "../../store/"
import Login from '@/components/Login.vue'
import Forget from '@/components/Forget.vue'
import Eggs from '@/components/Eggs.vue'
import Profile from '@/components/Profile.vue'
import Fblogin from '@/components/Fblogin.vue'
import Chat from '@/components/Chat.vue'
import My from '@/components/My.vue'
import LuckyWheel from '@/components/LuckyWheel.vue'

export default {
  name: 'App',
  components:{
    Login,
    Forget,
    Eggs,
    Profile,
    Fblogin,
    Chat,
    My,
    LuckyWheel
  },
  data: () => ({
    domainarr: [],
    downloadurl: '',
    drawer: false,
    drawer_width: 0,
    badgeshow: false,
    items: [
      { title: 'English', lang: 'en', image:'flag-usa.svg' },
      { title: 'Tagalog', lang: 'ph', image:'flag-php.svg' }
    ],
    lang:'',
    themes: [
      { value:'dark', text:'Dark', icon:'mdi-brightness-4' },
      { value:'light', text:'Light', icon:'mdi-white-balance-sunny' },
      { value:'system', text:'System', icon:'mdi-theme-light-dark' },
      { value:'red', text:'Red', icon:'mdi-palette' },
      { value:'purple', text:'Purple', icon:'mdi-palette' },
      { value:'blue', text:'Blue', icon:'mdi-palette' },
    ],
    theme_group: '',
    theme_background: null,
    themetoggle: 'dark',
    loginreg: false,
    forget: false,
    goose: false,
    showdialog: false,
    bottomvalue: '',
    profiletype: false,
    showdialogprofile: false,
    showdialogmy: false,
    meminfo:{
      id:0,
      avatar: 1,
      assets: {
        
      }
    },
    myvipinfo:{},
    balance: 0,
    balancetimer: null,
    nologinbtn: false,
    ios: false,
    iosapp: false,
    iosapp_help: false,
    freebonus_claim: false,
    freebonus_upimg: false,
    website: '',
    websiteerror: false,
    claimfreebonus: false,
    firstdeposit: false,
    file1: null,
    f1: null,
    cpfset: true,
    screenshot: 2,
    in24hour: false,
    popup: false,
    loading: false,
    show777: false,
    tgbonus_show: false,
    showbar: true,
    absolute: false,
    overlay: false,
    origurl: '',
    domain: '',
    sms_domain: '',
    show_give_away: false,
    hasemail: false,
    email: '',
    emailcode: '',
    emailcodebtndisabled: false,
    emailcodebtntext: '',
    countdown2: 120,
    timer2: null,
    verify_email: false,
    safaritip: false,
    whatsappurl1: 'whatsapp://send?text=🎁Plataforma dando 12,77 se cadastrar no CPF💗Meusss ganhos de agora nem acredito 💸 corra vamos testar👉 https://euamojogos.com',
    whatsappurl2: '',
    show_tip: true,
    showbar_conf: 0,
    showdialogfblogin: false,
    showdialogchat: false,
    logoimg: '',
    logoimgw: 120,
    // 大转盘相关
    showLuckyWheel: false
  }),
  computed: {
    token() {
        return this.getStorage('token')
    },
  },
  watch: {
    lang: {
      handler(newVal, oldVal) {
        this.$i18n.locale = newVal
        this.$vuetify.lang.current = newVal
        localStorage.setItem('Lang', newVal)
        //sessionStorage.setItem('Lang', newVal)
        if(oldVal!=''){
          location.reload()
        }
      }
    },
    themetoggle: {
      handler(newVal, oldVal) {
        console.log(newVal, oldVal)
        if(newVal=='light'){
          //this.$vuetify.theme.themes.light = this.$vuetify.theme.defaults.light
          Object.assign(this.$vuetify.theme.themes.light, this.$vuetify.theme.defaults.light)
          this.$vuetify.theme.light = true
          this.$vuetify.theme.dark = false
          sessionStorage.setItem('Theme', 'light')
          this.theme_background = this.$vuetify.theme.themes.light.background
        }
        if(newVal=='dark'){
          //this.$vuetify.theme.themes.dark = this.$vuetify.theme.defaults.dark
          Object.assign(this.$vuetify.theme.themes.dark, this.$vuetify.theme.defaults.dark)
          this.$vuetify.theme.light = false
          this.$vuetify.theme.dark = true
          sessionStorage.setItem('Theme', 'dark')
          this.theme_background = this.$vuetify.theme.themes.dark.background
        }
        if(newVal=='system'){
          if(window.matchMedia("(prefers-color-scheme: dark)").matches){
            Object.assign(this.$vuetify.theme.themes.dark, this.$vuetify.theme.defaults.dark)
            this.$vuetify.theme.light = false
            this.$vuetify.theme.dark = true
            this.theme_background = this.$vuetify.theme.themes.dark.background
          }else{
            Object.assign(this.$vuetify.theme.themes.light, this.$vuetify.theme.defaults.light)
            this.$vuetify.theme.light = true
            this.$vuetify.theme.dark = false
            this.theme_background = this.$vuetify.theme.themes.light.background
          }
          sessionStorage.setItem('Theme', 'system')
        }
        if(newVal=='red'){
          //if(window.matchMedia("(prefers-color-scheme: dark)").matches){
            Object.assign(this.$vuetify.theme.themes.dark, this.$vuetify.theme.defaults.red)
            this.$vuetify.theme.light = false
            this.$vuetify.theme.dark = true
            this.theme_background = this.$vuetify.theme.themes.dark.background
          //}else{
          //  Object.assign(this.$vuetify.theme.themes.light, this.$vuetify.theme.defaults.red)
          //  this.$vuetify.theme.light = true
          //  this.$vuetify.theme.dark = false
          //  this.theme_background = this.$vuetify.theme.themes.light.background
          //}
          sessionStorage.setItem('Theme', 'red')
        }
        if(newVal=='purple'){
          //if(window.matchMedia("(prefers-color-scheme: dark)").matches){
            Object.assign(this.$vuetify.theme.themes.dark, this.$vuetify.theme.defaults.purple)
            this.$vuetify.theme.light = false
            this.$vuetify.theme.dark = true
            this.theme_background = this.$vuetify.theme.themes.dark.background
          //}else{
          //  Object.assign(this.$vuetify.theme.themes.light, this.$vuetify.theme.defaults.purple)
          //  this.$vuetify.theme.light = true
          //  this.$vuetify.theme.dark = false
          //  this.theme_background = this.$vuetify.theme.themes.light.background
          //}
          sessionStorage.setItem('Theme', 'purple')
        }
        if(newVal=='blue'){
          //if(window.matchMedia("(prefers-color-scheme: dark)").matches){
            Object.assign(this.$vuetify.theme.themes.dark, this.$vuetify.theme.defaults.blue)
            this.$vuetify.theme.light = false
            this.$vuetify.theme.dark = true
            this.theme_background = this.$vuetify.theme.themes.dark.background
          //}else{
          //  Object.assign(this.$vuetify.theme.themes.light, this.$vuetify.theme.defaults.blue)
          //  this.$vuetify.theme.light = true
          //  this.$vuetify.theme.dark = false
          //  this.theme_background = this.$vuetify.theme.themes.light.background
          //}
          sessionStorage.setItem('Theme', 'blue')
        }
      }
    },
    '$store.state.hour3reddot': function (newVal) {
      if (newVal) {
        this.badgeshow = true
      }
    },
    '$store.state.luckywheel': function (newVal) {
      if (newVal) {
        this.showLuckyWheel = true
      } else {
        this.showLuckyWheel = false
      }
    },
    '$store.state.balance': function (newVal) {
      this.balance = newVal
    },
    // 添加路由监听
    '$route': {
      handler(to) {
        // 根据路由name设置showbar
        this.showbar = to.name === 'Home'
        if(to.name !== 'Home'){
          Crisp.chat.hide()
        }else{
          Crisp.chat.show()
        }
      },
      immediate: true // 立即执行一次
    }
  },
  created() {
    /*
    this.$nextTick(() => {
      // 禁用右键
      document.oncontextmenu = new Function('event.returnValue=false')
      // 禁用选择
      document.onselectstart = new Function('event.returnValue=false')
      // 禁止f12
      document.οnkeydοwn = new Function('event.returnValue=false')
    })
 
    // 上面的禁止f12那段代码没有生效，但是加了下面这段就能生效。
    document.onkeydown = function (e) {
      if (e && e.keyCode === 123) {
        e.returnValue = false
        // e.keyCode = 0   //去掉也可以的，倘若要写，则需要setter 以及 getter配合使用，不配合，会报错
        return false
      }
    }
    */
    this.settheme()

    console.log(this.$route.query)
    sessionStorage.setItem('code', this.$route.query.code??'')
    if(this.$route.query.hasOwnProperty('af_config')){
      localStorage.setItem('af_config', this.$route.query.af_config)
    }
    if(localStorage.getItem('lpid')==null){
      this.findlp()
    }
    
    if(window.screen.width>window.screen.height){
      this.drawer = true
      this.drawer_width = '18rem'
    }else{
      this.drawer_width = '85%'
    }
    this.$i18n.locale = localStorage.getItem('Lang')?localStorage.getItem('Lang'):'en'
    this.$vuetify.lang.current = localStorage.getItem('Lang')?localStorage.getItem('Lang'):'en'
    this.lang = this.$i18n.locale

    console.log('is_pc:'+this.$store.state.is_pc)

    Crisp.configure('f5a65a98-82b8-46f4-9ac4-0adc946a4073', {
      autoload: false
    })

    if(!!navigator.userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)){
      this.ios = true
    }

    if (navigator.userAgent.match(/FBAN|FBAV/i)) {
      this.origurl = window.location.href
      this.domain = window.location.host
      if(this.$store.state.fbopen){
        //this.overlay = true
        store.commit('fbopen', false)
        if(!this.ios){
          /*
          setTimeout(() => {
            alert("Baixe o APP - Cadastre-se agora e ganhe um presente grátis $12.77 🎁🎁🎁 Os jogos mais divertidos de 2024 ⭐️⭐️⭐️⭐️⭐️ (5,0)"); 
            top.location = response.data.download
          }, 30000)
          */
        }
      }
    }

    this.$server.conf().then((response) => {
      this.domainarr = response.data.logo.split('.').reverse()
      this.downloadurl = response.data.download
      this.theme_group = response.data.group
      this.sms_domain = response.data.sms_domain
      this.showbar_conf = response.data.showbar
      this.logoimg = response.data.logoimg
      this.logoimgw = response.data.logoimgw
      if(response.data.theme){
        sessionStorage.setItem('Theme', response.data.theme)
        this.settheme()
      }
      store.commit('download', response.data.download)
      if(this.overlay==true && !this.ios){
        //alert("Baixe o APP - Cadastre-se agora e ganhe um presente grátis $12.77 🎁🎁🎁 Os jogos mais divertidos de 2024 ⭐️⭐️⭐️⭐️⭐️ (5,0)"); 
        //top.location = response.data.download
      }

      this.whatsappurl2 = 'whatsapp://send?text=🎁Plataforma dando 12,77 se cadastrar no CPF 🎁Bora aproveitar que tá pagando muito 💸 '+this.sms_domain+' 💗Meusss ganhos de agora nem acredito 🥺🙏🏼agora $ 12.5 de bônus posta no Facebook💸 corra vamos testar👉 '+this.sms_domain+' 🤝 Amigos, por favor me respondam se você está interessado ou não.'
      if( (/iphone|ipod|ipad/.test( window.navigator.userAgent.toLowerCase() )) && (/safari/.test( window.navigator.userAgent.toLowerCase() ))==false ){
        this.whatsappurl1 = "pgb://"+encodeURIComponent(this.whatsappurl1)
        this.whatsappurl2 = "pgb://"+encodeURIComponent(this.whatsappurl2)
      }
    })
  },
  mounted() {
    if(this.$route.query.s){
      this.loginbys()
    }else{
      if(this.getStorage('member_id')){
        this.tgbonus_show = true
        this.getmeminfo()
        this.balancetimer = setInterval(() => {
          this.getmeminfo()
        }, 60000)
      }
    }

    this.emailcodebtntext = this.$t('hqyzm')

    if( !(this.getStorage('popuptime') && parseInt(this.getStorage('popuptime')) > (parseInt(Date.now()/1000)-300)) ){
      //this.popup = true
      this.openLuckyWheel()
      setTimeout(() => {//按顺序
        //this.goose = true
      }, 1)
      this.setStorage('popuptime', parseInt(Date.now()/1000))
    }

    //console.log(this.$route)
    if(this.$route.name=='homebonus' || this.$route.name=='vipbonus'){
      if(this.getStorage('member_id')){
        this.showprofile('promo')
      }
    }
    if(this.$route.name=='reg' && !this.getStorage('member_id')){
      this.showloginreg('reg')
    }
    setTimeout(() => {
      this.nologinbtn=true
    }, 3000)

    setTimeout(() => {
      let ID = this.meminfo.id
      if(this.meminfo.deposited){
        ID = ID + '_cun'
      }
      if(this.meminfo.invited){
        ID = ID + '_yao'
      }
      if(this.meminfo.promod){
        ID = ID + '_tui'
      }
      if(this.meminfo.id>0){
        Crisp.setTokenId(this.meminfo.id)
        if(this.meminfo.deposited || this.meminfo.invited || this.meminfo.promod){
          Crisp.user.setEmail(ID+"@lv.moneycoming.ph")
        }
        Crisp.load()
      }
    }, 5000)

    Crisp.chat.show()
  },
  beforeDestroy () {
    Crisp.chat.hide()
  },
  methods: {
    findlp() {
      let paramObj = {
        
      }
      this.$server.findlp(paramObj).then((response) => {
        if(response.code==200){
          localStorage.setItem('lpid', response.data.lpid)
        }else{
          
        }
      })
    },
    settheme() {
      console.log(this.$vuetify.theme)
      //console.log(sessionStorage.getItem('Theme')==null, window.matchMedia("(prefers-color-scheme: dark)").matches)
      if(sessionStorage.getItem('Theme')=='light'){
        Object.assign(this.$vuetify.theme.themes.light, this.$vuetify.theme.defaults.light)
        this.$vuetify.theme.light = true
        this.$vuetify.theme.dark = false
        this.themetoggle = 'light'
        this.theme_background = this.$vuetify.theme.themes.light.background
      }
      if(sessionStorage.getItem('Theme')=='dark'){
        Object.assign(this.$vuetify.theme.themes.dark, this.$vuetify.theme.defaults.dark)
        this.$vuetify.theme.light = false
        this.$vuetify.theme.dark = true
        this.themetoggle = 'dark'
        this.theme_background = this.$vuetify.theme.themes.dark.background
      }
      if(sessionStorage.getItem('Theme')=='system'){
        this.themetoggle = 'system'
        if(window.matchMedia("(prefers-color-scheme: dark)").matches){
          Object.assign(this.$vuetify.theme.themes.dark, this.$vuetify.theme.defaults.dark)
          this.$vuetify.theme.light = false
          this.$vuetify.theme.dark = true
          this.theme_background = this.$vuetify.theme.themes.dark.background
        }else{
          Object.assign(this.$vuetify.theme.themes.light, this.$vuetify.theme.defaults.light)
          this.$vuetify.theme.light = true
          this.$vuetify.theme.dark = false
          this.theme_background = this.$vuetify.theme.themes.light.background
        }
      }
      if(sessionStorage.getItem('Theme')==null){
        this.themetoggle = 'system'
        if(window.matchMedia("(prefers-color-scheme: dark)").matches){
          Object.assign(this.$vuetify.theme.themes.dark, this.$vuetify.theme.defaults.dark)
          this.$vuetify.theme.light = false
          this.$vuetify.theme.dark = true
          this.theme_background = this.$vuetify.theme.themes.dark.background
        }else{
          Object.assign(this.$vuetify.theme.themes.light, this.$vuetify.theme.defaults.light)
          this.$vuetify.theme.light = true
          this.$vuetify.theme.dark = false
          this.theme_background = this.$vuetify.theme.themes.light.background
        }
      }
      if(sessionStorage.getItem('Theme')=='red'){
        this.themetoggle = 'red'
        Object.assign(this.$vuetify.theme.themes.dark, this.$vuetify.theme.defaults.red)
        this.$vuetify.theme.light = false
        this.$vuetify.theme.dark = true
        this.theme_background = this.$vuetify.theme.themes.dark.background
      }
      if(sessionStorage.getItem('Theme')=='purple'){
        this.themetoggle = 'purple'
        Object.assign(this.$vuetify.theme.themes.dark, this.$vuetify.theme.defaults.purple)
        this.$vuetify.theme.light = false
        this.$vuetify.theme.dark = true
        this.theme_background = this.$vuetify.theme.themes.dark.background
      }
      if(sessionStorage.getItem('Theme')=='blue'){
        this.themetoggle = 'blue'
        Object.assign(this.$vuetify.theme.themes.dark, this.$vuetify.theme.defaults.blue)
        this.$vuetify.theme.light = false
        this.$vuetify.theme.dark = true
        this.theme_background = this.$vuetify.theme.themes.dark.background
      }
    },
    loginbys() {
      let paramObj = {
        s: this.$route.query.s
      }
      this.$server.loginbys(paramObj).then((response) => {
        if(response.code==200){
          this.setStorage('member_id', response.data.member_id)
          this.setStorage('token', response.data.token)
          this.getmeminfo()
          this.$router.push('/invite')
          //location.reload()
        }else{
          this.$snackbar.warning(this.$t('dlsb'))
        }
      })
    },
    changeDrawer() {
      console.log(window.screen.width, window.screen.height)
      this.drawer = !this.drawer
    },
    jumpTo(page) {
      this.$router.replace({name:page})
    },
    goose_open() {
      this.goose = true
    },
    goose_close() {
      this.goose = false
    },
    showloginreg(tab) {
      this.loginreg = tab
      this.showdialog = true
    },
    closeloginreg() {
      this.loginreg = false
      this.showdialog = false
      this.getmeminfo()
    },
    showforget() {
      this.forget = true
    },
    closeforget() {
      this.forget = false
    },
    showprofile(type) {
      this.profiletype = type
      this.showdialogprofile = true
    },
    closeprofile() {
      this.profiletype = false
      this.showdialogprofile = false
    },
    showmy() {
      this.showdialogmy = true
    },
    closemy() {
      this.showdialogmy = false
      this.getmeminfo()
    },
    showtab_deposit() {
      if(this.getStorage('member_id')){
        store2.wallet_currtab = 'deposit'
        this.showprofile('wallet')
      }else{
        this.showloginreg('login')
      }
      this.firstdeposit = false
      this.popup = false
    },
    showtab_withdraw() {
      store2.wallet_currtab = 'withdraw'
      this.showprofile('wallet')
    },
    logout() {
      this.removeStorage("token")
      this.removeStorage("member_id")
      this.meminfo = {id:0}
      //this.$router.push('/home')
      location.reload()
    },
    customer_service() {
      this.$router.push('/ticket')
      /*
      let paramObj = {
        did: getDeviceId(),
      }
      if(this.getStorage('member_id')){
        paramObj.member_id = this.getStorage('member_id')
      }
      this.$server.ticketurl(paramObj).then((response) => {
        if(response.code==200){
          window.open(response.data, '_blank')
        }
      })
      */
    },
    getmeminfo() {
      let paramObj = {
        member_id: this.getStorage('member_id'),
      }
      this.$server.getmeminfo(paramObj).then((response) => {
        if(response.code==200){
          this.meminfo = response.data
          if(this.meminfo.email!=null){
            this.email = this.meminfo.email
            this.hasemail = true
          }
          this.verify_email = response.data.verify_email
          this.myvip()
          this.bonus777()
          this.balance = response.data.total_balance
          store.commit('balance', this.balance)

          if( response.data.first_deposit ){
            console.log(response.data.first_deposit)

            if(response.data.pixel!=''){
              if(isNaN(response.data.pixel)){
                
                var idlabel = response.data.pixel.split("/")
                this.loadScript('https://www.googletagmanager.com/gtag/js?id='+idlabel[0],()=>{
                  gtag('js', new Date());
                  gtag('set', {
                    'phone_conversion_number': '63'+response.data.phone,
                    'phone_conversion_ids': [response.data.pixel]
                  })
                  gtag('config', idlabel[0]);
                  //console.log(idlabel)
                });

              }else{
                this.$analytics.fbq.init(response.data.pixel, {
                  ph: '63'+response.data.phone
                })
              }
            }

            let purchaseObj = {
              "some_parameter": "some_value", // from additional_event_values
              "af_currency": "PHP", // from currency
              "af_content_id" :"Gold1002", // from purchase
              "af_revenue": "102", // from revenue
              "af_quantity": "10", // from purchase
              "af_validated": true // flag that AF verified the purchase
            }
            this.sendFirstPurchse(purchaseObj)
            this.notify(response.data.first_deposit)

            if(response.data.pixel!=''){
              if(isNaN(response.data.pixel)){
                
                gtag('event', 'conversion', {
                    'send_to': response.data.pixel,
                    'value': 5,
                    'currency': 'PHP'
                })
                
              }else{
                if(response.data.chnl!='GG'){
                  if(response.data.event_type=='2'){
                    this.$analytics.fbq.event('SubmitApplication')
                  }else{
                    this.$analytics.fbq.event('Purchase',{value: response.data.first_deposit_amount, currency: 'PHP'})
                  }
                }
              }
            }

          }

          if( response.data.deposited==1 && !!navigator.userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)){
            if( !(this.getStorage('iosdesktoptime') && parseInt(this.getStorage('iosdesktoptime')) > (parseInt(Date.now()/1000)-86400)) ){
              if( !this.getStorage('iosinstalled') ){
                this.iosapp = true
                this.setStorage('iosdesktoptime', parseInt(Date.now()/1000))
              }
            }
          }

          if( response.data.deposited==0 ){
            this.show_tip = true
            if( !(this.getStorage('remind') && parseInt(this.getStorage('remind')) > (parseInt(Date.now()/1000)-86400)) ){
              if( !(this.getStorage('remind_times') && parseInt(this.getStorage('remind_times'))>5) ){
                this.firstdeposit = true
                if(this.getStorage('remind_times')==null){
                  this.setStorage('remind_times', 1)
                }else{
                  this.setStorage('remind_times', parseInt(this.getStorage('remind_times'))+1)
                }
              }
              this.setStorage('remind', parseInt(Date.now()/1000))
            }
          }

          if( response.data.reg_time_diff<86400 ){
            this.show777 = true
          }

          if(this.meminfo.give_away>0){
            this.show_give_away = true
          }

        }
      })
    },
    iosinstalled() {
      this.iosapp = false
      this.setStorage('iosinstalled', parseInt(Date.now()/1000))
    },
    myvip() {
      let paramObj = {
        member_id: this.getStorage('member_id'),
      }
      this.$server.myvip(paramObj).then((response) => {
        if(response.code==200){
          this.myvipinfo = response.data
          store.commit('current_level', response.data.mylevel)
          if (parseInt(response.data.hour3_lasttime) == 0) {
            store.commit('hour3reddot', true)
          }
        }
      })
    },
    notify(trade_no) {
      let paramObj = {
        member_id: this.getStorage('member_id'),
        trade_no:trade_no
      }
      this.$server.notify(paramObj).then((response) => {
        if(response.code==200){
          
        }else{
          
        }
      })
    },
    bonus777() {
      let paramObj = {
        member_id: this.getStorage('member_id')
      }
      this.$server.bonus777(paramObj).then((response) => {
        if(response.code==200){
          this.cpfset = response.data.disabled
          this.screenshot = response.data.screenshot
          this.in24hour = response.data.in24hour

          if( !(this.getStorage('bonus777time') && parseInt(this.getStorage('bonus777time')) > (parseInt(Date.now()/1000)-3600*6)) ){
            if(!this.cpfset || this.screenshot<2){
              if( this.meminfo.reg_time_diff<86400 ){
                this.freebonus_claim = true
              }
            }
            this.setStorage('bonus777time', parseInt(Date.now()/1000))
          }
        }else{
          
        }
      })
    },
    gocpf() {
      this.showmy()
    },
    cpfbonus() {
      this.loading = true
      if(this.website.toLowerCase()!='bonus.game'){
        this.websiteerror = true
        this.loading = false
        return false
      }
      let paramObj = {
        member_id: this.getStorage('member_id'),
        email: this.email,
        emailcode: this.emailcode,
      }
      this.$server.cpfbonus(paramObj).then((response) => {
        if(response.code==200){
          this.claimfreebonus = false
          this.getmeminfo()
        }else{
          this.$snackbar.warning(response.msg)
        }
        this.loading = false
      })
    },
    onSelectFile() {
      if(this.file1.size>20000000){
        this.file1 = null
        this.$snackbar.error('O tamanho da imagem deve ser inferior a 20 MB')
        return false
      }
      let paramObj = {
        member_id: this.getStorage('member_id'),
        file: this.file1
      }
      this.$server.uploadscreenshot(paramObj).then((response) => {
        if(response.code==200){
          this.f1 = response.data.url
        }else{
          this.file1 = null
          this.f1 = null
          this.$snackbar.error(response.msg)
        }
      })
    },
    savescreenshot() {
      this.loading = true
      if(this.f1==null){
        this.$snackbar.error(this.$t('zs1zt'))
        this.loading = false
        return false
      }
      let paramObj = {
        member_id: this.getStorage('member_id'),
        imgurl: this.f1,
      }
      this.$server.savescreenshot(paramObj).then((response) => {
        if(response.code==200){
          this.file1 = null
          this.f1 = null
          this.bonus777()
          this.freebonus_upimg = false
        }else{

        }
        this.loading = false
      })
    },
    dblclick() {
      console.log('dblclick')
    },
    delimg() {
      this.file1 = null
      this.f1 = null
    },
    receivescreenshotaward() {
      let paramObj = {
        member_id: this.getStorage('member_id'),
      }
      this.$server.receivescreenshotaward(paramObj).then((response) => {
        if(response.code==200){
          this.freebonus_claim = false
          this.getmeminfo()
        }else{
          this.$snackbar.warning(response.msg)
        }
      })
    },
    sendFirstPurchse(purchaseObj)
    {
      let event_sender = getEventSender()
      console.log(event_sender)
      if(event_sender)
      {
        event_sender("af_purchase",JSON.stringify(purchaseObj))
      }
    },
    give_away_informed()
    {
      let paramObj = {
        member_id: this.getStorage('member_id'),
        id: this.meminfo.give_away
      }
      this.$server.give_away_informed(paramObj).then((response) => {
        if(response.code==200){
          
        }else{
          
        }
        this.show_give_away = false
      })
    },
    sendemail() {
      var email_regExp = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/
      if (!email_regExp.test(this.email)) {
        this.$snackbar.warning(this.$t('yxgscw'))
        return false;
      }
      this.emailcodebtndisabled = true
      let paramObj = {
        member_id: this.getStorage('member_id'),
        email: this.email
      }
      this.$server.sendemail_change(paramObj).then((response) => {
        if(response.code==200){
          this.timer2 = setInterval(() => {
            if (this.countdown2 > 0 && this.countdown2 <= 120) {
              this.countdown2--;
              if (this.countdown2 !== 0) {
                this.emailcodebtntext = this.$t('hqyzm') + " (" + this.countdown2 + ")"
              } else {
                this.clear2()
              }
            }
          }, 1000)
        }else{
          this.emailcodebtndisabled = false
          this.$snackbar.warning(this.$t('fssb')+response.msg)
        }
      })
    },
    clear2() {
      clearInterval(this.timer2)
      this.countdown2 = 120
      this.timer2 = null
      this.emailcodebtndisabled = false
      this.emailcodebtntext = this.$t('hqyzm')
    },
    gotofreespin() {
      this.$router.push({
        name: 'FreeSpins',
      })
      this.popup = false
    },
    iosios() {
      this.iosapp = true
      if(!(/Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent) && !/Crios/.test(navigator.userAgent))){
        this.safaritip = true
        this.doCopy(window.location.protocol+"//"+window.location.host)
      }
    },
    doCopy: function (text) {
      this.$copyText(text).then( (e)=>{
        console.log(e)
        this.$snackbar.info(this.$t('fzcg'))
      }, function (e) {
        console.log(e)
      })
    },
    loadScript(src, callback) {
      var script = document.createElement('script'),
          head = document.getElementsByTagName('head')[0];
      script.type = 'text/javascript';
      script.charset = 'UTF-8';
      script.src = src;
      if (script.addEventListener) {
          script.addEventListener('load', function () {
              callback();
          }, false);
      } else if (script.attachEvent) {
          script.attachEvent('onreadystatechange', function () {
              var target = window.event.srcElement;
              if (target.readyState == 'loaded') {
                  callback();
              }
          });
      }
      head.appendChild(script);
    },
    fblogin() {
      Crisp.chat.hide()
      //this.showdialogfblogin = true
    },
    closefblogin() {
      Crisp.chat.show()
      this.showdialogfblogin = false
    },
    chat() {
      if(this.getStorage('member_id')){
        Crisp.chat.hide()
        this.showdialogchat = true
      }
    },
    closechat() {
      Crisp.chat.show()
      this.showdialogchat = false
    },
    // 大转盘相关方法
    openLuckyWheel() {
      this.drawer = false
      this.showLuckyWheel = true
    },
    closeLuckyWheel() {
      this.showLuckyWheel = false
    },
    onLuckyWin(result) {
      // 这里可以添加中奖后的处理逻辑，比如更新用户余额等
      this.$snackbar.success(`Congratulations! You have won ${result.winPrize.name}!`)
    },
    onLuckyClaim(prize) {
      console.log('Claim Prize:', prize)
      // 这里可以调用API领取奖品
      this.getmeminfo() // 重新获取用户信息更新余额
    },
    onClaimReward(prize) {
      console.log('Claim Reward:', prize)
      // 这里可以添加领取奖励后的处理逻辑
      this.getmeminfo() // 重新获取用户信息更新余额
    },
    onClaimRedPacket(prize) {
      console.log('Red Packet:', prize)
      // 这里可以调用API领取红包奖励
      this.$snackbar.success(`Congratulations! You have won ${prize.displayAmount}!`)
      this.getmeminfo() // 重新获取用户信息更新余额
    },
    onNavigateTo(action) {
      switch (action) {
        case 'deposit':
          this.showtab_deposit()
          break
        case 'bet':
          this.$router.push({ name: 'hot' })
          break
        case 'download':
          this.$router.push({ name: 'download' })
          break
        case 'auth':
          this.showdialogmy = true
          break
        case 'invite':
          this.$router.push({ name: 'invite' })
          break
        default:
      }
    }
  },
};
</script>

<style>
  @import '../../assets/css/style.css';

.theme--light.logo.v-toolbar.v-sheet {
    background-color: #f5f5f5;
}

@media (max-width: 767px) {
  .v-navigation-drawer {
    width: 80%!important;
  }
}

@media (min-width: 768px) {
  .v-navigation-drawer {
    width: 280px!important;
  }
}
</style>
<style>
.freebonus-bg{
  overflow: hidden;
}
.freebonus-bg::before{
  content: "";
  position: absolute;
  top: -50%;
  left: -30%;
  width: 600px;
  height: 600px;
  border-radius: 50%;
  background-color: #ffffff;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100%25' height='100%25' viewBox='0 0 800 800'%3E%3Cdefs%3E%3CradialGradient id='a' cx='400' cy='400' r='50%25' gradientUnits='userSpaceOnUse'%3E%3Cstop offset='0' stop-color='%23ffffff'/%3E%3Cstop offset='1' stop-color='%2376C6FF'/%3E%3C/radialGradient%3E%3CradialGradient id='b' cx='400' cy='400' r='70%25' gradientUnits='userSpaceOnUse'%3E%3Cstop offset='0' stop-color='%23ffffff'/%3E%3Cstop offset='1' stop-color='%2390D4FF'/%3E%3C/radialGradient%3E%3C/defs%3E%3Crect fill='url(%23a)' width='800' height='800'/%3E%3Cg fill-opacity='.8'%3E%3Cpath fill='url(%23b)' d='M998.7 439.2c1.7-26.5 1.7-52.7 0.1-78.5L401 399.9c0 0 0-0.1 0-0.1l587.6-116.9c-5.1-25.9-11.9-51.2-20.3-75.8L400.9 399.7c0 0 0-0.1 0-0.1l537.3-265c-11.6-23.5-24.8-46.2-39.3-67.9L400.8 399.5c0 0 0-0.1-0.1-0.1l450.4-395c-17.3-19.7-35.8-38.2-55.5-55.5l-395 450.4c0 0-0.1 0-0.1-0.1L733.4-99c-21.7-14.5-44.4-27.6-68-39.3l-265 537.4c0 0-0.1 0-0.1 0l192.6-567.4c-24.6-8.3-49.9-15.1-75.8-20.2L400.2 399c0 0-0.1 0-0.1 0l39.2-597.7c-26.5-1.7-52.7-1.7-78.5-0.1L399.9 399c0 0-0.1 0-0.1 0L282.9-188.6c-25.9 5.1-51.2 11.9-75.8 20.3l192.6 567.4c0 0-0.1 0-0.1 0l-265-537.3c-23.5 11.6-46.2 24.8-67.9 39.3l332.8 498.1c0 0-0.1 0-0.1 0.1L4.4-51.1C-15.3-33.9-33.8-15.3-51.1 4.4l450.4 395c0 0 0 0.1-0.1 0.1L-99 66.6c-14.5 21.7-27.6 44.4-39.3 68l537.4 265c0 0 0 0.1 0 0.1l-567.4-192.6c-8.3 24.6-15.1 49.9-20.2 75.8L399 399.8c0 0 0 0.1 0 0.1l-597.7-39.2c-1.7 26.5-1.7 52.7-0.1 78.5L399 400.1c0 0 0 0.1 0 0.1l-587.6 116.9c5.1 25.9 11.9 51.2 20.3 75.8l567.4-192.6c0 0 0 0.1 0 0.1l-537.3 265c11.6 23.5 24.8 46.2 39.3 67.9l498.1-332.8c0 0 0 0.1 0.1 0.1l-450.4 395c17.3 19.7 35.8 38.2 55.5 55.5l395-450.4c0 0 0.1 0 0.1 0.1L66.6 899c21.7 14.5 44.4 27.6 68 39.3l265-537.4c0 0 0.1 0 0.1 0L207.1 968.3c24.6 8.3 49.9 15.1 75.8 20.2L399.8 401c0 0 0.1 0 0.1 0l-39.2 597.7c26.5 1.7 52.7 1.7 78.5 0.1L400.1 401c0 0 0.1 0 0.1 0l116.9 587.6c25.9-5.1 51.2-11.9 75.8-20.3L400.3 400.9c0 0 0.1 0 0.1 0l265 537.3c23.5-11.6 46.2-24.8 67.9-39.3L400.5 400.8c0 0 0.1 0 0.1-0.1l395 450.4c19.7-17.3 38.2-35.8 55.5-55.5l-450.4-395c0 0 0-0.1 0.1-0.1L899 733.4c14.5-21.7 27.6-44.4 39.3-68l-537.4-265c0 0 0-0.1 0-0.1l567.4 192.6c8.3-24.6 15.1-49.9 20.2-75.8L401 400.2c0 0 0-0.1 0-0.1L998.7 439.2z'/%3E%3C/g%3E%3C/svg%3E");
  background-attachment: fixed;
  background-size: cover;
  animation: rotateAnimation 20s linear infinite;
}
@keyframes rotateAnimation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.dialog-end{
  align-self: flex-end;
}
.box-down-arrow:after {
  content: "";
  position: absolute;
  width: 1.5rem;
  height: 1.5rem;
  background: var(--v-nav_bg-base);
  transform: rotate(-135deg);
  left: 50%;
  margin-left: -.8rem;
  bottom: .8rem;
}
.popup-bg{
  background-image: url("../../assets/img/ribbons.webp");
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}
.browser_tip_arrow{
  height: 5rem;
  fill: #fff;
}
.browser_tip_arrow_bottom{
  -webkit-transform: rotateX(180deg);
  transform: rotateX(180deg);
  bottom: 0.5rem;
  right: 1.27rem;
  top: auto;
  position: fixed;
}

.tooltip-bottom::before{
    border-right: solid 8px transparent;
    border-left: solid 8px transparent;
    transform: translateX(-50%);
    position: absolute;
    z-index: -21;
    content: '';
    bottom: 100%;
    left: 50%;
    height: 0;
    width: 0;
}
.tooltip-bottom.primary::before{
    border-bottom: solid 8px var(--v-primary-base);
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

.pulse-animation {
  animation: pulse 1s infinite ease-in-out;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>