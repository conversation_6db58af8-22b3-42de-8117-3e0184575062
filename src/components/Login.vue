<template>
  <v-dialog
    v-model="showdialog"
    persistent
    max-width="400"
    overlay-opacity="0.8"
  >
    <v-card color="app_bg">
      <v-toolbar
        flat
        color="nav_bg"
      >
        <v-spacer />
        <v-toolbar-title>
          <div class="d-flex align-center pl-7">
            <v-img
              class="shrink"
              contain
              :src="logoimg"
              transition="scale-transition"
              :max-width="logoimgw"
            />

            <!-- <span class="ml-2 mb-n2 font-weight-bold text--primary">{{ domainarr[1] }}<small class="primary--text">.{{ domainarr[0] }}</small></span> -->
          </div>
        </v-toolbar-title>
        <v-spacer />
        <v-btn
          icon
          @click="closeloginreg"
        >
          <v-icon>mdi-close</v-icon>
        </v-btn>

        <template v-slot:extension>
          <v-tabs
            v-model="tab"
            centered
          >
            <v-tabs-slider color="" />

            <v-tab :href="'#login'">
              {{ $t('denglu') }}
            </v-tab>
            <v-tab :href="'#reg'">
              {{ $t('zhuce') }}
            </v-tab>
          </v-tabs>
        </template>
      </v-toolbar>
      <v-tabs-items
        v-model="tab"
        class="app_bg"
      >
        <v-tab-item :value="'login'">
          <v-card-text>
            <v-subheader style="height: 32px;">
              {{ $t('shoujihao') }}
            </v-subheader>
            <v-text-field
              v-model.number="phone_login"
              type="number"
              flat
              solo
              background-color="btn_bg"
              prefix="+63"
              :placeholder="$t('qsrsjh')"
              hide-details
            >
              <template #prepend-inner>
                <div style="width:24px">
                  <v-img
                    style="margin: auto 0"
                    max-width="24"
                    src="@/assets/img/icons/flag-php.svg"
                  />
                </div>
              </template>
            </v-text-field>

            <v-subheader style="height: 32px;">
              {{ $t('mima') }}
              <v-spacer />
              <a
                class="text-decoration-none"
                @click="showforget"
              >{{ $t('wjmm') }}?</a>
            </v-subheader>
            <v-text-field
              v-model="password_login"
              flat
              solo
              background-color="btn_bg"
              prepend-inner-icon="mdi-form-textbox-password"
              :append-icon="show2 ? 'mdi-eye' : 'mdi-eye-off'"
              :type="show2 ? 'text' : 'password'"
              :placeholder="$t('qsrmm')"
              hide-details
              @click:append="show2 = !show2"
            />
          </v-card-text>
          <v-card-text>
            <v-btn
              depressed
              block
              color="primary"
              @click="login"
            >
              {{ $t('denglu') }}
            </v-btn>

            <div class="text-center my-3 text--disabled">
              {{ $t('myzh') }}
              <a
                class="text-decoration-none"
                @click="tabtoreg"
              >{{ $t('zhuce') }}</a>
            </div>
          </v-card-text>
        </v-tab-item>
        <v-tab-item :value="'reg'">
          <v-card-text>
            <div class="text-center">
              <div class="mb-n10">
                <img
                  width="80"
                  src="../assets/img/free-bonus.webp"
                >
              </div>

              <div class="rounded-xl freebonus-reg-bg pa-4 pt-10">
                <v-sheet
                  light
                  class="rounded-xl pa-2 opacity-7 text-caption"
                >
                  Get 150% bonus on your first 4 deposits
                </v-sheet>
              </div>
            </div>

            <v-subheader style="height: 28px;">
              {{ $t('shoujihao') }}
            </v-subheader>
            <v-text-field
              v-model.number="phone_reg"
              type="number"
              flat
              solo
              dense
              background-color="btn_bg"
              prefix="+63"
              :placeholder="$t('qsrsjh')"
              hide-details
            >
              <template #prepend-inner>
                <div style="width:24px">
                  <v-img
                    style="margin: auto 0"
                    max-width="24"
                    src="@/assets/img/icons/flag-php.svg"
                  />
                </div>
              </template>
            </v-text-field>

            <v-subheader style="height: 32px;">
              {{ $t('mima') }}
            </v-subheader>
            <v-text-field
              v-model="password_reg"
              flat
              solo
              dense
              background-color="btn_bg"
              prepend-inner-icon="mdi-form-textbox-password"
              :append-icon="show1 ? 'mdi-eye' : 'mdi-eye-off'"
              :type="show1 ? 'text' : 'password'"
              :placeholder="$t('qsrmm')"
              hide-details
              @click:append="show1 = !show1"
            />
            <v-subheader
              v-if="invite_code!=''"
              style="height: 32px;"
            >
              {{ $t('yqm') }}
            </v-subheader>
            <v-text-field
              v-if="invite_code!=''"
              v-model="invite_code"
              flat
              solo
              dense
              background-color="btn_bg"
              readonly
              prepend-inner-icon="mdi-account-multiple"
              :placeholder="$t('sryqm')"
              :hint="$t('rgmyzwxtx')"
              @click:append="show1 = !show1"
            />
            <v-row
              v-if="verify_status"
              dense
            >
              <v-col cols="7">
                <v-text-field
                  v-model="verify_code"
                  flat
                  solo
                  background-color="btn_bg"
                />
              </v-col>
              <v-col
                cols="5"
                class="d-flex"
              >
                <v-img
                  :src="verifySrc"
                  height="40"
                  @click="refeshVerifySrc"
                />
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-text class="pt-0">
            <v-btn
              depressed
              block
              color="primary"
              :loading="regloading"
              @click="reg"
            >
              {{ $t('zhuce') }}
            </v-btn>

            <div class="text-center my-3 text--disabled">
              {{ $t('yyzh') }}
              <a
                class="text-decoration-none"
                @click="tabtologin"
              >{{ $t('denglu') }}</a>
            </div>

            <div class="d-flex justify-center flex-wrap">
              <span
                class="d-flex align-center pa-2 opacity-7"
              >
                <img
                  src="../assets/img/logos/providers/pagcor.png"
                  height="28px"
                  class="mx-auto"
                >
              </span>
              <span
                class="d-flex align-center pa-2 opacity-7"
              >
                <img
                  src="../assets/img/logos/providers/gamingfor.png"
                  height="28px"
                  class="mx-auto"
                >
              </span>
            </div>
          </v-card-text>
        </v-tab-item>
      </v-tabs-items>
    </v-card>
    <v-overlay :value="showprogress">
      <v-progress-circular
        indeterminate
      />
    </v-overlay>
  </v-dialog>
</template>

<script>
import { getEventSender } from '@/utils/sender'
  export default {
    name: 'Login',
    components:{
      
    },
    props: {
      value: {
        type: String,
        required: true
      }
    },
    data () {
      return {
        domainarr: [],
        showdialog: true,
        tab: null,
        show1: false,
        show2: false,
        phone_reg: '',
        password_reg: '',
        phone_login: '',
        password_login: '',
        invite_code: '',
        af_config: '',
        lpid: 0,
        regloading: false,
        showprogress: false,
        logoimg: '',
        logoimgw: 120,
        verify_code: '',
        verifySrc: '',
        v_key: '',
        verify_status: false
      }
    },
    watch:{
      showprogress (val) {
        val && setTimeout(() => {
          this.showprogress = false
        }, 5000)
      },
    },
    created() {
      this.tab=this.value
      console.log(this.tab)
      this.invite_code = sessionStorage.getItem('code')
      this.af_config = localStorage.getItem('af_config')
      this.lpid = localStorage.getItem('lpid')

      this.$server.conf().then((response) => {
        this.domainarr = response.data.logo.split('.').reverse()
        this.logoimg = response.data.logoimg
        this.logoimgw = response.data.logoimgw
      })
    },
    mounted() {
      this.verify()
    },
    methods: {
      tabtoreg(){
        this.tab='reg'
        this.phone_login=''
        this.password_login=''
      },
      tabtologin(){
        this.tab='login'
        this.phone_reg=''
        this.password_reg=''
      },
      closeloginreg() {
        this.showdialog = false
        this.$emit('closeloginreg')
      },
      showforget() {
        this.$emit('showforget')
        this.closeloginreg()
      },
      verify() {
        let paramObj = {

        }
        this.$server.verify(paramObj).then((response) => {
          if(response.code==200){
            this.verifySrc = response.data.data.img
            this.v_key = response.data.data.key
            this.verify_status = response.data.verify_status
          }else{
            
          }
        })
      },
      refeshVerifySrc() {
        this.verify()
      },
      reg() {
        var phone_regExp = /^\d{6,13}$/
        if (!phone_regExp.test(this.phone_reg)) {
          this.$snackbar.warning(this.$t('sjhmgscw'))
          return false;
        }
        if (this.password_reg == ''){
          this.$snackbar.warning(this.$t('qsrmm'))
          return false;
        }
        this.regloading = true
        this.showprogress = true
        let paramObj = {
          v_key: this.v_key,
          verify_code: this.verify_code,
          phone: this.phone_reg,
          password: this.password_reg,
          invite_code: this.invite_code,
          af_config: this.af_config,
          fp: this.$store.state.visitorId,
          lpid: this.lpid,
        };
        this.$server.reg(paramObj).then((response) => {
          if(response.code==200){

            if(response.data.pixel!='' && response.data.chnl!='GG'){
              if(response.data.pixel=='1337866743597458'){
                this.$analytics.fbq.init(response.data.pixel, {
                  external_id: response.data.uuid
                })
              }else{
                this.$analytics.fbq.init(response.data.pixel, {
                  ph: '63'+response.data.phone
                })
              }
              this.$analytics.fbq.event('CompleteRegistration')
            }

            this.sendCompleteRegistration()
            this.setStorage('member_id', response.data.member_id)
            this.setStorage('token', response.data.token)
            this.closeloginreg()
            this.showprogress = false
            this.regloading = false
            this.setStorage('popuptime', 1)
            this.setStorage('bonus777time', 1)
            setTimeout(() => {
              location.reload()
            }, 4000)
          }else if(response.code==902){
            this.showprogress = false
            this.$snackbar.warning(this.$t('hmycz'))
            this.regloading = false
          }else{
            this.showprogress = false
            this.$snackbar.warning(this.$t('zcsb'))
            this.regloading = false
          }
        })
      },
      sendCompleteRegistration()
      {
        let event_sender = getEventSender()
        console.log(event_sender)
        if(event_sender)
        {
          event_sender("af_complete_registration","")
        }
      },
      login() {
        var phone_regExp = /^\d{6,13}$/
        if (!phone_regExp.test(this.phone_login)) {
          this.$snackbar.warning(this.$t('sjhmgscw'))
          return false;
        }
        if (this.password_login == ''){
          this.$snackbar.warning(this.$t('qsrmm'))
          return false;
        }
        this.showprogress = true
        let paramObj = {
          phone: this.phone_login,
          password: this.password_login
        }
        this.$server.login(paramObj).then((response) => {
          if(response.code==200){
            this.setStorage('member_id', response.data.member_id)
            this.setStorage('token', response.data.token)
            this.showprogress = false
            location.reload()
          }else if(response.code==904){
            this.showprogress = false
            this.$snackbar.warning(this.$t('dlsbmmcw'))
          }else if(response.code==905){
            this.showprogress = false
            this.$snackbar.warning(this.$t('dlsbbcz'))
          }else if(response.code==940){
            this.showprogress = false
            this.$snackbar.warning('Your account has been suspended.')
          }else{
            this.showprogress = false
            this.$snackbar.warning(this.$t('dlsb'))
          }
        })
      }
    }
  }
</script>

<style>
.freebonus-reg-bg {
  background-color: #ffffff!important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100%25' height='100%25' viewBox='0 0 800 800'%3E%3Cdefs%3E%3CradialGradient id='a' cx='400' cy='400' r='50%25' gradientUnits='userSpaceOnUse'%3E%3Cstop offset='0' stop-color='%23ffffff'/%3E%3Cstop offset='1' stop-color='%23FFD64E'/%3E%3C/radialGradient%3E%3CradialGradient id='b' cx='400' cy='400' r='70%25' gradientUnits='userSpaceOnUse'%3E%3Cstop offset='0' stop-color='%23ffffff'/%3E%3Cstop offset='1' stop-color='%23FFF398'/%3E%3C/radialGradient%3E%3C/defs%3E%3Crect fill='url(%23a)' width='800' height='800'/%3E%3Cg fill-opacity='.8'%3E%3Cpath fill='url(%23b)' d='M998.7 439.2c1.7-26.5 1.7-52.7 0.1-78.5L401 399.9c0 0 0-0.1 0-0.1l587.6-116.9c-5.1-25.9-11.9-51.2-20.3-75.8L400.9 399.7c0 0 0-0.1 0-0.1l537.3-265c-11.6-23.5-24.8-46.2-39.3-67.9L400.8 399.5c0 0 0-0.1-0.1-0.1l450.4-395c-17.3-19.7-35.8-38.2-55.5-55.5l-395 450.4c0 0-0.1 0-0.1-0.1L733.4-99c-21.7-14.5-44.4-27.6-68-39.3l-265 537.4c0 0-0.1 0-0.1 0l192.6-567.4c-24.6-8.3-49.9-15.1-75.8-20.2L400.2 399c0 0-0.1 0-0.1 0l39.2-597.7c-26.5-1.7-52.7-1.7-78.5-0.1L399.9 399c0 0-0.1 0-0.1 0L282.9-188.6c-25.9 5.1-51.2 11.9-75.8 20.3l192.6 567.4c0 0-0.1 0-0.1 0l-265-537.3c-23.5 11.6-46.2 24.8-67.9 39.3l332.8 498.1c0 0-0.1 0-0.1 0.1L4.4-51.1C-15.3-33.9-33.8-15.3-51.1 4.4l450.4 395c0 0 0 0.1-0.1 0.1L-99 66.6c-14.5 21.7-27.6 44.4-39.3 68l537.4 265c0 0 0 0.1 0 0.1l-567.4-192.6c-8.3 24.6-15.1 49.9-20.2 75.8L399 399.8c0 0 0 0.1 0 0.1l-597.7-39.2c-1.7 26.5-1.7 52.7-0.1 78.5L399 400.1c0 0 0 0.1 0 0.1l-587.6 116.9c5.1 25.9 11.9 51.2 20.3 75.8l567.4-192.6c0 0 0 0.1 0 0.1l-537.3 265c11.6 23.5 24.8 46.2 39.3 67.9l498.1-332.8c0 0 0 0.1 0.1 0.1l-450.4 395c17.3 19.7 35.8 38.2 55.5 55.5l395-450.4c0 0 0.1 0 0.1 0.1L66.6 899c21.7 14.5 44.4 27.6 68 39.3l265-537.4c0 0 0.1 0 0.1 0L207.1 968.3c24.6 8.3 49.9 15.1 75.8 20.2L399.8 401c0 0 0.1 0 0.1 0l-39.2 597.7c26.5 1.7 52.7 1.7 78.5 0.1L400.1 401c0 0 0.1 0 0.1 0l116.9 587.6c25.9-5.1 51.2-11.9 75.8-20.3L400.3 400.9c0 0 0.1 0 0.1 0l265 537.3c23.5-11.6 46.2-24.8 67.9-39.3L400.5 400.8c0 0 0.1 0 0.1-0.1l395 450.4c19.7-17.3 38.2-35.8 55.5-55.5l-450.4-395c0 0 0-0.1 0.1-0.1L899 733.4c14.5-21.7 27.6-44.4 39.3-68l-537.4-265c0 0 0-0.1 0-0.1l567.4 192.6c8.3-24.6 15.1-49.9 20.2-75.8L401 400.2c0 0 0-0.1 0-0.1L998.7 439.2z'/%3E%3C/g%3E%3C/svg%3E");
  background-size: cover!important;
}
</style>