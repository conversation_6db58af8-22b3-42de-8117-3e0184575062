<template>
  <v-dialog
    v-model="showdialog"
    max-width="600px"
    content-class="chat-dialog overflow-visible"
  >
    <v-btn
      icon
      dark
      fixed
      right
      class="mt-n6 mr-0 opacity-9"
      style="z-index: 1000002;"
      @click="closechat"
    >
      <v-icon large>
        mdi-close-circle
      </v-icon>
    </v-btn>
    <v-card>
      <div class="chat-container">
        <iframe
          width="100%"
          height="100%"
          style="border: none;"
          :src="chaturl"
        />
      </div>
    </v-card>
  </v-dialog>
</template>
  <script>
  export default {
    name: 'Chat',
    components:{
    },
    props: {
      value: {
        type: String,
        required: true
      }
    },
    data: () => ({
      showdialog: true,
      dialogalert: false,
      resmsg: '',
      chaturl: ''
    }),
    computed: {
  
    },
    watch:{
      showdialog: function(nv,ov){
        console.log(nv,ov)
        this.closechat()
      },
    },
    created() {
      this.chaturl = '/chat/index.html?t='+this.value
    },
    mounted() {
      // 添加消息监听器
      window.addEventListener('message', this.handleMessage);
    },
    beforeDestroy() {
      // 组件销毁前移除监听器
      window.removeEventListener('message', this.handleMessage);
    },
    methods: {
      handleMessage(event) {
        // 处理来自iframe的消息
        if (event.data.type === 'chat') {
          // 处理数据
          console.log('收到iframe消息：', event.data);
        }
      },
      closechat() {
        this.showdialog = false
        this.$emit('closechat')
      }
    },
  };
  </script>

<style scoped>
.chat-dialog {
  margin: 0;
  height: auto !important;
}

.chat-container {
  position: relative;
  width: 100%;
  height: 80vh; /* 使用更高的百分比确保底部内容可见 */
  overflow: hidden;
}

@media screen and (max-width: 600px) {
  .chat-container {
    height: 70vh; /* 在小屏幕上使用更合适的高度 */
  }
}

/* iOS特定样式 */
@supports (-webkit-touch-callout: none) {
  .chat-container {
    height: 600px; /* 在iOS上使用固定像素高度而非vh单位 */
    max-height: 70vh; /* 限制最大高度 */
  }
}
</style>