<template>
  <v-container class="app_bg pb-16">
    <v-subheader class="px-0">
      {{ $t('qbye') }}
      <v-spacer />
      <div class="d-flex align-center justify-center">
        <v-img
          width="16"
          src="../assets/img/icons/PHP.png"
        />
        <strong class="orange--text mx-1">{{ meminfo.assets.available }}</strong>
        <span>PHP</span>
      </div>
    </v-subheader>

    <div v-if="meminfo.game_balance>0">
      <v-divider class="opacity-3" />
      <div
        class="my-2 d-flex justify-space-between"
      >
        <span>
          <small class="text--disabled">{{ meminfo.game_platform }}</small>
          <v-chip
            small
            outlined
            color="orange"
            class="ml-1"
          >
            ₱ {{ meminfo.game_balance }}
          </v-chip>
        </span>

        <v-btn
          small
          depressed
          color="primary"
          @click="collection = true"
        >
          <v-icon
            small
            left
          >
            mdi-restore
          </v-icon>
          {{ $t('yjgj') }}
        </v-btn>
      </div>
    </div>

    <v-divider class="opacity-3" />
    
    <v-subheader class="px-0">
      {{ $t('wdjj') }}
      <v-spacer />
      <a
        class="text-decoration-none text--disabled"
        @click="promohistory"
      >
        <i class="mdi mdi-history" />
        {{ $t('lsjl') }}
      </a>
    </v-subheader>
    
    <v-sheet
      v-if="Object.keys(current_promo).length != 0"
      light
      color="yellow lighten-5"
      class="rounded-lg mt-n2"
      elevation="3"
    >
      <v-list-item three-line>
        <v-list-item-avatar class="mr-2">
          <v-icon color="primary">
            mdi-gift-open
          </v-icon>
        </v-list-item-avatar>
        <v-list-item-content>
          <v-list-item-title>
            <a @click="task_detail = true">
              {{ current_promo.promo_name }}
            </a>
          </v-list-item-title>
          <v-progress-linear
            rounded
            striped
            :value="current_promo.progress"
            color="green"
            class="my-1"
          />
          <div class="d-flex align-center justify-space-between grey--text text-caption">
            <small>{{ current_promo.progress }}%</small>
            <small class="text-right">
              ₱ 
              <span class="orange--text">{{ current_promo.finished_amount }}</span>
              / {{ current_promo.total_amount }}
            </small>
          </div>
          <v-divider class="opacity-3 my-2" />
          <div
            v-if="current_promo.status==1"
            class="d-flex align-center justify-space-between"
          >
            <small class="orange--text">
              <v-icon
                small
                color="orange"
              >mdi-timer-sand-complete</v-icon>
              {{ $t('jxz') }} ...
            </small>
            <v-btn
              small
              depressed
              rounded
              dark
              color="orange"
              @click="fangqi"
            >
              {{ $t('fangqi') }}
            </v-btn>
          </div>
        </v-list-item-content>
      </v-list-item>
    </v-sheet>

    <v-subheader class="px-0">
      {{ $t('hdlb') }}
      <v-spacer />
      <a
        class="text-decoration-none text--disabled"
        @click="task_rule = true"
      >
        <i class="mdi mdi-tooltip-question-outline" />
        {{ $t('gzsm') }}
      </a>
    </v-subheader>

    <v-row dense>
      <v-col
        cols="6"
      >
        <v-list-item
          dense
          light
          class="rounded-pill brown--text text-center box-no-bg mb-3"
          style="background: linear-gradient(56deg, rgb(149, 116, 45), rgb(245, 216, 138) 35%, rgb(193, 148, 53) 75%, rgb(241, 208, 131));"
          @click="goose_open"
        >
          <v-list-item-content>
            <v-list-item-title>Lucky Mission Draw</v-list-item-title>
          </v-list-item-content>
        </v-list-item>
      </v-col>
      <v-col
        cols="6"
      >
        <v-list-item
          dense
          dark
          class="rounded-pill white--text text-center box-no-bg"
          style="background: linear-gradient(56deg, rgb(180 141 241), rgb(106 44 159) 34%, rgb(119 22 216) 67%, rgb(80 17 148));"
          @click="gotofreespin"
        >
          <v-list-item-content>
            <v-list-item-title>{{ $t('freespins_t1') }}</v-list-item-title>
          </v-list-item-content>
        </v-list-item>
      </v-col>
    </v-row>



    <div class="task_list">
      <v-hover
        v-for="(item, index) in promoslist"
        v-slot="{ hover }"
        :key="'p_'+index"
      >
        <v-sheet
          rounded="lg"
          :elevation="hover ? 3 : 1"
          color="box_bg"
          class="mb-3"
        >
          <v-list-item two-line>
            <v-list-item-avatar class="mr-2">
              <v-icon>mdi-gift</v-icon>
            </v-list-item-avatar>
            <v-list-item-content>
              <v-list-item-title>
                <a @click="canjia(item)">
                  {{ item.promo_name }}
                </a>
              </v-list-item-title>
              <div class="text-right">
                <v-btn
                  small
                  depressed
                  rounded
                  color="primary"
                  @click="canjia(item)"
                >
                  {{ $t('canjia') }}
                </v-btn>
              </div>
            </v-list-item-content>
          </v-list-item>
        </v-sheet>
      </v-hover>
    </div>

    <v-dialog
      v-model="collection"
      max-width="300"
      scrollable
    >
      <v-card class="box_bg">
        <v-card-title class="text-subtitle-1">
          <v-spacer />
          {{ $t('qdyezh') }}？
          <v-spacer />
        </v-card-title>
        <v-divider class="opacity-3" />
        <v-card-actions class="pa-3">
          <v-spacer />
          <v-btn
            outlined
            color="green darken-1"
            @click="collection = false"
          >
            {{ $t('quxiao') }}
          </v-btn>
          <v-spacer />
          <v-btn
            depressed
            dark
            color="green darken-1"
            :disabled="collectdisabled"
            @click="usercollect"
          >
            {{ $t('queren') }}
          </v-btn>
          <v-spacer />
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog
      v-model="task_detail"
      max-width="300"
    >
      <v-card class="box_bg">
        <v-card-title class="text-subtitle-1 nav_bg">
          {{ current_promo.promo_name }}
        </v-card-title>
        <v-card-text class="pt-4">
          <v-list
            dense
            class="transparent pa-0"
          >
            <v-list-item class="pa-0 text--disabled">
              <v-list-item-content>{{ $t('cyje') }}</v-list-item-content>
              <v-list-item-action>₱ {{ current_promo.participation_amount }}</v-list-item-action>
            </v-list-item>
            <v-list-item class="pa-0 text--disabled">
              <v-list-item-content>{{ $t('jjbl') }}</v-list-item-content>
              <v-list-item-action>{{ current_promo.bonus_ratio }}%</v-list-item-action>
            </v-list-item>
            <v-list-item class="pa-0 text--disabled">
              <v-list-item-content>{{ $t('jjje') }}</v-list-item-content>
              <v-list-item-action><div>₱ <span class="orange--text">{{ current_promo.gift_amount }}</span></div></v-list-item-action>
            </v-list-item>
            <v-list-item class="pa-0 text--disabled">
              <v-list-item-content>{{ $t('lsyq') }}</v-list-item-content>
              <v-list-item-action>₱ {{ current_promo.total_amount }}</v-list-item-action>
            </v-list-item>
            <v-list-item class="pa-0 text--disabled">
              <v-list-item-content>{{ $t('rwjd') }}</v-list-item-content>
              <v-list-item-action>
                {{ current_promo.progress }}%
                <v-list-item-action-text>
                  ₱ 
                  <span class="orange--text">{{ current_promo.finished_amount }}</span>
                  / {{ current_promo.total_amount }}
                </v-list-item-action-text>
              </v-list-item-action>
            </v-list-item>
          </v-list>
        </v-card-text>
        <v-divider class="opacity-3" />
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="green darken-1"
            text
            @click="task_detail = false"
          >
            {{ $t('zdl') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog
      v-model="task_ing"
      max-width="300"
    >
      <v-card class="box_bg">
        <v-card-title class="text-subtitle-1 nav_bg">
          {{ $t('yswwcdjjrw') }}!
        </v-card-title>
        <v-card-text class="pt-4">
          {{ $t('tszncjyg') }}
        </v-card-text>
        <v-divider class="opacity-3" />
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="green darken-1"
            text
            @click="zdling"
          >
            {{ $t('zdl') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog
      v-model="task_end"
      max-width="300"
    >
      <v-card class="box_bg">
        <v-card-title class="text-subtitle-1 nav_bg">
          {{ $t('qrfqgjjrw') }}?
        </v-card-title>
        <v-card-text class="pt-4">
          
        </v-card-text>
        <v-divider class="opacity-3" />
        <v-card-actions>
          <v-btn
            text
            @click="task_end = false"
          >
            {{ $t('quxiao') }}
          </v-btn>
          <v-spacer />
          <v-btn
            color="green darken-1"
            text
            @click="abandon(current_promo)"
          >
            {{ $t('queren') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog
      v-model="task_rule"
      max-width="300"
      scrollable
    >
      <v-card color="box_bg">
        <v-card-title class="text-subtitle-1">
          {{ $t('gzsm') }}
        </v-card-title>
        <v-card-text class="pb-0">
          <ul>
            <li>{{ $t('gz1') }}</li>
            <li>{{ $t('gz2') }}</li>
            <li>{{ $t('gz3') }}</li>
            <li>{{ $t('gz4') }}</li>
          </ul>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="green darken-1"
            text
            @click="task_rule = false"
          >
            {{ $t('zdl') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog
      v-model="task_join"
      max-width="400"
      scrollable
    >
      <v-card color="box_bg">
        <v-card-title class="text-subtitle-1 nav_bg">
          {{ choose_item.promo_name }}
        </v-card-title>
        <v-card-title class="text-body-2 nav_bg">
          {{ $t('qbye') }}
          <v-spacer />
          <span class="orange1--text shake-animation">₱ <strong>{{ meminfo.assets.available }}</strong></span>
        </v-card-title>
        <v-card-text class="pb-0">
          <v-list
            dense
            color="box_bg"
          >
            <v-list-item class="px-0">
              <v-list-item-content>{{ $t('cyje') }}</v-list-item-content>
              <v-list-item-content>
                <v-text-field
                  v-model.number="participation_amount"
                  type="number"
                  dense
                  filled
                  solo
                  flat
                  :hide-details="(participation_amount>meminfo.assets.available)?false:true"
                  :error-messages="(participation_amount>meminfo.assets.available)?$t('qbyebz'):''"
                  :readonly="participation_amount_readonly"
                  background-color="nav_bg"
                  color="grey"
                  prefix="₱"
                  suffix="PHP"
                  class="grey--text text-body-2"
                />
              </v-list-item-content>
            </v-list-item>
            <v-list-item class="px-0">
              <v-list-item-content>{{ $t('jjbl') }}</v-list-item-content>
              <v-list-item-action>{{ choose_item.conf.bonus_ratio?choose_item.conf.bonus_ratio:parseInt(choose_item.conf.bonus/choose_item.conf.deposit*100) }}%</v-list-item-action>
            </v-list-item>
            <v-list-item class="px-0">
              <v-list-item-content>{{ $t('jjje') }}</v-list-item-content>
              <v-list-item-action><span>₱ <strong class="orange--text">{{ only1time_bonus }}</strong></span></v-list-item-action>
            </v-list-item>
            <v-list-item class="px-0">
              <v-list-item-content>{{ $t('lsyq') }}</v-list-item-content>
              <v-list-item-action>₱ {{ only1time_require }}</v-list-item-action>
            </v-list-item>
          </v-list>
          <p class="text--disabled">
            *{{ $t('wctzedhkhdjj') }}.
          </p>
        </v-card-text>
        <v-divider class="opacity-3" />
        <v-card-actions>
          <v-btn
            text
            @click="task_join = false"
          >
            {{ $t('quxiao') }}
          </v-btn>
          <v-spacer />
          <v-btn
            color="green darken-1"
            text
            :disabled="Number(participation_amount)>Number(meminfo.assets.available)"
            @click="participate(choose_item)"
          >
            {{ $t('queren') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-snackbar
      v-model="snackbar"
      centered
    >
      {{ $t('cglq') }} <strong class="orange--text">₱ {{ gift_amount }} PHP</strong> {{ $t('jiangjin') }}

      <template v-slot:action="{ attrs }">
        <v-btn
          color=""
          text
          v-bind="attrs"
          @click="snackbar = false"
        >
          {{ $t('guanbi') }}
        </v-btn>
      </template>
    </v-snackbar>
    <v-overlay :value="showprogress">
      <v-progress-circular
        indeterminate
      />
    </v-overlay>

    <template v-if="goose">
      <Eggs
        @goose_close="goose_close"
      />
    </template>
  </v-container>
</template>
<script>
import store from "../store/"
import Eggs from '@/components/Eggs.vue'
export default {
  name: 'Promo',
  components:{
    Eggs
  },
  props: {

  },
  data: () => ({
    task_detail: false,
    task_join: false,
    task_ing: false,
    task_end: false,
    task_rule: false,
    snackbar: false,
    show: false,
    collection: false,

    meminfo:{
      assets: {
        available:0,
      },
      game_balance:0,
      game_platform:'',
      game_platform_id:0
    },
    balance: 0,
    collectdisabled: false,
    promoslist: [],
    current_promo: {},
    total_doing: 0,
    total_receive: 0,
    choose_item: {
      conf:{}
    },
    participation_amount: '',
    participation_amount_readonly: false,
    gift_amount: '',
    showprogress: false,
    goose: false,
  }),
  computed: {
    only1time_bonus: function() {
      if(this.choose_item.conf.frequency=='only1time'){
        return Math.min(Number(this.participation_amount * this.choose_item.conf.bonus_ratio / 100), Number(this.choose_item.conf.bonus_max))
      }else{
        return Number(this.choose_item.conf.bonus)
      }
    },
    only1time_require: function() {
      return this.only1time_bonus * this.choose_item.conf.multiple
    }
  },
  watch:{
    '$store.state.balance': function (newVal) {
      this.balance = newVal
    },
    showprogress (val) {
      val && setTimeout(() => {
        this.showprogress = false
      }, 5000)
    },
  },
  created() {
    this.getmeminfo()
    this.myaward()
    this.promos()
  },
  mounted() {

  },
  methods: {
    getmeminfo() {
      let paramObj = {
        member_id: this.getStorage('member_id'),
      }
      this.$server.getmeminfo(paramObj).then((response) => {
        if(response.code==200){
          this.meminfo = response.data
          this.balance = response.data.total_balance
          this.game_balance = response.data.game_balance
          this.game_platform = response.data.game_platform
          this.game_platform_id = response.data.game_platform_id
        }
      })
    },
    usercollect() {
      this.collectdisabled = true
      this.showprogress = true
      let paramObj = {
        member_id: this.getStorage('member_id'),
        platform_id: this.meminfo.game_platform_id
      }
      this.$server.usercollect(paramObj).then((response) => {
        this.showprogress = false
        if(response.code==200){
          this.getmeminfo()
          this.collectdisabled = false
          this.collection = false
        }
      })
    },
    myaward() {
      let paramObj = {
        member_id: this.getStorage('member_id'),
      }
      this.$server.myaward(paramObj).then((response) => {
        if(response.code==200){
          this.current_promo = response.data.current_promo
          this.total_receive = response.data.total_receive
          this.total_doing = response.data.total_doing
        }
      })
    },
    promos() {
      let paramObj = {
        member_id: this.getStorage('member_id'),
      }
      this.$server.promos(paramObj).then((response) => {
        if(response.code==200){
          this.promoslist = response.data.canparticipates
        }
      })
    },
    canjia(item) {
      this.choose_item = item
      if(item.conf.frequency!='only1time'){
        this.participation_amount_readonly = true
        this.participation_amount = item.conf.deposit
      }else{
        this.participation_amount_readonly = false
        this.participation_amount = ''
      }
      this.task_join = true
    },
    participate(promo) {
      if(promo.conf.frequency=='only1time' && Number(this.participation_amount)<Number(promo.conf.participation_amount_min)){
        this.$snackbar.warning(this.$t('cjjezsw')+promo.conf.participation_amount_min)
        return false
      }
      if(Number(this.participation_amount)>Number(this.meminfo.assets.available)){
        this.$snackbar.warning(this.$t('qbyebz'))
        return false
      }
      this.showprogress = true
      let paramObj = {
        member_id: this.getStorage('member_id'),
        promo_id: promo.id,
        participation_amount: this.participation_amount
      }
      this.$server.participate2(paramObj).then((response) => {
        this.getmeminfo()
        this.myaward()
        this.promos()
        this.showprogress = false
        if(response.code==200){
          this.task_join = false
        }else{
          if(response.code==922){
            this.task_ing = true
          }
          this.$snackbar.warning(response.msg)
        }
      })
    },
    fangqi() {
      this.task_end = true
    },
    abandon(promo_log) {
      if(this.balance>20){
        this.$snackbar.warning('Can not give up for now')
        return
      }
      this.showprogress = true
      let paramObj = {
        member_id: this.getStorage('member_id'),
        promo_log_id: promo_log.id
      }
      this.$server.abandon(paramObj).then((response) => {
        this.showprogress = false
        if(response.code==200){
          this.task_end = false
          this.getmeminfo()
          this.myaward()
          this.promos()
        }else{
          this.$snackbar.warning(response.msg)
        }
      })
    },
    receive(promo_log) {
      this.showprogress = true
      let paramObj = {
        member_id: this.getStorage('member_id'),
        promo_log_id: promo_log.id
      }
      this.$server.receive(paramObj).then((response) => {
        this.showprogress = false
        if(response.code==200){
          this.gift_amount = promo_log.gift_amount
          this.snackbar = true
          this.getmeminfo()
          this.myaward()
          this.promos()
        }else{
          this.$snackbar.warning(response.msg)
        }
      })
    },
    zdling() {
      this.task_ing = false
      this.task_join = false
    },
    promohistory() {
      this.$emit('promohistory')
    },
    goose_open() {
      //this.goose = true
      store.commit('luckywheel', true)
    },
    goose_close() {
      this.goose = false
    },
    gotofreespin() {
      this.$router.push({
        name: 'FreeSpins',
      })
      this.$emit('closeprofile')
    }
  },
};
</script>

<style>
  @keyframes shake {
    0% {
      transform: translateX(0);
    }
    25% {
      transform: translateX(-10px);
    }
    50% {
      transform: translateX(0);
    }
    75% {
      transform: translateX(-10px);
    }
    100% {
      transform: translateX(0);
    }

  }

  .shake-animation {
    animation: shake 1s ease-in-out;
  }
</style>