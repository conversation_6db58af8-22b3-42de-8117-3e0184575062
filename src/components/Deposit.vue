<template>
  <div>
    <v-card-text>
      <div class="input-block">
        <div class="d-flex align-center justify-space-between text--disabled mb-2">
          <span>{{ $t('kyye') }}</span>
          <div class="d-flex align-center justify-center">
            <v-img
              width="16"
              src="../assets/img/icons/PHP.png"
            />
            <strong class="orange--text mx-1">{{ balance }}</strong>
            <span>PHP</span>
            <v-btn
              icon
              @click="show = !show"
            >
              <v-icon color="grey">
                {{ show ? 'mdi-chevron-up-circle-outline' : 'mdi-chevron-down-circle-outline' }}
              </v-icon>
            </v-btn>
          </div>
        </div>
        <v-expand-transition>
          <div v-show="show">
            <div
              v-if="meminfo.game_balance>0"
              class="ma-2 text-right"
            >
              <v-btn
                small
                depressed
                color="primary"
                @click="collection = true"
              >
                {{ $t('yjgj') }}
              </v-btn>
            </div>

            <v-divider class="opacity-3" />
            <v-card-text
              class="text-right pa-0"
            >
              <div class="ma-2">
                <small>{{ $t('qbye') }}:</small>
                <v-chip
                  small
                  outlined
                  class="ml-2"
                >
                  ₱ {{ meminfo.assets.available }}
                </v-chip>
              </div>
              <div
                v-if="meminfo.game_balance>0"
                class="ma-2"
              >
                <small>{{ meminfo.game_platform }}:</small>
                <v-chip
                  small
                  outlined
                  class="ml-2"
                >
                  ₱ {{ meminfo.game_balance }}
                </v-chip>
              </div>
            </v-card-text>
            <v-divider class="opacity-3 mb-2" />
          </div>
        </v-expand-transition>

        <div class="mb-2 text--disabled">
          {{ $t('zffs') }}
        </div>
        <v-sheet
          v-show="show_payments"
          rounded
          color="transparent"
        >
          <v-container>
            <v-alert
              v-if="alertmsg"
              dense
              type="warning"
            >
              {{ alertmsg }}
            </v-alert>
            <v-row>
              <v-col
                v-for="(item, key) in payments_arr"
                :key="'payment'+key"
                cols="12"
                class="pa-1"
              >
                <a
                  class="text-decoration-none"
                  @click="select_payment(item)"
                >
                  <v-sheet
                    rounded
                    class="btn_bg pa-1 text--disabled"
                  >
                    <v-list-item class="px-2">
                      <v-list-item-avatar>
                        <v-img
                          v-if="item?.code?.includes('Gcash')"
                          src="../assets/img/logos/gcash.webp"
                        />
                        <v-img
                          v-if="item?.code?.includes('Maya')"
                          src="../assets/img/logos/maya.webp"
                        />
                      </v-list-item-avatar>

                      <v-list-item-content>
                        <v-list-item-title class="text-body-2">{{ item.name }}</v-list-item-title>
                      </v-list-item-content>

                      <v-list-item-icon>
                        <small
                          class="text--disabled mr-2"
                        >{{ item.min_amount }} ~ {{ item.max_amount }} PHP</small>
                        <v-icon color="grey">
                          mdi-chevron-right
                        </v-icon>
                      </v-list-item-icon>
                    </v-list-item>
                  </v-sheet>
                </a>
              </v-col>

              <v-col
                v-if="hasdeposit==false"
                cols="12"
                class="pa-1 mt-3"
              >
                <v-alert
                  dense
                  text
                  color="primary"
                  icon="mdi-gift-open"
                >
                  {{ $t('free5bonus') }}
                </v-alert>
              </v-col>
            </v-row>
          </v-container>
        </v-sheet>
        <v-sheet
          v-show="!show_payments"
          rounded
          class="btn_bg"
        >
          <v-list-item
            dense
            @click="show_payments = !show_payments"
          >
            <v-list-item-avatar size="32">
              <v-img
                v-if="payment_selected?.code?.includes('Gcash')"
                src="../assets/img/logos/gcash.webp"
              />
              <v-img
                v-if="payment_selected?.code?.includes('Maya')"
                src="../assets/img/logos/maya.webp"
              />
            </v-list-item-avatar>

            <v-list-item-content>
              <v-list-item-title class="text-body-2">
                {{ payment_selected.name }}
              </v-list-item-title>
            </v-list-item-content>

            <v-list-item-action>
              <div>
                <small
                  class="text--disabled mr-2"
                >{{ payment_selected.min_amount }} ~ {{ payment_selected.max_amount }} PHP</small>
                <v-icon color="grey">
                  mdi-chevron-right
                </v-icon>
              </div>
            </v-list-item-action>
          </v-list-item>
        </v-sheet>
      </div>

      <div
        v-show="!show_payments"
        class="input-block mt-3"
      >
        <v-card
          v-if="hasfree && freespintimes>0"
          flat
          transition="scroll-y-transition"
          class="mb-2 pa-1 rounded-pill text-center"
          style="background: linear-gradient(56deg, #ffb829, rgb(198 102 165) 34%, rgb(226 61 176) 67%, rgb(106 27 154));"
          @click="gotofreespin"
        >
          {{ $t('freespins_dtip1') }}
          <strong class="yellow--text mx-1">{{ freespintimes }}</strong>
          {{ $t('freespins_dtip2') }}
        </v-card>

        <v-row dense>
          <v-col
            v-for="amount in amounts"
            :key="'amount'+amount"
            cols="4"
            md="3"
            @click="selectamount(amount)"
          >
            <div
              class="pa-1 ma-1 text-center rounded-lg btn_bg sum-box"
              :class="(select_amount==amount)?'active':''"
            >
              <span>₱ {{ amount }}</span>
              <v-chip
                v-if="first_selected"
                x-small
                color="nav_bg"
                text-color="orange"
                input-value="true"
                class="sum-chip"
              >
                +₱ {{ amount*bonus_ratio }}
              </v-chip>
              <!--
              <v-chip
                v-if="hasdeposit==false"
                x-small
                color="nav_bg"
                text-color="orange"
                input-value="true"
                class="sum-chip2"
              >
                +₱ {{ getBonus(amount) }}
              </v-chip>
              -->
              <v-icon
                v-if="select_amount==amount"
                color="primary"
                class="sum-check"
              >
                mdi-check-circle
              </v-icon>
            </div>
          </v-col>
        </v-row>

        <v-text-field
          v-model.number="select_amount"
          type="number"
          filled
          solo
          flat
          dense
          background-color="btn_bg"
          :error-messages="select_amount_error"
          hide-details="auto"
          :prefix="$t('ckje') + ' (₱)'"
          :suffix="(allow_first && first_selected)?($t('zengsong')+' +₱'+gift_amount):''"
          :placeholder="$t('qsrje')"
          class="grey--text text-body-2 mt-4"
        />

        <v-btn
          v-show="!show_payments"
          depressed
          block
          large
          color="primary"
          class="text-none mt-4"
          :disabled="depositdisabled"
          @click="deposit"
        >
          {{ $t('queding') }}
        </v-btn>
        <!--
        <v-checkbox
          v-if="hasdeposit==false"
          v-model="defaultfirst"
          dense
          :label="'₱'+getBonus(select_amount)+' bonus on your First Deposit.(No bets needed)'"
          hide-details
          class="text-body-2"
        />
        -->
        <v-checkbox
          v-show="allow_first"
          v-model="first_selected"
          dense
          :label="$t('cjsczs100')"
          hide-details
          class="text-body-2"
        />
      </div>
    </v-card-text>

    <v-dialog
      v-model="collection"
      max-width="400"
      scrollable
    >
      <v-card class="box_bg">
        <v-card-title class="text-subtitle-1">
          <v-spacer />
          {{ $t('qdyezh') }}？
          <v-spacer />
        </v-card-title>
        <v-divider class="opacity-3" />
        <v-card-actions class="pa-3">
          <v-spacer />
          <v-btn
            outlined
            color="green darken-1"
            class="px-10"
            @click="collection = false"
          >
            {{ $t('quxiao') }}
          </v-btn>
          <v-spacer />
          <v-btn
            depressed
            dark
            color="green darken-1"
            class="px-10"
            :disabled="collectdisabled"
            @click="usercollect"
          >
            {{ $t('queren') }}
          </v-btn>
          <v-spacer />
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-overlay :value="showprogress">
      <v-progress-circular
        indeterminate
      />
    </v-overlay>
    <template v-if="marquee_details">
      <Firstd
        @firstd_close="firstd_close"
      />
    </template>
  </div>
</template>
<script>
import Firstd from '@/components/Firstd.vue'
export default {
  name: 'Deposit',
  components:{
    Firstd
  },
  props: {

  },
  data: () => ({
    marquee_details: false,
    meminfo:{
      assets: {
        available:0,
      },
      game_balance:0,
      game_platform:'',
      game_platform_id:0
    },
    payments_arr: [

    ],
    collection: false,
    show_payments: true,
    payment_selected: {},
    select_amount: 200,
    select_amount_error: '',
    allow_first: true,
    hasdeposit: true,
    defaultfirst: true,
    first_selected: false,
    bonus_max: 0,
    bonus_ratio: 0,
    balance: 0,
    show: false,
    collectdisabled: false,
    depositdisabled: false,
    showprogress: false,
    hasfree: false,
    freetimes: {},
    freespintimes: 0,
    alertmsg: ''
  }),
  computed: {
    amounts() {
        if (!this.payment_selected?.min_amount) return []
        
        const minAmount = this.payment_selected.min_amount
        const baseAmounts = [150,200,300,500,1000,2000,5000,10000,15000]
        
        // 过滤出大于等于最小金额的值
        return baseAmounts.filter(amount => amount >= minAmount)
    },
    gift_amount: function() {
      return Math.min(this.select_amount*this.bonus_ratio,this.bonus_max)
    },
    first_deposit_gift_amount: function() {
      return (this.select_amount*0.2).toFixed(2)
    },
  },
  watch:{
    select_amount: {
      handler(newVal, oldVal) {
        console.log(newVal, oldVal)
        this.getfreetimes()
      }
    },
    '$store.state.balance': function (newVal) {
      this.balance = newVal
    },
    showprogress (val) {
      val && setTimeout(() => {
        this.showprogress = false
      }, 5000)
    },
  },
  created() {
    
  },
  mounted() {
    this.getmeminfo()
    this.payments()
    this.showfirst()
    this.promoinfo()
  },
  methods: {
    getmeminfo() {
      let paramObj = {
        member_id: this.getStorage('member_id'),
      }
      this.$server.getmeminfo(paramObj).then((response) => {
        if(response.code==200){
          this.meminfo = response.data
          this.balance = response.data.total_balance
          this.game_balance = response.data.game_balance
          this.game_platform = response.data.game_platform
          this.game_platform_id = response.data.game_platform_id
        }
      })

      this.$server.conf().then((response) => {
        this.alertmsg = response.data.pay_maintain
      })
    },
    payments() {
      let paramObj = {
        member_id: this.getStorage('member_id'),
      }
      this.$server.payments(paramObj).then((response) => {
        if(response.code==200){
          this.payments_arr = response.data
        }
      })
    },
    showfirst() {
      let paramObj = {
        member_id: this.getStorage('member_id'),
      }
      this.$server.showfirst(paramObj).then((response) => {
        if(response.code==200){
          this.allow_first = response.data.allow_first
          this.hasdeposit = response.data.hasdeposit
          this.hasfree = response.data.hasfree
          this.freetimes = response.data.freetimes
          if(this.allow_first){
            this.first_selected = true
          }
          this.getfreetimes()
        }
      })
    },
    promoinfo() {
      let paramObj = {
        promo_id:1
      }
      this.$server.promoinfo(paramObj).then((response) => {
        if(response.code==200){
          this.bonus_max = response.data.conf.bonus_max
          this.bonus_ratio = response.data.conf.bonus_ratio/100
        }
      })
    },
    usercollect() {
      this.collectdisabled = true
      this.showprogress = true
      let paramObj = {
        member_id: this.getStorage('member_id'),
        platform_id: this.meminfo.game_platform_id
      }
      this.$server.usercollect(paramObj).then((response) => {
        if(response.code==200){
          this.getmeminfo()
          this.collectdisabled = false
          this.showprogress = false
          this.collection = false
        }
      })
    },
    deposit() {
      if(this.select_amount<this.payment_selected.min_amount || this.select_amount>this.payment_selected.max_amount){
        //this.$snackbar.warning(this.$t('ckcgxe'))
        this.select_amount_error = this.$t('ckcgxe')
        return false;
      }
      this.showprogress = true
      this.depositdisabled = true
      let paramObj = {
        member_id: this.getStorage('member_id'),
        payment_platform_id:this.payment_selected.id,
        money:this.select_amount,
        first_selected: this.first_selected
      }

      this.$server.deposit(paramObj).then((response) => {
        this.showprogress = false
        this.depositdisabled = true
        if(response.code==200){
          this.$emit("qrcodeshow", response.data.pay_url, response.data.isiframe)
        }
      })
    },
    selectamount(amount) {
      this.select_amount = amount
    },
    select_payment(item) {
      this.payment_selected = item
      this.show_payments = false
      if(item.min_amount>200){
        this.select_amount = item.min_amount
      }else{
        this.select_amount = 200
      }
    },
    getfreetimes()
    {
      this.freespintimes = 0
      for (const fkey in this.freetimes) {
        if(this.select_amount>=parseInt(fkey)){
          this.freespintimes = this.freetimes[fkey]
        }
      }
    },
    gotofreespin() {
      this.$router.push({
        name: 'FreeSpins',
      })
      this.$emit('fanhui')
    },
    firstd_open() {
      this.marquee_details = true
    },
    firstd_close() {
      this.marquee_details = false
    },
    getBonus(amount) {
      if (amount < 200) return 20
      if (amount == 200) return 30
      if (amount < 500) return 50
      if (amount == 500) return 88
      if (amount <= 1000) return 188
      return 288
    }
  },
};
</script>
<style scoped>
  .CustomRadios .v-radio {
    padding: 8px;
    margin: 5px ;
    min-width: 90%;
    color: var(--v-primary-base);
    border: 1px dashed var(--v-secondary-lighten5);
  }
  .CustomRadios .v-radio.v-item--active{
    background-color: rgba(38, 163, 47, .1);
    border: 1px solid var(--v-primary-base);
  }
  .CustomRadios .v-item--active strong{
    color: var(--v-primary-base);
  }
  .v-application .sum-box{
    position: relative;
    border: 2px solid var(--v-nav_bg-base)!important;
    cursor: pointer;
    transition: all 0.3s ease-in-out;
  }
  
  .sum-box.active{
    border-color: var(--v-primary-base)!important;
  }
  .sum-chip{
    position: absolute;
    right: -2px;
    top: -8px;
  }
  .sum-box.active .sum-chip{
    background-color: #FFC107 !important;
    border-color: var(--v-nav_bg-base) !important;
    color: rgba(0, 0, 0, 0.87) !important;
  }
  .sum-chip2{
    position: absolute;
    left: -2px;
    bottom: -8px;
  }
  .sum-box.active .sum-chip2{
    background-color: #FFC107 !important;
    border-color: var(--v-nav_bg-base) !important;
    color: rgba(0, 0, 0, 0.87) !important;
  }
  .sum-check{
    position: absolute;
    bottom: -5px;
    right: -5px;
  }
</style>