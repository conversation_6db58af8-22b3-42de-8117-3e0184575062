<template>
  <div class="app_bg">
    <v-tabs
      v-if="!showQrcode && !showWaiting"
      v-model="tab_wallet"
      centered
      background-color="nav_bg"
    >
      <v-tab :href="`#deposit`">
        {{ $t('cunkuan') }}
      </v-tab>
      <v-tab :href="`#withdraw`">
        {{ $t('tikuan') }}
      </v-tab>
      <v-tabs-items
        v-model="tab_wallet"
      >
        <v-tab-item
          class="app_bg pb-16 pb-sm-0"
          value="deposit"
        >
          <Deposit
            @qrcodeshow="qrcodeshow(arguments)"
            @fanhui="fanhui"
          />
        </v-tab-item>

        <v-tab-item
          class="app_bg pb-16 pb-sm-0"
          value="withdraw"
        >
          <v-card-text>
            <form>
              <div class="input-block">
                <div class="d-flex align-center justify-space-between text--disabled mb-2">
                  <span>{{ $t('kyye') }}</span>
                  <span class="d-flex align-center justify-center">
                    <v-img
                      width="16"
                      src="../assets/img/icons/PHP.png"
                    />
                    <strong class="orange--text mx-1">{{ balance }}</strong>
                    <span>PHP</span>
                    <v-btn
                      icon
                      @click="refresh"
                    >
                      <v-icon color="grey">mdi-refresh</v-icon>
                    </v-btn>
                  </span>
                </div>
                <v-alert
                  dense
                  text
                  class="text-body-2"
                >
                  {{ $t('Your withdrawal will generally be processed within 5-30 minutes. Please make sure to reserve enough credit for your next gaming session.') }}
                </v-alert>
              </div>

              <div
                class="input-block"
              >
                <div class="mt-3 mb-1 text--disabled">
                  {{ $t('zsxm') }}
                </div>
                <v-text-field
                  v-model="realname"
                  filled
                  solo
                  flat
                  dense
                  :hide-details="!realnameerror"
                  :error-messages="realnameerror?$t('qsrzsxm'):''"
                  background-color="btn_bg"
                  class="grey--text"
                  :disabled="realnamedisabled"
                />
                <div class="mt-3 mb-1 text--disabled">
                  Account
                </div>
                <v-select
                  v-model="acc_type"
                  :items="acc_types"
                  filled
                  solo
                  flat
                  dense
                  no-data-text=""
                  :hide-details="!acc_typeerror"
                  :error-messages="acc_typeerror?'choose the account':''"
                  background-color="btn_bg"
                />
                <div class="mt-3 mb-1 text--disabled" />
                <v-text-field
                  v-model.number="tng_no"
                  type="number"
                  filled
                  solo
                  flat
                  dense
                  :hide-details="false"
                  :error-messages="tng_noerror"
                  background-color="btn_bg"
                  class="grey--text"
                  :disabled="tng_nodisabled"
                />
                <div class="d-flex justify-space-between mt-3 mb-1 text--disabled">
                  <div>
                    {{ $t('jine') }}
                  </div>
                  <div>
                    {{ $t('ktje') }}:
                    <strong class="mx-1 green--text">{{ meminfo.assets.withdrawable }}</strong>
                    PHP
                    <v-btn
                      icon
                      small
                      class="mt-n1"
                      @click="tipDialog = true"
                    >
                      <v-icon small>
                        mdi-tooltip-question-outline
                      </v-icon>
                    </v-btn>
                  </div>
                </div>
                <v-text-field
                  v-model.number="withdraw_money"
                  type="number"
                  filled
                  solo
                  flat
                  background-color="btn_bg"
                  :error-messages="withdraw_money_error"
                  prefix="₱"
                  suffix="PHP"
                  :placeholder="$t('qsrje')"
                  :hint="withdraw_money_hint"
                  persistent-hint
                  :hide-details="false"
                  class="grey--tex text-body-2 multi-line-hint"
                />
              </div>

              <v-btn
                depressed
                block
                large
                color="primary"
                class="text-none mt-4"
                @click="withdraw"
              >
                {{ $t('queren') }}
              </v-btn>
            </form>
          </v-card-text>
        </v-tab-item>
      </v-tabs-items>
    </v-tabs>

    <div
      v-if="showWaiting"
      ref="wallet"
      class="pb-16 pb-sm-0"
    >
      <v-card
        flat
        color="app_bg pa-5 pt-md-0"
      >
        <v-card-text
          class="text-center"
        >
          <div class="my-5">
            <v-icon
              x-large
              color="primary"
              style="font-size: 64px;!important"
            >
              mdi-check-circle
            </v-icon>
          </div> 
          <h3>
            {{ $t('tksqytj') }}!
          </h3>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            depressed
            color="btn_bg"
            class="pa-3"
            @click="fanhui"
          >
            {{ $t('fanhui') }}
          </v-btn>
          <v-spacer />
        </v-card-actions>
      </v-card>
    </div>
    <v-overlay :value="showprogress">
      <v-progress-circular
        indeterminate
      />
    </v-overlay>

    <v-dialog
      v-model="tipDialog"
      max-width="300"
      scrollable
    >
      <v-card class="box_bg">
        <v-card-title class="text-subtitle-1">
          {{ $t('ktje') }}
        </v-card-title>
        <v-card-text>
          {{ $t('kyyetip') }}
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="green darken-1"
            text
            @click="tipDialog = false"
          >
            {{ $t('zdl') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog
      v-model="tipFree"
      max-width="300"
      scrollable
    >
      <v-card class="box_bg">
        <v-card-title class="text-subtitle-1">
          <v-icon
            small
            left
          >
            mdi-bell
          </v-icon>
          {{ $t('wxts') }}
        </v-card-title>
        <v-card-text>
          {{ $t('wckts') }}
          <p class="font-weight-medium amber--text">
            {{ $t('wckts2') }}
          </p>
        </v-card-text>
        <v-card-actions>
          <v-btn
            text
            @click="tipFree = false"
          >
            {{ $t('quxiao') }}
          </v-btn>

          <v-spacer />
          <v-btn
            color="green darken-1"
            dark
            depressed
            @click="agree"
          >
            {{ $t('zdl') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <div
      v-if="showQrcode && isiframe==1"
      class="pb-16 pb-sm-0"
    >
      <div style="height: 80vh; overflow: hidden;">
        <iframe
          width="100%"
          height="100%"
          style="border: none;"
          :src="pay_url"
        />
      </div>
    </div>
    <div
      v-if="showQrcode && isiframe==0"
      ref="wallet"
      class="pb-16 pa-5 pb-sm-0"
      style="height: 700px;"
    >
      <v-card
        flat
        color="btn_bg pa-5 pt-md-0"
      >
        <v-card-text
          class="text-center"
        >
          <div class="mb-5">
            <v-icon
              x-large
              color="primary"
            >
              mdi-cash-clock
            </v-icon>
          </div> 
          <h3 class="primary--text">
            The order has been successfully created, please click the button below to pay!
          </h3>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            depressed
            color="primary"
            class="pa-3 px-10"
            :href="pay_url"
            target="_blank"
          >
            Go to Pay
          </v-btn>
          <v-spacer />
        </v-card-actions>
      </v-card>
    </div>
  </div>
</template>

<script>
import {store2} from '../store2.js'
import store from "../store/"
import Deposit from '@/components/Deposit.vue'
  export default {
    name: 'Wallet',
    components: {
      Deposit
    },
    data: () => ({
      isiframe: 0,
      pay_url: '',
      showQrcode: false,
      showWaiting: false,
      tipDialog: false,
      tipFree: false,
      meminfo:{
        assets: {
          withdrawable:0,
        }
      },
      withdraw_deduct: 0,
      balance: 0,
      tab_wallet: 'deposit',
      withdraw_money:'',
      withdraw_money_error: '',
      min_amount: 200,
      max_amount: 50000,
      realname: '',
      select_amount: 0,
      qr_code:'',
      qr_code_omit: '',
      erweima:'',
      acc_types: ['GCash','Paymaya'],
      acc_type: 'GCash',
      realnamedisabled: false,
      tng_nodisabled: false,
      realnameerror: false,
      acc_typeerror: false,
      tng_noerror: '',
      showprogress: false,
      tng_no: ''
    }),
    computed: {
      withdraw_deduct_msg() {
        return ''
      },
      withdraw_money_hint() {
        let hint = this.$t('mctkxe')+': '+this.min_amount+' ~ '+this.max_amount+' PHP'
        
        if(this.withdraw_deduct>0 && this.withdraw_money){
          hint += '\nThe 3rd-party payment processor charges ₱'+this.withdraw_deduct+' each withdrawal'
          hint += '\nActual amount received: '+this.withdraw_money+' - '+this.withdraw_deduct+' = ₱'+((this.withdraw_money-this.withdraw_deduct).toFixed(2))
        }
        
        return hint
      }
    },
    watch:{
      tab_wallet: {
        handler(newVal, oldVal) {
          console.log(newVal, oldVal)
          if(newVal=='withdraw'){
            this.collect()
          }
        }
      },
      '$store.state.balance': function (newVal) {
        this.balance = newVal
      },
      showprogress (val) {
        val && setTimeout(() => {
          this.showprogress = false
        }, 5000)
      },
    },
    created() {
      this.tab_wallet = store2.wallet_currtab
      this.$server.conf().then((response) => {
        this.withdraw_deduct = response.data.withdraw_deduct
      })
    },
    mounted() {
      this.getmeminfo()
      setTimeout(() => {
        if(this.meminfo.realname){
          this.realname = this.meminfo.realname
          //this.realnamedisabled = true
        }else{
          this.realname = localStorage.getItem('realname')??''
          this.realnamedisabled = false
        }
        if(this.meminfo.cpf){
          this.tng_no = this.meminfo.cpf
          //this.tng_nodisabled = true
        }else{
          this.tng_no = localStorage.getItem('tng_no')??''
          this.tng_nodisabled = false
        }
      }, 3000)
    },
    methods: {
      refresh() {
        this.getmeminfo()
      },
      getmeminfo() {
        let paramObj = {
          member_id: this.getStorage('member_id'),
        }
        this.$server.getmeminfo(paramObj).then((response) => {
          if(response.code==200){
            this.meminfo = response.data
            this.balance = response.data.total_balance
            if(response.data.withdraw_success_count<3){
              this.min_amount = 100
            }
            store.commit('balance', this.balance)
          }
        })
      },
      collect() {
        this.$snackbar.info(this.$t('yxyezdgj'))
        let paramObj = {
          member_id: this.getStorage('member_id'),
        }
        this.$server.collect(paramObj).then((response) => {
          if(response.code==200){
            this.getmeminfo()
          }
        })
      },
      async withdraw() {
        if (this.realname=='') {
          this.realnameerror = true
          return false
        }else{
          this.realnameerror = false
        }
        if (this.acc_type=='') {
          this.acc_typeerror = true
          return false
        }else{
          this.acc_typeerror = false
        }
        if (this.tng_no=='') {
          this.tng_noerror = 'Please check the withdrawal account format.'
        } else if(isNaN(this.tng_no)) {
          this.tng_noerror = 'Please check the withdrawal account format.'
        } else if(!/^[09]\d{9,10}$/.test(this.tng_no)) {
          this.tng_noerror = 'Please check the withdrawal account format.'
        } else {
          this.tng_noerror = ''
        }
        await this.checkaccount()
        if(this.tng_noerror){
          return false
        }

        if(this.withdraw_money>this.meminfo.assets.withdrawable){
          //this.$snackbar.warning(this.$t('ktjebz'))
          this.withdraw_money_error = this.$t('ktjebz')
          return false;
        }
        if(this.meminfo.deposited==0 && this.meminfo.assets.withdrawable<100){
          //this.$snackbar.warning(this.$t('tkcgxe'))
          this.withdraw_money_error = 'You must earn over ₱100 in balance using the free sign-up bonus before you can withdraw cash. Please continue playing.'
          return false;
        }
        if(this.withdraw_money<this.min_amount || this.withdraw_money>this.max_amount){
          //this.$snackbar.warning(this.$t('tkcgxe'))
          this.withdraw_money_error = this.$t('tkcgxe') + '(' +this.min_amount+' ~ '+this.max_amount+') PHP'
          return false;
        }
        if(this.meminfo.deposited==0){
          this.tipFree = true
          return false
        }
        this.withdraw_submit()
      },
      agree() {
        this.tipFree = false
        this.withdraw_submit()
      },
      async checkaccount() {
        let paramObj = {
          member_id: this.getStorage('member_id'),
          acc_type: this.acc_type,
          tng_no: this.tng_no
        }
        await this.$server.checkaccount(paramObj).then((response) => {
          if(response.code==941){
            this.tng_noerror = response.msg
          }
        })
      },
      withdraw_submit() {
        this.showprogress = true
        let paramObj = {
          member_id: this.getStorage('member_id'),
          money:this.withdraw_money,
          acc_type: this.acc_type,
          tng_no: this.tng_no,
          realname: this.realname,
        }
        this.$server.withdraw(paramObj).then((response) => {
          this.showprogress = false
          if(response.code==200){
            this.getmeminfo()
            //this.$snackbar.success(this.$t('tjcg'))
            this.showWaiting = true
            localStorage.setItem('acc_type', this.acc_type)
            localStorage.setItem('tng_no', this.tng_no)
            localStorage.setItem('realname', this.realname)
          }else{
            this.$snackbar.warning(response.msg)
          }
        })
      },
      fanhui() {
        this.$emit('closeprofile')
      },
      qrcodeshow(param) {
        //console.log(param)
        //this.select_amount = param[0]
        //this.qr_code = param[1]
        //this.qr_code_omit = param[2]
        //this.erweima = param[3]
        this.pay_url = param[0]
        this.isiframe = param[1]
        this.showQrcode = true
      },
      depositcompleted() {
        this.$emit('closeprofile')
      },
      doCopy: function (text) {
        this.$copyText(text, this.$refs.wallet).then( (e)=>{
          console.log(e)
          this.$snackbar.info(this.$t('fzcg'))
        }, function (e) {
          console.log(e)
        })
      },
    },
  };
</script>
<style>
.multi-line-hint .v-messages__message {
  white-space: pre-line;
}
</style>