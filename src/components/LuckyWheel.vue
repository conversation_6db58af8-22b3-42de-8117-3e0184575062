<template>
  <v-dialog
    v-model="showDialog"
    max-width="100%"
    persistent
    content-class="lucky-wheel-dialog"
  >
    <div class="lucky-wheel-container">
      <!-- 关闭按钮 -->
      <v-btn
        icon
        small
        absolute
        right
        top
        class="close-btn"
        @click="close"
      >
        <v-icon>mdi-close</v-icon>
      </v-btn>

      <!-- 弹层内容 -->
      <v-card class="lucky-wheel-card transparent-card">
        <!-- 标题头部图片 -->
        <div class="title-header">
          <img src="../assets/img/lucky_title_head.png" alt="Lucky Title" class="title-head-image" />
        </div>
        
        <!-- 标题背景区域 -->
        <div class="title-background-section mb-2">
          <h3 class="title-text-main">{{ $t('lucky_wheel_title') }}</h3>
        </div>

        <!-- Tab 切换区域 -->
        <div class="tabs-wrapper mb-3">
          <v-tabs
            v-model="activeTab"
            color="yellow"
            background-color="transparent"
            centered
            center-active
            show-arrows
            height="60"
            class="transparent-tabs"
          >
            <v-tab
              v-for="(tab, index) in tabs"
              :key="index"
              class="text-overline"
              style="min-width: 80px !important; max-width: 100px !important; width: 90px !important; padding: 4px 8px; margin: 0 5px !important; line-height: 1.1;"
            >
              {{ tab.name }}
            </v-tab>
          </v-tabs>
        </div>

        <!-- Tab 内容区域 -->
        <v-tabs-items v-model="activeTab" class="transparent-tabs-items">
          <v-tab-item
            v-for="(tab, index) in tabs"
            :key="index"
            class="transparent-tab-item"
          >
            <v-container class="text-center">
              
              <!-- 倒计时区域 -->
              <div v-if="tab.wheel==2 && wheel2_timediff>0" class="countdown-section mb-4">
                <!-- 提示文字 -->
                <div class="countdown-title">
                  <span class="title-text">3x Daily Red Packets: 3PM, 8PM, 10PM</span>
                </div>

                <!-- 倒计时 -->
                <div class="countdown-timer">
                  <div class="time-unit">
                    <div class="time-number">{{ formatTime(countdown.hours) }}</div>
                    <div class="time-label">H</div>
                  </div>
                  <div class="time-separator">:</div>
                  <div class="time-unit">
                    <div class="time-number">{{ formatTime(countdown.minutes) }}</div>
                    <div class="time-label">M</div>
                  </div>
                  <div class="time-separator">:</div>
                  <div class="time-unit">
                    <div class="time-number">{{ formatTime(countdown.seconds) }}</div>
                    <div class="time-label">S</div>
                  </div>
                </div>
              </div>
              
              <!-- 游戏内容区域 -->
              <div class="game-content-container">
                <!-- 转盘类型 -->
                <div v-if="!tab.type || tab.type === 'wheel'" class="wheel-container">
                  <LuckyWheel
                    :key="`wheel_${index}_${wheelKey}`"
                    :ref="`luckyWheel_${index}`"
                    width="280px"
                    height="280px"
                    :prizes="tab.prizes"
                    :blocks="tab.blocks"
                    :buttons="tab.buttons"
                    @start="() => checkConditionsAndStart(index)"
                    @end="endCallback"
                  />
                </div>
                
                <!-- 图片类型 -->
                <div v-else-if="tab.type === 'image'" class="image-container">
                  <div class="clickable-image" @click="() => checkConditionsAndTrigger(index)">
                    <img 
                      :src="tab.image" 
                      :alt="tab.name"
                      class="game-image"
                    />
                    <div class="click-effect" :class="{ 'active': clickEffects[index] }"></div>
                  </div>
                </div>
              </div>

              <!--
              <div class="activity-status-section mt-4">
                <div class="claim-button-container mb-3">
                  <v-btn
                    block
                    color="primary"
                    class="claim-btn"
                    @click="() => checkConditionsAndClaim(index)"
                  >
                    <v-icon left>mdi-gift</v-icon>
                    Claim
                  </v-btn>
                </div>
                
                <div class="progress-container mb-3 pa-4 rounded-xl">
                  <div class="d-flex justify-space-between align-center mb-2">
                    <span class="font-weight-medium text-body-2">Progress</span>
                    <span class="font-weight-bold text-body-2">{{ getProgressPercentage(index) }}%</span>
                  </div>
                  <v-progress-linear
                    :value="getProgressPercentage(index)"
                    height="8"
                    rounded
                    :color="getProgressColor(index)"
                    background-color="rgba(255, 255, 255, 0.3)"
                    class="rounded-lg"
                  />
                  <div class="mt-2 text-center">
                    <span class="text-caption font-weight-medium">{{ getProgressText(index) }}</span>
                  </div>
                </div>
              </div>
              -->

              <!-- 中奖信息滚动展示 -->
              <div class="wheel-info mt-4">
                <!-- 近期中奖信息 -->
                <div class="winner-info-container">
                  <div class="winner-info-header">
                    <span class="winner-header-text">🏆</span>
                  </div>
                  <div class="winner-scroll-container">
                    <div 
                      class="winner-scroll-content" 
                      :style="{ transform: `translateY(-${getScrollOffset(index) * 30}px)` }"
                    >
                      <div 
                        v-for="(winner, winnerIndex) in tab.winners" 
                        :key="`winner_${index}_${winnerIndex}`"
                        class="winner-item"
                      >
                        <span class="winner-name">{{ winner.member_id }}</span>
                        <span class="winner-amount">₱{{ winner.reward }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </v-container>
          </v-tab-item>
        </v-tabs-items>
      </v-card>
    </div>

    <!-- 中奖结果弹窗 -->
    <v-dialog
      v-model="showResult"
      max-width="400"
      persistent
    >
      <v-card class="result-card pa-4 rounded-xl">
        <!-- 关闭按钮 -->
        <v-btn
          icon
          small
          absolute
          right
          top
          class="result-close-btn"
          @click="closeResult"
        >
          <v-icon>mdi-close</v-icon>
        </v-btn>
        
        <v-card-title class="text-center">
          <h4 class="mx-auto">🎉 Congratulations!</h4>
        </v-card-title>
        <v-card-text class="text-center">
          <div class="result-content">
            <div class="prize-image mb-3">
              <v-img
                v-if="winPrize.image"
                :src="winPrize.image"
                width="80"
                height="80"
                class="mx-auto"
              />
              <v-icon
                v-else
                size="64"
                color="orange"
              >
                mdi-trophy
              </v-icon>
            </div>
            <h5 class="prize-name">{{ winPrize.name }}</h5>
            <p class="prize-description text--secondary">
              {{ winPrize.description }}
            </p>
          </div>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            block
            rounded
            depressed
            color="primary"
            @click="claimPrize"
          >
            {{ $t('claim') }}
          </v-btn>

          <v-spacer />
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- 条件引导弹窗 -->
    <v-dialog
      v-model="showConditionsDialog"
      max-width="600"
      persistent
      overlay-opacity="0.6"
    >
      <v-card class="conditions-card">
        <!-- 关闭按钮 -->
        <v-btn
          icon
          small
          absolute
          right
          top
          class="conditions-close-btn"
          @click="closeConditionsDialog"
        >
          <v-icon>mdi-close</v-icon>
        </v-btn>
        
        <v-card-title class="text-center pa-4">
          <div class="text-center" style="width: 100%;">
            <v-icon large color="warning" class="mb-2">mdi-information</v-icon>
          </div>
        </v-card-title>
        
        <v-card-text class="pa-4">
          <!-- 总体进度 -->
          <div class="overall-progress mb-4 pa-5 rounded-xl">
            <div class="d-flex justify-space-between align-center mb-3">
              <span class="font-weight-bold">Overall progress</span>
              <span class="success--text font-weight-bold text-h6">{{ currentConditions.overallProgress || 0 }}%</span>
            </div>
            <v-progress-linear
              :value="currentConditions.overallProgress || 0"
              height="12"
              rounded
              color="success"
              background-color="rgba(0, 0, 0, 0.2)"
            />
          </div>
          
          <!-- 条件列表 -->
          <div class="conditions-list pa-5 rounded-xl">
            <div 
              v-for="(condition, condIndex) in currentConditions.requirements" 
              :key="condIndex"
              class="condition-card mb-3 pa-4 rounded-lg elevation-2"
            >
              <div class="d-flex align-start">
                <div class="flex-shrink-0 mt-1">
                  <v-icon 
                    :color="condition.completed ? 'success lighten-1' : 'grey lighten-2'"
                    size="20"
                  >
                    {{ condition.completed ? 'mdi-check-circle' : 'mdi-circle-outline' }}
                  </v-icon>
                </div>
                <div class="flex-grow-1 ml-3">
                  <div class="font-weight-bold text-body-2 grey--text text--lighten-4 mb-1">{{ condition.title }}</div>
                </div>
                
              </div>
              
              <!-- 进度条（如果有数值要求） -->
              <div v-if="condition.progress !== undefined" class="mt-2 ml-8 mr-20">
                <v-progress-linear
                  :value="condition.progress"
                  height="6"
                  rounded
                  :color="condition.completed ? 'success' : 'primary'"
                  background-color="rgba(0, 0, 0, 0.2)"
                />
                <div class="d-flex justify-center align-center mt-1 text-caption grey--text text--lighten-3">
                  <span class="font-weight-bold green--text text--darken-1">{{ condition.current || 0 }}</span>
                  <span class="mx-1">/</span>
                  <span>{{ condition.target || 0 }}</span>
                  <span class="ml-1">{{ condition.unit || '' }}</span>
                </div>
              </div>
              
              <!-- 操作按钮 -->
              <div v-if="condition.actionButton && !condition.completed" class="mt-2 ml-8 mr-20">
                <v-btn
                  small
                  depressed
                  :color="condition.actionButton.color || 'primary'"
                  @click="handleConditionAction(condition)"
                >
                  {{ condition.actionButton.text }}
                </v-btn>
              </div>
              
            </div>
          </div>
          
          <!-- 提示信息 -->
          <v-alert
            type="info"
            border="left"
            colored-border
            elevation="2"
            dense
            class="mt-4 mb-0 text-caption"
          >
            {{ currentConditions.tip }}
          </v-alert>
        </v-card-text>
        
      </v-card>
    </v-dialog>

    <!-- 红包雨效果 -->
    <v-dialog
      v-model="showRedPacketRain"
      fullscreen
      hide-overlay
      transition="dialog-bottom-transition"
      content-class="red-packet-rain-dialog"
    >
      <div class="red-packet-rain-container">
        <!-- 背景 -->
        <div class="rain-background"></div>
        
        <!-- 关闭按钮 -->
        <v-btn
          icon
          large
          absolute
          right
          top
          class="rain-close-btn"
          @click="stopRedPacketRain"
        >
          <v-icon color="white">mdi-close</v-icon>
        </v-btn>
        
        <!-- 准备倒计时界面 -->
        <div v-if="showPrepareCountdown" class="prepare-countdown-overlay">
          <div class="prepare-countdown-content">
            <h1 class="prepare-title">🧧 Red packet rain is preparing</h1>
            <div 
              class="prepare-countdown-number"
              :key="prepareCountdown"
            >
              {{ prepareCountdown > 0 ? prepareCountdown : 'Start!' }}
            </div>
            <p class="prepare-subtitle">Are you ready?</p>
          </div>
        </div>
        
        <!-- 游戏界面 -->
        <div v-if="gameStarted">
          <!-- 标题 -->
          <div class="rain-title">
            <h2 class="text-center mb-4">🧧 Red packet rain is here!</h2>
            <p class="text-center">Click the red packet to get the reward</p>
            <div class="rain-countdown text-center mt-2">
              <div class="game-countdown-container">
                <span class="countdown-text">⏰ Remaining time: {{ redPacketCountdown }} seconds</span>
              </div>
            </div>
          </div>
          
          <!-- 红包元素 -->
          <div 
            v-for="packet in redPackets" 
            :key="packet.id"
            class="red-packet"
            :class="{ 'clicked': packet.clicked }"
            :style="{
              left: packet.x + '%',
              top: packet.y + '%',
              transform: `rotate(${packet.rotation}deg)`,
              fontSize: packet.size + 'px'
            }"
            @click.stop="clickRedPacket(packet)"
            @touchstart.stop="clickRedPacket(packet)"
          >
            🧧
          </div>
        </div>

      </div>
    </v-dialog>

    <!-- 红包抽奖结果弹窗 -->
    <v-dialog
      v-model="showRedPacketResult"
      max-width="400"
      persistent
    >
      <v-card class="red-packet-result-card">
        <v-card-title class="text-center pa-4">
          <div class="text-center" style="width: 100%;">
            <div class="red-packet-icon mb-3">🧧</div>
            <h4 class="font-weight-bold yellow--text">Congratulations!</h4>
          </div>
        </v-card-title>
        
        <v-card-text class="text-center px-6 py-0">
          <div class="prize-amount-display mb-4">
            <div class="amount-text mb-3">{{ redPacketPrize.displayAmount }}</div>
            <div class="amount-label">Red packet reward</div>
          </div>
          
          <div class="celebration-text">
            <p class="mb-2">🎉 Congratulations! You have received a red packet reward!</p>
          </div>
        </v-card-text>
        
        <v-card-actions class="pa-4">
          <v-spacer />
          <v-btn
            color="orange"
            large
            rounded
            block
            depressed
            @click="claimRedPacketReward"
          >
            <v-icon left>mdi-gift</v-icon>
            Claim
          </v-btn>
          <v-spacer />
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog
      v-model="showDownloadDialog"
      persistent
      max-width="290"
    >
      <v-card>
        <v-card-title class="text-h5">
          Confirmation
        </v-card-title>
        <v-card-text>This ticket can be claimed only using the APP</v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            color="green darken-1"
            text
            @click="showDownloadDialog = false"
          >
            Cancel
          </v-btn>
          <v-btn
            color="green darken-1"
            text
            @click="gotoDownload"
          >
            Download
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-dialog>
</template>

<script>
import store from "../store/"
export default {
  name: 'LuckyWheelDialog',
  components: {
    
  },
  props: {
    
  },
  data() {
    return {
      showDialog: true,
      showResult: false,
      activeTab: 0,
      isSpinning: false,
      winPrize: {},
      tabs: [],
      wheelKey: 0, // 用于强制重新渲染转盘
      scrollTimer: null,
      currentScrollIndex: 0,
      // 倒计时相关
      wheel2_timediff: 0,
      countdownTimer: null,
      countdown: {
        hours: 0,
        minutes: 0,
        seconds: 0
      },
      // 图片点击效果
      clickEffects: {},
      // 每个tab的滚动索引
      tabScrollIndexes: {},
      showConditionsDialog: false,
      currentConditions: {},
      currentTabIndex: 0,
      // 红包雨相关状态
      showRedPacketRain: false,
      redPackets: [],
      redPacketAnimationId: null,
      redPacketSpawnTimer: null,
      redPacketTimer: null,
      redPacketCountdownTimer: null,
      redPacketStartTime: null,
      redPacketTimeout: 30000,
      redPacketRemainingTime: 30, // 添加响应式的剩余时间变量
      showRedPacketResult: false,
      redPacketPrize: {},
      // 准备倒计时相关
      showPrepareCountdown: false,
      prepareCountdown: 3,
      gameStarted: false,
      wheelConfig: [],
      showDownloadDialog: false
    }
  },
  computed: {
    // 根据tab类型获取对应的中奖信息
    getDisplayWinners() {
      return (tabIndex) => {
        const tab = this.tabs[tabIndex]
        if (!tab) return []
        
        // 如果tab有自己的winners数据，使用tab的数据
        const winners = tab.winners || this.winnersList
        // 重复两次数据以实现无缝循环滚动
        return [...winners, ...winners]
      }
    },
    
    // 获取滚动偏移量
    getScrollOffset() {
      return (tabIndex) => {
        return this.tabScrollIndexes[tabIndex] || 0
      }
    },
    
    // 红包雨倒计时
    redPacketCountdown() {
      return this.redPacketRemainingTime
    }
  },
  watch: {
    activeTab(newVal) {
      console.log('切换到tab:', newVal)
      // 重置转盘状态
      this.isSpinning = false
      
      // 强制重新渲染当前转盘
      this.$nextTick(() => {
        this.wheelKey++
        console.log('切换tab后重新渲染转盘，wheelKey:', this.wheelKey)
      })
    }
  },
  created() {
    this.redemption()
  },
  beforeDestroy() {
    this.stopWinnerScroll()
    this.stopCountdown()
    this.stopRedPacketRain()
  },
  methods: {
    // 随机设置 activeTab
    setRandomTab() {
      // 生成一个随机索引
      const randomIndex = Math.floor(Math.random() * this.tabs.length);
      // 更新 activeTab
      this.activeTab = randomIndex;
    },
    redemption() {
      let paramObj = {
        member_id: parseInt(this.getStorage('member_id')),
      }
      this.$server.redemption(paramObj).then((response) => {
        if(response.code==200){
          this.wheelConfig = response.data.map((item) => {
            if(item.wheel==0){
              item.name = '💓Lucky Daily Mission'
              item.blocks = [{
                padding: '10px',
                background: '#24262b',
                imgs: [{
                  src: require('@/assets/img/lucky_bg.png'),
                  width: '100%',
                  height: '100%',
                  rotate: false
                }]
              }]
              item.buttons = [{
                radius: '30%',
                pointer: true,
                fonts: [{
                  top: '-10px',
                  text: 'Go',
                  fontColor: '#fff',
                  fontWeight: 'bold'
                }],
                imgs: [{
                  top: '-58px',
                  src: require('@/assets/img/lucky_btn.png'),
                  width: '80px',
                  height: '100px'
                }]
              }]
              item.prizes = item.prizes.map((item,index) => ({
                background: index%2==0 ? '#ff4094' : '#feffd7',
                fonts: [{
                  text: item.award === '0' 
                    ? `Thanks` 
                    : `+₱ ${item.award}`,
                  fontColor: index%2==0 ? '#ffffe3' : '#fd408a',
                  fontSize: '14px',
                  fontWeight: 'bold',
                  top: '15%'
                }],
                imgs: [{
                  src: require(`@/assets/img/icons/${item.award === '0' ? 'slots.png' : 'bonus.svg'}`),
                  top: '35%',
                  width: '36px',
                  height: '36px',
                }]
              }))
              item.winners = item.winners
            }
            if(item.wheel==1){
              item.name = '📱iPhone Prize'
              item.blocks = [{
                padding: '15px', 
                background: '#24262b',
                imgs: [{
                  src: require('@/assets/img/lucky_bg.png'),
                  width: '100%',
                  height: '100%',
                  rotate: false
                }]
              }]
              item.buttons = [{
                radius: '30%',
                pointer: true,
                fonts: [{
                  top: '-10px',
                  text: 'Go',
                  fontColor: '#fff',
                  fontWeight: 'bold'
                }],
                imgs: [{
                  top: '-58px',
                  src: require('@/assets/img/lucky_btn.png'),
                  width: '80px',
                  height: '100px'
                }]
              }]
              item.prizes = item.prizes.map((item,index) => ({
                background: index%2==0 ? '#ff4094' : '#feffd7',
                fonts: [{
                  text: item.type === 2 
                    ? `${item.award}`
                    : `+₱ ${item.award}`,
                  fontColor: index%2==0 ? '#ffffe3' : '#fd408a',
                  fontSize: '14px',
                  fontWeight: 'bold',
                  top: '15%'
                }],
                imgs: [{
                  src: require(`@/assets/img/${item.type === 2 ? 'phone-iphone15.webp' : 'icons/bonus.svg'}`),
                  top: '50%',
                  width: '36px',
                  height: '36px',
                }]
              }))
              item.winners = item.winners
            }
            if(item.wheel==2){
              item.name = '🧧Red packet rain'
              item.type = 'image'
              item.image = require('@/assets/img/lucky_raffle.webp')
              item.winners = item.winners
              this.wheel2_timediff = item.countdown
            }
            if(item.wheel==3){
              item.name = '🥇Monthly turnover Bonus'
              item.type = 'image'
              item.image = require('@/assets/img/lucky_cash_box.webp')
              item.winners = item.winners
            }
            return {
              ...item,
            };
          });
          console.log(this.wheelConfig)

          this.initWheel()

          this.startCountdown()
          this.startWinnerScroll()

          this.setRandomTab()
        }else{
          
        }
      })
    },
    initWheel() {
      console.log('初始化转盘配置', this.wheelConfig)
      
      // 初始化转盘配置
      this.tabs = this.wheelConfig.map((config, index) => ({
        ...config,
        // 确保每个转盘都有基本配置
        blocks: config.blocks || [{ padding: '13px', background: '#617df2' }],
        buttons: config.buttons || [{
          radius: '35%',
          background: '#8a9bf3',
          pointer: true,
          fonts: [{ text: '开始', top: '-10px' }]
        }]
      }))
      
      console.log('转盘配置已初始化', this.tabs)
      
      // 强制重新渲染转盘
      this.wheelKey++
      
      // 确保在下一个tick后重置状态
      this.$nextTick(() => {
        this.isSpinning = false
        console.log('转盘状态已重置，wheelKey:', this.wheelKey)
        
        // 等待DOM更新后再次检查转盘实例
        this.$nextTick(() => {
          console.log('DOM更新后的refs:', this.$refs)
          const wheel = this.$refs.currentWheel
          console.log('转盘实例检查:', { wheel, isArray: Array.isArray(wheel), hasPlay: wheel?.[0]?.play || wheel?.play })
        })
      })
    },
    
    // 开始转盘回调
    startCallback(tabIndex) {
      console.log('开始转盘回调被触发', { 
        isSpinning: this.isSpinning, 
        activeTab: this.activeTab, 
        tabIndex: tabIndex || this.activeTab 
      })
      
      if (this.isSpinning) {
        console.log('转盘正在旋转中，忽略此次点击')
        return
      }
      
      this.isSpinning = true
      
      // 获取当前激活的转盘实例
      const targetTab = tabIndex !== undefined ? tabIndex : this.activeTab
      const refName = `luckyWheel_${targetTab}`
      let currentWheel = this.$refs[refName]
      
      // 如果是数组，取第一个元素
      if (Array.isArray(currentWheel)) {
        currentWheel = currentWheel[0]
      }
      
      console.log('转盘实例信息：', { 
        targetTab, 
        refName, 
        currentWheel, 
        isArray: Array.isArray(this.$refs[refName]), 
        refs: this.$refs 
      })
      
      if (currentWheel) {
        console.log('开始转动转盘', { currentWheel, methods: Object.getOwnPropertyNames(currentWheel) })
        
        try {
          // 模拟调用后端接口获取中奖结果
          this.getLuckyResult(targetTab)
        } catch (error) {
          console.error('转盘play方法调用失败:', error)
          this.isSpinning = false
        }
      } else {
        console.error('转盘实例未找到', { 
          targetTab,
          refName,
          currentWheel, 
          refs: this.$refs,
          activeTab: this.activeTab
        })
        this.isSpinning = false
      }
    },
    
    // 转盘结束回调
    endCallback(prize) {
      console.log('转盘结束回调', prize)
      this.isSpinning = false
      
      // 获取奖品图片
      let prizeImage = null
      if (prize.imgs && prize.imgs.length > 0) {
        prizeImage = prize.imgs[0].src
      }
      
      // 设置中奖信息
      this.winPrize = {
        name: prize.fonts[0].text,
        description: 'Congratulations! You have won ' + prize.fonts[0].text + '!',
        image: prizeImage
      }
      
      console.log('中奖信息设置:', this.winPrize)
      
      // 显示中奖结果
      this.showResult = true
      this.redemption()
      
      // 触发中奖事件
      this.$emit('win', {
        tabIndex: this.activeTab,
        prize: prize,
        winPrize: this.winPrize
      })
    },
    
    // 获取抽奖结果
    async getLuckyResult(targetTab) {
      try {
        let paramObj = {
          wheel: this.wheelConfig[targetTab].wheel,
          member_id: parseInt(this.getStorage('member_id')),
        }
        await this.$server.redemption_reward(paramObj).then((response) => {
          if(response.code==200){
            const tabIndex = targetTab !== undefined ? targetTab : this.activeTab
            const prizeIndex = response.data.award_key
            
            // 获取对应的转盘实例并停止
            const refName = `luckyWheel_${tabIndex}`
            let currentWheel = this.$refs[refName]
            
            // 如果是数组，取第一个元素
            if (Array.isArray(currentWheel)) {
              currentWheel = currentWheel[0]
            }
            
            console.log('准备停止转盘：', { tabIndex, refName, currentWheel, prizeIndex })
            
            if (currentWheel) {
              try {
                currentWheel.play()
                setTimeout(() => {
                  currentWheel.stop(prizeIndex)
                }, 3000)
                
                console.log('转盘停止，中奖索引:', prizeIndex)
              } catch (error) {
                console.error('转盘stop方法调用失败:', error)
                this.isSpinning = false
              }
            } else {
              console.error('转盘实例未找到，无法停止', { refName, refs: this.$refs })
              this.isSpinning = false
            }
          }else{
            this.$snackbar.warning(response.msg)
          }
        })
      } catch (error) {
        console.error('获取抽奖结果失败:', error)
        this.isSpinning = false
        this.$snackbar.error('抽奖失败，请重试')
      }
    },
    
    // 领取奖品
    claimPrize() {
      // 这里应该调用API领取奖品
      this.$emit('claim', this.winPrize)
      this.closeResult()
    },
    
    // 关闭结果弹窗
    closeResult() {
      this.showResult = false
      this.winPrize = {
        name: '',
        description: '',
        image: null
      }
    },
    
    // 关闭整个弹层
    close() {
      store.commit('luckywheel', false)
      this.stopWinnerScroll() // 停止滚动
      this.stopCountdown() // 停止倒计时
      this.showDialog = false
      this.$emit('close')
    },
    
    // 开始中奖信息滚动
    startWinnerScroll() {
      this.stopWinnerScroll() // 先清除现有定时器
      
      // 初始化每个tab的滚动索引
      this.tabs.forEach((tab, index) => {
        this.$set(this.tabScrollIndexes, index, 0)
      })
      
      console.log('开始中奖信息滚动', { 
        tabCount: this.tabs.length,
        tabScrollIndexes: this.tabScrollIndexes
      })
      
      this.scrollTimer = setInterval(() => {
        // 更新每个tab的滚动索引
        this.tabs.forEach((tab, index) => {
          const winners = tab.winners || this.winnersList
          let currentIndex = this.tabScrollIndexes[index] || 0
          currentIndex++
          
          // 当滚动到第一轮数据结束时，重置到开头
          if (currentIndex >= winners.length) {
            currentIndex = 0
          }
          
          this.$set(this.tabScrollIndexes, index, currentIndex)
        })
        
      }, 2000) // 每2秒滚动一行
    },
    
    // 停止中奖信息滚动
    stopWinnerScroll() {
      if (this.scrollTimer) {
        clearInterval(this.scrollTimer)
        this.scrollTimer = null
      }
    },
    
    // 处理图片点击
    handleImageClick(tabIndex) {
      if (this.wheelConfig[tabIndex].wheel === 2) {
        this.startRedPacketRain()
      } else if (this.wheelConfig[tabIndex].wheel === 3) {
        this.simulateImageReward()
      }
    },
    
    simulateImageReward() {
      let paramObj = {
        wheel: 3,
        member_id: parseInt(this.getStorage('member_id')),
      }
      this.$server.redemption_reward(paramObj).then((response) => {
        if(response.code==200){
          // 设置中奖信息
          this.winPrize = {
            name: '',
            description: 'Congratulations! You have won ' + response.data.unit + ' ' + response.data.reward,
            image: null
          }
          this.showResult = true
          this.redemption()
          this.$emit('claim', this.winPrize)
        }else{
          this.$snackbar.warning(response.msg)
        }
      })
    },
    
    // 格式化时间数字（补零）
    formatTime(time) {
      return time.toString().padStart(2, '0')
    },
    
    // 开始倒计时
    startCountdown() {
      this.stopCountdown() // 先清除现有定时器
      if(this.wheel2_timediff>0){
        this.countdownTimer = setInterval(() => {
          this.updateCountdown()
        }, 1000)
      }
    },
    
    // 停止倒计时
    stopCountdown() {
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer)
        this.countdownTimer = null
      }
    },
    
    // 更新倒计时
    updateCountdown() {
      if(this.wheel2_timediff>0){
        this.wheel2_timediff = this.wheel2_timediff - 1
        this.calculateCountdown(this.wheel2_timediff*1000)
      }else{
        this.wheel2_timediff = 0
        this.stopCountdown()
        this.redemption()
      }
    },
    
    // 计算倒计时各部分
    calculateCountdown(diff) {
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
      const seconds = Math.floor((diff % (1000 * 60)) / 1000)
      
      this.countdown = {
        hours: hours,
        minutes: minutes,
        seconds: seconds
      }
    },

    // 检查条件并开始
    checkConditionsAndStart(tabIndex) {
      if(!this.getStorage('member_id')){
        this.$emit('login')
        return
      }
      if(this.wheelConfig[tabIndex].appDownload==false){
        this.showDownloadDialog = true
        return
      }
      if(this.wheelConfig[tabIndex].countdown>0){
        return
      }
      
      this.currentTabIndex = tabIndex
      
      if (this.isEligible(tabIndex)) {
        this.startCallback(tabIndex)
      } else {
        this.showConditionsDialog = true
        this.currentConditions = this.getConditionsData(tabIndex)
      }
    },

    // 检查条件并触发
    checkConditionsAndTrigger(tabIndex) {
      if(!this.getStorage('member_id')){
        this.$emit('login')
        return
      }
      if(this.wheelConfig[tabIndex].appDownload==false){
        this.showDownloadDialog = true
        return
      }
      if(this.wheelConfig[tabIndex].countdown>0){
        return
      }

      this.currentTabIndex = tabIndex
      
      if (this.isEligible(tabIndex)) {
        this.handleImageClick(tabIndex)
      } else {
        this.showConditionsDialog = true
        this.currentConditions = this.getConditionsData(tabIndex)
      }
    },
    gotoDownload(){
      this.showDownloadDialog = false
      this.$router.replace({name:'download'})
      this.close()
    },
    // 检查用户是否符合条件
    isEligible(tabIndex) {    
      // 检查所有条件是否都满足
      return Object.keys(this.wheelConfig[tabIndex].condition).every(conditionType => {
        return this.checkSingleCondition(conditionType,tabIndex)
      })
    },

    // 检查单个条件
    checkSingleCondition(conditionType,tabIndex) {
      switch (conditionType) {
        case 'dailyDeposit':
          return this.wheelConfig[tabIndex].condition.dailyDeposit.completed
        case 'dailyBet':
          return this.wheelConfig[tabIndex].condition.dailyBet.completed
        case 'realNameAuth':
          return this.wheelConfig[tabIndex].condition.realNameAuth.completed
        case 'totalInvite':
          return this.wheelConfig[tabIndex].condition.totalInvite.completed
        case 'monthlyBet':
          return this.wheelConfig[tabIndex].condition.monthlyBet.completed
        default:
          return true
      }
    },

    // 获取条件数据
    getConditionsData(tabIndex) {
      if (!this.wheelConfig[tabIndex].condition) {
        return {
          title: 'Conditions',
          overallProgress: 100,
          requirements: [],
          tip: 'No special conditions required'
        }
      }
      
      const requirements = Object.keys(this.wheelConfig[tabIndex].condition).map(conditionType => {
        return this.getConditionDetails(conditionType,tabIndex)
      })
      
      const completedCount = requirements.filter(req => req.completed).length
      const overallProgress = Math.round((completedCount / requirements.length) * 100)
      
      return {
        title: `${this.wheelConfig[tabIndex].name} - Conditions`,
        overallProgress,
        requirements,
        tip: `Complete ${completedCount}/${requirements.length} conditions to participate in ${this.wheelConfig[tabIndex].name} activity!`
      }
    },

    // 获取条件详情
    getConditionDetails(conditionType,tabIndex) {
      switch (conditionType) {
        case 'dailyDeposit':
          return {
            title: 'Deposit ≧ ₱'+this.wheelConfig[tabIndex].condition.dailyDeposit.target,
            description: 'Complete the daily minimum deposit amount',
            completed: this.wheelConfig[tabIndex].condition.dailyDeposit.completed,
            progress: Math.min((this.wheelConfig[tabIndex].condition.dailyDeposit.current / this.wheelConfig[tabIndex].condition.dailyDeposit.target) * 100, 100),
            current: this.wheelConfig[tabIndex].condition.dailyDeposit.current,
            target: this.wheelConfig[tabIndex].condition.dailyDeposit.target,
            unit: 'PHP',
            actionButton: {
              text: 'Deposit',
              color: 'primary',
              action: 'deposit'
            }
          }
        case 'dailyBet':
          return {
            title: 'Vaild bet ≧ '+this.wheelConfig[tabIndex].condition.dailyBet.target,
            description: 'Complete the daily minimum bet amount',
            completed: this.wheelConfig[tabIndex].condition.dailyBet.completed,
            progress: Math.min((this.wheelConfig[tabIndex].condition.dailyBet.current / this.wheelConfig[tabIndex].condition.dailyBet.target) * 100, 100),
            current: this.wheelConfig[tabIndex].condition.dailyBet.current,
            target: this.wheelConfig[tabIndex].condition.dailyBet.target,
            unit: 'PHP',
            actionButton: {
              text: 'Bet',
              color: 'primary',
              action: 'bet'
            }
          }
        case 'realNameAuth':
          return {
            title: 'Real Name Auth',
            description: 'Complete the real name authentication',
            completed: this.wheelConfig[tabIndex].condition.realNameAuth.completed,
            actionButton: !this.wheelConfig[tabIndex].condition.realNameAuth.completed ? {
              text: 'Auth',
              color: 'warning',
              action: 'auth'
            } : null
          }
        case 'totalInvite':
          return {
            title: 'Invite valid players ≧ '+this.wheelConfig[tabIndex].condition.totalInvite.target,
            description: 'Complete the total invite amount',
            progress: Math.min((this.wheelConfig[tabIndex].condition.totalInvite.current / this.wheelConfig[tabIndex].condition.totalInvite.target) * 100, 100),
            current: this.wheelConfig[tabIndex].condition.totalInvite.current,
            target: this.wheelConfig[tabIndex].condition.totalInvite.target,
            unit: 'Players',
            completed: this.wheelConfig[tabIndex].condition.totalInvite.completed,
            actionButton: !this.wheelConfig[tabIndex].condition.totalInvite.completed ? {
              text: 'Invite',
              color: 'success',
              action: 'invite'
            } : null
          }
        case 'monthlyBet':
          return {
            title: 'Monthly Bet ≧ '+this.wheelConfig[tabIndex].condition.monthlyBet.target,
            description: 'Complete the monthly minimum bet amount',
            progress: Math.min((this.wheelConfig[tabIndex].condition.monthlyBet.current / this.wheelConfig[tabIndex].condition.monthlyBet.target) * 100, 100),
            current: this.wheelConfig[tabIndex].condition.monthlyBet.current,
            target: this.wheelConfig[tabIndex].condition.monthlyBet.target,
            unit: 'PHP',
            completed: this.wheelConfig[tabIndex].condition.monthlyBet.completed,
            actionButton: !this.wheelConfig[tabIndex].condition.monthlyBet.completed ? {
              text: 'Bet',
              color: 'primary',
              action: 'bet'
            } : null
          }
        default:
          return {
            title: 'Unknown Condition',
            description: 'Condition Description',
            completed: false
          }
      }
    },

    // 获取进度百分比
    getProgressPercentage(tabIndex) {
      const tab = this.tabs[tabIndex]
      if (!tab || !tab.conditions || tab.conditions.length === 0) return 100
      
      const completedCount = tab.conditions.filter(conditionType => 
        this.checkSingleCondition(conditionType)
      ).length
      
      return Math.round((completedCount / tab.conditions.length) * 100)
    },

    // 获取进度颜色
    getProgressColor(tabIndex) {
      const percentage = this.getProgressPercentage(tabIndex)
      
      if (percentage >= 100) return 'success'
      if (percentage >= 60) return 'primary'
      if (percentage >= 30) return 'warning'
      return 'error'
    },

    // 获取进度文本
    getProgressText(tabIndex) {
      const tab = this.tabs[tabIndex]
      if (!tab || !tab.conditions || tab.conditions.length === 0) {
        return 'Can participate in the activity'
      }
      
      const completedCount = tab.conditions.filter(conditionType => 
        this.checkSingleCondition(conditionType)
      ).length
      
      const totalCount = tab.conditions.length
      
      if (completedCount === totalCount) {
        return 'Can participate in the activity'
      } else {
        return `Completed ${completedCount}/${totalCount}`
      }
    },

    // 处理条件操作
    handleConditionAction(condition) {
      console.log('处理条件操作:', condition)
      
      if (!condition.actionButton) return
      
      this.$emit('navigate-to', condition.actionButton.action)
      this.closeConditionsDialog()
      this.close()
    },

    // 关闭条件引导弹窗
    closeConditionsDialog() {
      this.showConditionsDialog = false
      this.currentConditions = {}
    },

    // 启动红包雨效果
    startRedPacketRain() {
      this.showRedPacketRain = true
      this.showPrepareCountdown = true
      this.gameStarted = false
      this.prepareCountdown = 3
      this.redPackets = []
      
      // 开始准备倒计时
      this.startPrepareCountdown()
    },
    
    // 开始准备倒计时
    startPrepareCountdown() {
      console.log('开始准备倒计时，初始值:', this.prepareCountdown)
      const countdownTimer = setInterval(() => {
        this.prepareCountdown--
        console.log('准备倒计时:', this.prepareCountdown)
        
        // 强制更新组件以触发动画
        this.$forceUpdate()
        
        if (this.prepareCountdown <= 0) {
          clearInterval(countdownTimer)
          console.log('准备倒计时结束，即将开始游戏')
          
          // 准备倒计时结束，开始游戏
          setTimeout(() => {
            this.showPrepareCountdown = false
            this.gameStarted = true
            console.log('开始游戏，gameStarted:', this.gameStarted)
            this.startGameCountdown()
          }, 1000) // 显示"开始！"1秒后开始游戏
        }
      }, 1000)
    },
    
    // 开始游戏倒计时和红包雨
    startGameCountdown() {
      console.log('开始游戏倒计时和红包雨')
      this.redPacketStartTime = Date.now()
      this.redPacketTimeout = 30000 // 30秒超时
      this.redPacketRemainingTime = 30 // 重置倒计时
      console.log('设置开始时间:', this.redPacketStartTime, '剩余时间:', this.redPacketRemainingTime)
      this.createRedPackets()
      this.animateRedPackets()
      
      // 每秒更新倒计时显示
      this.redPacketCountdownTimer = setInterval(() => {
        if (!this.showRedPacketRain) return
        
        // 计算剩余时间
        const elapsed = Date.now() - this.redPacketStartTime
        const remaining = Math.max(0, Math.ceil((this.redPacketTimeout - elapsed) / 1000))
        this.redPacketRemainingTime = remaining
        
        console.log('倒计时更新:', this.redPacketRemainingTime, '已过时间:', Math.floor(elapsed/1000))
        
        // 如果时间到了，自动结束
        if (remaining <= 0) {
          console.log('倒计时结束，自动停止红包雨')
          this.stopRedPacketRain()
          //this.$snackbar.error('红包雨已结束，下次记得快点抢哦！')
        }
      }, 1000)
      
      // 30秒后自动结束红包雨（双重保险）
      this.redPacketTimer = setTimeout(() => {
        if (this.showRedPacketRain) {
          console.log('30秒时间到，自动结束红包雨')
          this.stopRedPacketRain()
          //this.$snackbar.error('红包雨已结束，下次记得快点抢哦！')
        }
      }, this.redPacketTimeout)
    },

    // 创建红包
    createRedPackets() {
      this.redPackets = []
      
      // 检测设备类型
      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent)
      const isAndroid = /Android/.test(navigator.userAgent)
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || window.innerWidth <= 768
      
      // iOS设备需要更保守的配置
      const initialCount = isIOS ? 2 : (isMobile ? 3 : 5)
      
      // 立即生成初始红包
      for (let i = 0; i < initialCount; i++) {
        const packet = {
          id: `packet_${Date.now()}_${Math.random()}_${i}`,
          x: 5 + Math.random() * 90, // 随机横坐标 (5%-95%)
          y: -20 - (i * 20), // 错开起始位置，避免重叠
          speed: 0.15 + Math.random() * 0.12, // 非常慢的下落速度 0.15-0.25 像素/帧
          rotation: Math.random() * 360,
          rotationSpeed: -0.2 + Math.random() * 0.4, // 更慢的旋转速度
          size: 50 + Math.random() * 15, // 更大的尺寸 50-65px，便于点击
          clicked: false,
          gravity: 0.003 + Math.random() * 0.002 // 非常小的重力加速度 0.003-0.005
        }
        this.redPackets.push(packet)
      }
      
      // 连续生成红包，保持红包雨效果
      this.redPacketSpawnTimer = setInterval(() => {
        if (!this.showRedPacketRain) return
        
        // 根据设备类型调整红包数量和生成频率
        const maxPackets = isIOS ? 8 : (isMobile ? 12 : 25)
        const spawnCount = isIOS ? 1 : (isMobile ? 1 : (1 + Math.floor(Math.random() * 2)))
        
        // 只在红包数量不足时生成新红包
        if (this.redPackets.length < maxPackets) {
          for (let i = 0; i < spawnCount; i++) {
            const packet = {
              id: `packet_${Date.now()}_${Math.random()}_spawn`,
              x: 5 + Math.random() * 90, // 随机横坐标 (5%-95%)
              y: -20, // 从顶部开始
              speed: 0.15 + Math.random() * 0.1, // 非常慢的下落速度 0.15-0.25 像素/帧
              rotation: Math.random() * 360,
              rotationSpeed: -0.2 + Math.random() * 0.4, // 更慢的旋转速度
              size: 50 + Math.random() * 15, // 更大的尺寸 50-65px，便于点击
              clicked: false,
              gravity: 0.003 + Math.random() * 0.002 // 非常小的重力加速度 0.003-0.005
            }
            this.redPackets.push(packet)
          }
        }
        
        // 移除已掉落到底部或被点击的红包（150%确保完全掉出屏幕）
        this.redPackets = this.redPackets.filter(p => p.y < 150 && !p.clicked)
        
        // 限制最大红包数量，避免性能问题
        if (this.redPackets.length > maxPackets) {
          this.redPackets = this.redPackets.slice(-maxPackets)
        }
        
      }, isIOS ? (800 + Math.random() * 700) : (isMobile ? (500 + Math.random() * 500) : (300 + Math.random() * 400))) // iOS设备生成间隔最长
    },

    // 红包动画
    animateRedPackets() {
      let frameCount = 0
      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent)
      
      const animate = () => {
        if (!this.showRedPacketRain) return

        // iOS设备使用更保守的清理频率
        const cleanupInterval = isIOS ? 6 : 3
        const shouldCleanup = frameCount % cleanupInterval === 0
        frameCount++

        // 批量更新红包位置
        for (let i = 0; i < this.redPackets.length; i++) {
          const packet = this.redPackets[i]
          if (!packet.clicked) {
            // 应用重力加速度（更温和的加速）
            packet.speed += packet.gravity
            packet.y += packet.speed
            packet.rotation += packet.rotationSpeed
            
            // 限制最大下落速度，避免太快
            if (packet.speed > 0.5) {
              packet.speed = 0.5
            }
          }
        }

        // 定期清理过期红包，减少频率
        if (shouldCleanup) {
          this.redPackets = this.redPackets.filter(packet => 
            packet.y <= 150 && !packet.clicked
          )
        }

        // iOS使用setTimeout模拟60FPS，避免Safari的30FPS限制
        if (isIOS) {
          setTimeout(() => {
            this.redPacketAnimationId = requestAnimationFrame(animate)
          }, 16.67) // 约60FPS
        } else {
          this.redPacketAnimationId = requestAnimationFrame(animate)
        }
      }
      
      animate()
    },

    // 点击红包
    clickRedPacket(packet) {
      console.log('红包被点击了:', packet)
      if (packet.clicked) return
      
      packet.clicked = true
      
      // 停止红包雨动画
      this.stopRedPacketRain()
      
      // 生成随机奖励
      this.generateRedPacketReward()
    },

    // 停止红包雨
    stopRedPacketRain() {
      this.showRedPacketRain = false
      this.showPrepareCountdown = false
      this.gameStarted = false
      
      // 清理动画帧
      if (this.redPacketAnimationId) {
        cancelAnimationFrame(this.redPacketAnimationId)
        this.redPacketAnimationId = null
      }
      
      // 清理生成红包的定时器
      if (this.redPacketSpawnTimer) {
        clearInterval(this.redPacketSpawnTimer)
        this.redPacketSpawnTimer = null
      }
      
      // 清理30秒超时定时器
      if (this.redPacketTimer) {
        clearTimeout(this.redPacketTimer)
        this.redPacketTimer = null
      }
      
      // 清理倒计时定时器
      if (this.redPacketCountdownTimer) {
        clearInterval(this.redPacketCountdownTimer)
        this.redPacketCountdownTimer = null
      }
      
      // 清空红包数组和重置状态
      this.redPackets = []
      this.redPacketStartTime = null
      this.redPacketRemainingTime = 30
    },

    // 生成红包奖励
    generateRedPacketReward() {
      let paramObj = {
        wheel: 2,
        member_id: parseInt(this.getStorage('member_id')),
      }
      this.$server.redemption_reward(paramObj).then((response) => {
        if(response.code==200){
          this.redPacketPrize = {
            amount: response.data.reward,
            currency: response.data.unit,
            displayAmount: `${response.data.unit}${response.data.reward}`
          }
          
          this.showRedPacketResult = true

          this.redemption()
        }else{
          this.$snackbar.warning(response.msg)
        }
      })
    },

    // 领取红包奖励
    claimRedPacketReward() {
      this.showRedPacketResult = false
      this.$snackbar.success(`Congratulations! You have won ${this.redPacketPrize.displayAmount}!`)
      this.$emit('claim-red-packet', this.redPacketPrize)
    },

    // 关闭红包结果弹窗
    closeRedPacketResult() {
      this.showRedPacketResult = false
    }
  }
}
</script>

<style>
.lucky-wheel-dialog {
  box-shadow: none !important;
  -webkit-box-shadow: none !important;
  -moz-box-shadow: none !important;
  filter: drop-shadow(none) !important;
}

.lucky-wheel-container {
  position: relative;
  max-width: 600px;
  margin: 0 auto;
}

.lucky-wheel-card {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(15px);
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 透明卡片样式 */
.transparent-card {
  background: transparent !important;
  backdrop-filter: none !important;
  box-shadow: none !important;
  border: none !important;
  padding: 0 !important;
}

/* 标题头部样式 */
.title-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: -17px; /* 与背景区域无缝连接 */
  z-index: 2;
  position: relative;
}

.title-head-image {
  max-width: 300px;
  width: auto;
  height: auto;
}

/* 标题背景区域 */
.title-background-section {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAZYAAABBCAMAAADFanKHAAABZVBMVEUAAAD/TEb/SUX/Pyj/Skb/Szb/QCn/U1D/UEv/QCn/QCn7IQf/QCn/RC7/RzH/QCn/QCnHFQD/QCn/QSr/Qiv/Qy3/QCn/RzL/QCn/QCn/QCnfGAD/Pyv/RzH/QCmYEQCfEwD/Tjr/QCmVEQCjEwD/al2vFQD/f3f/YFD/eG3/Pyf/Pib/QCn/PSX/OiP/OyT/hn//OiL/Xk3/SUD/X0//b2P/TTj/jIb/X07/LhT/Jwz/Igb9HAC9FQD/f3b/e3D/TDb/cWb/aFr/Wkf/eG3/Zlf/gHr/VEL/STP/Rj7/NRv/Rz//NyD/NR32HAD0GwDuGwDtGwDoGwDlGQDhGQDZGQDVGADdFgDRFgDNFgDMFgDGFgDDFQD/gnv/aVz/d2z/YlL/UDz/iIH/j4r/Tz3/RS7/Tjm6FAD/SjX/XUy2FACvFQCnEwD/SD7/XEuZEgD/SDP/WVT/UkueEwD/X1ugEwCPEADy33/VAAAAKnRSTlMA7+9R7wZI7+/13FEMQCUap0nn0FcxvZmFeXBQ/OKzHelk7bOc8Ovq1aA6sWKrAAADmElEQVR42u3bV1facBjH8cdqUDAMmYLU3aWpUBXBxRQU7d4LV1tr1Wqxtq+/SSomJOEfOHB4cvF8rzgSNOFz8TtihGYLhO7sZSrJZCWTSxRTm0c3LbXUkapsLeXprL5jg7b0/dKlPeJYW1aT7iRYF8m+uq+bhfPUfnE3kcjtZdKlymHyS/zeAxd0On+hWIlTbXSY3khMQGdzHRRycaqtRJa8Ezpa6KCwG6faZrkPHc13UFhMZNLStqTFcdktLjJLNdV8U/2or9Fh57pMv9m8WSmzFhu1v5/PS8MiLos4LaV4PJmUWRY56GS3RZZ8LlNKJksb0uJnhZtmDJptmPqoh7XUX1K6fkWsPvmpOW2x2CNdMc0x0gtj6mbllB/IOC/m1SgJtb5tLYmrPy+Ovrz50uQTC7EQC7EQC7HgsjhtUr36BuVGRkaGxLzE0hmWITHxPR2U69UnazgBIoJRM4+3l1dW154+f/n6zfuPn9fniKUzLN/XP314++7Vi2drqyvLT2KCYREAzkcsFmPxcQAQIBaLsQRAKkQslmIJgRzvJRYLsXh5+J+fWCzE4odaQWKxDEsQbnLZicUiLHYXKIWJxSIsYVDFOYjFEiwODtQ5icUSLE6oz0MsFmDxgCb3JLGgs0y6QdsEsaCzTIC+ILEgswTBoECUWFBZogEwaphYUFmGwTDeQSyILA4ejLMRCyKLDRrlIRY0Fg80zG0nFiQWuxsaFyEWJJYIMOJ8xILC4uOAVYBYUFgCwC5ELAgsITCJ9xJL11m8PJjlJ5aus/jBNCexdJ3FCWZxPmLpOouPA5MiNPkIkx8Bdm47sSCw2N3AzEO/t2CwCB723tOHLygs7NXnHMSCxOJgrH6YPtjHYhHCjL0nFhQW9up76K+TKCzs1bfRLRaYLIKtwd4TCwoLe/XDdPseCgt79V12Fkv5UqxavRKrVsWHCy23w2iBVbmZfhpVNqnlU11opUu5qtSVnPTIgIW9+qMCk6Vc3lkY6JcbEOvpU9XTuW5h1tNuqrekr2egVv91IlV0+4TBIowa7D2T5eT07M8FUO309+L32amehbX6vINYNCGw6FZ/WCAWTRgswrBm74lFGwqL3VW/98SiDYFFs/q9ArHowmERelV7Tyz6kFgcvLL3xKIPh0VZfVeUWAzCYom6tP/LymaZAqqdppgsSkHVbZTmLONjQLXT2DiTRcmv3HRsxnJ3mlTadpkeb4rFy8M/FvrKWfu6yu8AAAAASUVORK5CYII=);
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

/* 背景动画效果 */
@keyframes shimmer {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.title-text-main {
  color: #FFFFFF;
  font-size: 18px;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), 0 0 10px rgba(255, 255, 255, 0.3);
  margin: 0;
  letter-spacing: 1px;
  text-transform: uppercase;
  font-family: 'Arial Black', Arial, sans-serif;
}

/* Tab包装器样式 */
.tabs-wrapper {
  display: flex;
  justify-content: center;
}

/* 透明tabs样式 */
.transparent-tabs {
  background: transparent !important;
  max-width: 400px;
}

.transparent-tabs .v-tabs-bar {
  background: transparent !important;
}

.transparent-tabs .v-slide-group__content {
  justify-content: center !important;
  display: flex !important;
}

.transparent-tabs .v-tab {
  color: #d51935 !important;
  background-color: rgba(255, 255, 255, 0.6) !important;
  border-radius: 10px !important;
  margin: 0 10px !important;
  text-transform: none !important;
  min-width: 120px !important;
  flex: 0 0 auto !important;
}

.transparent-tabs .v-tab--active {
  color: #d51935 !important;
  background-color: #fff !important;
}

.transparent-tabs .v-tabs-slider {
  display: none !important;
}

/* 透明tabs-items样式 */
.transparent-tabs-items {
  background: transparent !important;
}

.transparent-tab-item {
  background: transparent !important;
}

.close-btn {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 10;
  background: rgba(0, 0, 0, 0.7) !important;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 游戏内容容器 */
.game-content-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.wheel-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

/* 图片游戏样式 */
.image-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.clickable-image {
  position: relative;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.clickable-image:hover {
  transform: scale(1.05);
}

.game-image {
  width: 280px;
  height: 280px;
  object-fit: contain;
  border-radius: 15px;
  transition: all 0.3s ease;
}

/* 点击效果 */
.click-effect {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  border-radius: 15px;
  background: radial-gradient(circle, rgba(255, 215, 0, 0.3) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.click-effect.active {
  opacity: 1;
}

.wheel-container:before{
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: -1;
  width: 380px;
  height: 400px;
  background: url('../assets/img/lucky_wheel_bg.png') no-repeat center / contain;
  -webkit-animation: spin-wheel 10s infinite linear;
  animation: spin-wheel 10s infinite linear;
}

/* 旋转动画定义 */
@keyframes spin-wheel {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@-webkit-keyframes spin-wheel {
  0% {
    -webkit-transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    -webkit-transform: translate(-50%, -50%) rotate(360deg);
  }
}

/* 倒计时区域样式 */
.countdown-section {
  margin-bottom: 20px;
}

.countdown-title {
  margin-bottom: 12px;
}

.title-text {
  display: inline-block;
  color: #FFD700;
  font-size: 16px;
  font-weight: bold;
  background: linear-gradient(135deg, #FF6B35, #F7931E);
  padding: 8px 16px;
  border-radius: 20px;
  border: 2px solid #FFD700;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.countdown-timer {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
}

.time-unit {
  text-align: center;
}

.time-number {
  background: linear-gradient(135deg, #1e3c72, #2a5298);
  color: #FFFFFF;
  font-size: 24px;
  font-weight: bold;
  padding: 8px 12px;
  border-radius: 8px;
  border: 2px solid #FFD700;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  min-width: 50px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
}

.time-label {
  color: #FFD700;
  font-size: 12px;
  font-weight: bold;
  margin-top: 4px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.time-separator {
  color: #FFD700;
  font-size: 20px;
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  margin: 0 4px;
}

.wheel-info {
  max-width: 400px;
  margin: 0 auto;
}

/* 中奖信息展示样式 */
.winner-info-container {
  background: url('../assets/img/lucky_rank_bg.png') no-repeat center center;
  background-size: contain;
  border-radius: 12px;
  padding: 8px 40px;
  margin-top: 12px;
  position: relative;
  overflow: hidden;
  min-height: 140px;
}

.winner-info-header {
  text-align: center;
  margin-bottom: 12px;
}

.winner-header-text {
  color: #FFD700;
  font-weight: bold;
  font-size: 14px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.winner-scroll-container {
  height: 60px;
  overflow: hidden;
  position: relative;
  padding: 0;
}

.winner-scroll-content {
  transition: transform 0.5s ease-in-out;
  padding: 0;
}

.winner-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 12px;
  height: 30px;
}

.winner-name {
  color: #FFFFFF;
  font-size: 12px;
  font-weight: 500;
  flex: 1;
  text-align: left;
}

.winner-amount {
  color: #FFD700;
  font-size: 12px;
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.result-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.result-card .v-card__title,
.result-card .v-card__text {
  color: white;
}

.prize-image {
  padding: 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: inline-block;
}

.prize-name {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 8px;
}

.prize-description {
  opacity: 0.9;
}

/* 中奖结果弹窗关闭按钮样式 */
.result-close-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 10;
  background: rgba(255, 255, 255, 0.2) !important;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.result-close-btn:hover {
  background: rgba(255, 255, 255, 0.3) !important;
  transform: scale(1.1);
}

.result-close-btn .v-icon {
  color: white !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .lucky-wheel-container {
    max-width: 90vw;
  }
  
  .wheel-container {
    min-height: 250px;
  }
  
  .title-text {
    font-size: 14px;
    padding: 6px 12px;
  }
  
  .time-number {
    font-size: 20px;
    padding: 6px 8px;
    min-width: 40px;
  }
  
  .time-label {
    font-size: 10px;
  }
  
  .time-separator {
    font-size: 16px;
  }
  
  .game-image {
    width: 240px;
    height: 240px;
  }
  
  .title-text-main {
    font-size: 16px;
  }
  
  .title-background-section {
    padding: 10px 15px 15px 15px;
  }

  .title-head-image {
    max-width: 200px;
  }

  .title-header {
    margin-bottom: -22px;
  }
}

/* 活动条件状态区域样式 */
.activity-status-section {
  max-width: 400px;
  margin: 0 auto;
}

/* Claim按钮样式 */
.claim-button-container {
  padding: 0 20px;
}

.claim-btn {
  font-weight: bold !important;
  text-transform: none !important;
  border-radius: 20px !important;
  height: 40px !important;
  font-size: 14px !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  box-shadow: 0 3px 10px rgba(102, 126, 234, 0.4) !important;
  transition: all 0.3s ease !important;
}

.claim-btn:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.6) !important;
}

.claim-btn:disabled {
  background: linear-gradient(135deg, #bdbdbd 0%, #757575 100%) !important;
  color: rgba(255, 255, 255, 0.7) !important;
  box-shadow: none !important;
  transform: none !important;
}

/* 条件引导弹窗样式 */
.conditions-card {
  border-radius: 20px !important;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
  position: relative;
}

/* 动画效果 */
.lucky-wheel-dialog {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 条件弹窗动画 */
.conditions-card {
  animation: slideUp 0.4s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 条件弹窗关闭按钮样式 */
.conditions-close-btn {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 10;
  background: rgba(0, 0, 0, 0.1) !important;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.conditions-close-btn:hover {
  background: rgba(0, 0, 0, 0.2) !important;
  transform: scale(1.1);
}

/* 进度条容器毛玻璃效果 */
.progress-container {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 总体进度毛玻璃效果 */
.overall-progress {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

/* 条件列表毛玻璃效果 */
.conditions-list {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

/* 条件卡片毛玻璃效果 */
.condition-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.25);
  transition: all 0.3s ease;
  backdrop-filter: blur(3px);
  -webkit-backdrop-filter: blur(3px);
}

.condition-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.35);
}

/* 红包雨效果样式 */
.red-packet-rain-dialog {
  overflow: hidden !important;
}

/* 准备倒计时样式 */
.prepare-countdown-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 200;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
}

.prepare-countdown-content {
  text-align: center;
  color: white;
}

.prepare-title {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 30px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
  animation: titlePulse 2s ease-in-out infinite;
}

.prepare-countdown-number {
  font-size: 8rem;
  font-weight: bold;
  color: #FFD700;
  text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8);
  margin: 20px 0;
  animation: countdownBounce 0.8s ease-in-out;
}

.prepare-subtitle {
  font-size: 1.5rem;
  margin-top: 20px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
  animation: subtitleFade 2s ease-in-out infinite alternate;
}

@keyframes titlePulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes countdownBounce {
  0% {
    transform: scale(0.5);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes subtitleFade {
  0% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

.red-packet-rain-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

.rain-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  opacity: 0.9;
}

.rain-close-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(10px);
}

.rain-title {
  position: absolute;
  top: 80px;
  left: 50%;
  width: 100%;
  transform: translateX(-50%);
  z-index: 100;
  color: white;
  text-align: center;
}

.rain-title h2 {
  font-size: 2.5rem;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  margin: 0;
}

.rain-title p {
  font-size: 1.2rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  margin: 0;
}

.rain-countdown {
  margin-top: 10px;
}

/* 游戏倒计时容器 */
.game-countdown-container {
  display: inline-block;
  background: rgba(0, 0, 0, 0.4);
  padding: 8px 16px;
  border-radius: 20px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 215, 0, 0.3);
}

.countdown-text {
  font-size: 1.1rem;
  font-weight: bold;
  color: #FFD700;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.red-packet {
  position: absolute;
  cursor: pointer;
  font-size: 40px;
  transition: transform 0.2s ease;
  z-index: 50;
  user-select: none;
  animation: redPacketGlow 2s ease-in-out infinite alternate;
  /* 硬件加速优化 */
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

.red-packet:hover {
  transform: scale(1.2) !important;
}

.red-packet.clicked {
  animation: redPacketClick 0.5s ease-out forwards;
  pointer-events: none;
}

@keyframes redPacketGlow {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.05);
  }
}

@keyframes redPacketClick {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.5);
    opacity: 0.8;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* 红包结果弹窗样式 */
.red-packet-result-card {
  border-radius: 20px !important;
  overflow: hidden;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%) !important;
  color: white !important;
  box-shadow: 0 10px 30px rgba(238, 90, 82, 0.3) !important;
}

.red-packet-icon {
  font-size: 4rem;
  animation: redPacketBounce 1s ease-in-out infinite;
}

@keyframes redPacketBounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

.prize-amount-display {
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.amount-text {
  font-size: 2.5rem;
  font-weight: bold;
  color: #fff;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  margin-bottom: 5px;
}

.amount-label {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.celebration-text {
  color: rgba(255, 255, 255, 0.95);
}

.celebration-text p {
  margin: 0;
}

/* 红包结果弹窗动画 */
.red-packet-result-card {
  animation: redPacketResultShow 0.6s ease-out;
}

@keyframes redPacketResultShow {
  0% {
    opacity: 0;
    transform: scale(0.5) rotate(-10deg);
  }
  60% {
    opacity: 1;
    transform: scale(1.1) rotate(2deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

/* iOS设备专用优化 */
@supports (-webkit-touch-callout: none) {
  .red-packet {
    /* iOS Safari专用优化 */
    -webkit-transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    -webkit-perspective: 1000;
    transform: translate3d(0, 0, 0);
    animation: none !important;
    transition: none !important;
  }
  
  .rain-background {
    /* iOS简化背景 */
    background: #667eea;
  }
  
  .red-packet-rain-container {
    /* iOS强制硬件加速 */
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }
  
  /* iOS倒计时优化 */
  .game-countdown-container {
    backdrop-filter: none !important;
    background: rgba(0, 0, 0, 0.7) !important;
  }
  
  /* iOS准备倒计时优化 */
  .prepare-countdown-overlay {
    backdrop-filter: none !important;
    background: rgba(0, 0, 0, 0.6) !important;
  }
  
  .prepare-countdown-number {
    /* 确保iOS显示准备倒计时 */
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }
}



/* 响应式设计 - 红包雨 */
@media (max-width: 768px) {
  .rain-title h2 {
    font-size: 2rem;
  }
  
  .rain-title p {
    font-size: 1rem;
  }
  
  .red-packet {
    font-size: 35px;
    /* 移动设备优化：减少动画复杂度 */
    animation: none;
    will-change: transform;
  }
  
  .red-packet:hover {
    transform: scale(1.1) !important;
  }
  
  .amount-text {
    font-size: 2rem;
  }
  
  /* 移动设备性能优化 */
  .rain-background {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    /* 移除复杂的背景效果 */
  }
  
  /* 游戏倒计时移动端优化 */
  .game-countdown-container {
    padding: 6px 12px;
    /* 移除 backdrop-filter 提升性能 */
    backdrop-filter: none;
    background: rgba(0, 0, 0, 0.6);
    border: 1px solid rgba(255, 215, 0, 0.3);
  }
  
  .countdown-text {
    font-size: 1rem;
  }
  
  /* 准备倒计时移动端优化 */
  .prepare-title {
    font-size: 1.8rem;
    margin-bottom: 20px;
  }
  
  .prepare-countdown-number {
    font-size: 5rem;
    margin: 15px 0;
  }
  
  .prepare-subtitle {
    font-size: 1.2rem;
    margin-top: 15px;
  }
  
  .prepare-countdown-overlay {
    /* 移动设备移除毛玻璃效果 */
    backdrop-filter: none;
    background: rgba(0, 0, 0, 0.5);
  }
}

</style> 