:root {
    --swiper-navigation-size: 30px!important;
}
/* Color */
/*
.theme--light.v-application {
    background: #F6F7FA!important;
}
.theme--dark.v-application {
    background: #24262B!important;
}

.theme--red.v-application {
    background: #ef8886!important;
}
.theme--purple.v-application {
    background: #4527A0!important;
}
.theme--blue.v-application {
    background: #181e67!important;
}
*/
.bg-light-a2{background-color: rgba(255,255,255,.2)!important;}
.bg-dark-a2{background-color: rgba(0,0,0,.2)!important;}
.bg-light-a4{background-color: rgba(255,255,255,.4)!important;}


.theme--light.bg-a2{background-color: rgba(0,0,0,.2)!important;}
.theme--dark.bg-a2{background-color: rgba(255,255,255,.2)!important;}

.opacity-1{opacity: .1!important;}
.opacity-2{opacity: .2!important;}
.opacity-3{opacity: .3!important;}
.opacity-4{opacity: .4!important;}
.opacity-5{opacity: .5!important;}
.opacity-6{opacity: .6!important;}
.opacity-7{opacity: .7!important;}
.opacity-8{opacity: .8!important;}
.opacity-9{opacity: .9!important;}

/* add */
.currency{
    font-size: 1rem;
    font-weight: 600;
}
.coin-icon{
    width: 1.6rem;
}
.h-36{
    min-height: 36px!important;
}

.swiper-navBtn{
    color: rgba(255, 255, 255, .3)!important;
    transition: color 0.3s ease;
}
.swiper-navBtn:hover{
    color: rgba(255, 255, 255, .8)!important;
}
    .swiper-navBtn::before,
    .swiper-navBtn::after{
    font-size: 35px;
}

.swiper-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1!important; 
}

.swiper-pagination-bullet {
    width: 0.625rem!important;
    height: 0.625rem!important;
    border-radius: .3125rem!important;
    background-color: var(--v-box_bg-base)!important;
    margin: 0 0.3125rem!important;
    cursor: pointer;
    transition: width .3s linear;
    opacity: 0.4!important;
}

.swiper-pagination-bullet-active {
    background-color: var(--v-box_bg-base)!important;
    width: 1.75rem!important;
    opacity: 0.8!important;
}

.box-no-bg:before{
    background-color: transparent!important;
}

.position-relative{
    position: relative;
}

.btn-center {
    left: 50%;
    transform: translateX(-50%);
  }

@-webkit-keyframes swing{ 
	10% {
	  transform: rotate(15deg); 
	}
	20% {
	  transform: rotate(-10deg); 
	}
	30% {
	  transform: rotate(5deg); 
	}
	40% {
	  transform: rotate(-5deg); 
	}
	50%,100% {
	  transform: rotate(0deg); 
	}
}

.img-gray{
    -webkit-filter: grayscale(1); /* Webkit */
    filter: gray; /* IE6-9 */  
    filter: grayscale(1); /* W3C */
}
.animat-swing {
    animation: shake 2s infinite;
  }
  
  @keyframes shake {
    10%, 90% { transform: translate3d(-1px, 0, 0); }
    20%, 80% { transform: translate3d(+2px, 0, 0); }
    30%, 70% { transform: translate3d(-4px, 0, 0); }
    40%, 60% { transform: translate3d(+4px, 0, 0); }
    50% { transform: translate3d(-4px, 0, 0); }
  }

.animat-breath {
    animation: breathing 2s ease-in-out infinite;
  }
  
  @keyframes breathing {
    0%, 100% { 
      transform: scale(1); 
    }
    50% { 
      transform: scale(1.2); 
    }
  }
