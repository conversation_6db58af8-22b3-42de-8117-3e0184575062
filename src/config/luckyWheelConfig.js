// 转盘配置文件
export const luckyWheelConfig = [
  {
    name: '每日转盘',
    description: '每日免费抽奖一次，赢取丰厚奖励！',
    rules: '每日限制一次，重置时间：每日00:00',
    // 参与条件
    conditions: ['dailyDeposit', 'appDownload'],
    claimText: 'Claim Daily Spin',
    lockedText: 'Complete Requirements',
    blocks: [{ 
      padding: '15px', 
      background: '#24262b',
      imgs: [{
        src: require('@/assets/img/lucky_bg.png'),
        width: '100%',
        height: '100%',
        rotate: 'true'
      }]
    }],
    prizes: [
      { 
        fonts: [{ 
          text: '+₱5', 
          fontColor: '#ffffe3', 
          fontSize: '14px', 
          fontWeight: 'bold', 
          top: '30%' 
        }],
        imgs: [{ 
          src: require('@/assets/img/icons/bonus.svg'),
          top: '55%',
          width: '36px',
          height: '36px'
        }]
      },
      { 
        fonts: [{ 
          text: '+₱10', 
          fontColor: '#fd408a', 
          fontSize: '14px', 
          fontWeight: 'bold', 
          top: '30%' 
        }],
        imgs: [{ 
          src: require('@/assets/img/icons/bonus.svg'),
          top: '55%',
          width: '36px',
          height: '36px'
        }]
      },
      { 
        fonts: [{ 
          text: '+₱20', 
          fontColor: '#ffffe3', 
          fontSize: '14px', 
          fontWeight: 'bold', 
          top: '30%' 
        }],
        imgs: [{ 
          src: require('@/assets/img/icons/bonus.svg'),
          top: '55%',
          width: '36px',
          height: '36px'
        }]
      },
      { 
        fonts: [{ 
          text: '+₱50', 
          fontColor: '#fd408a', 
          fontSize: '14px', 
          fontWeight: 'bold', 
          top: '30%' 
        }],
        imgs: [{ 
          src: require('@/assets/img/icons/bonus.svg'),
          top: '55%',
          width: '36px',
          height: '36px'
        }]
      },
      { 
        fonts: [{ 
          text: '+₱100', 
          fontColor: '#ffffe3', 
          fontSize: '14px', 
          fontWeight: 'bold', 
          top: '30%' 
        }],
        imgs: [{ 
          src: require('@/assets/img/icons/bonus.svg'),
          top: '55%',
          width: '36px',
          height: '36px'
        }]
      },
      { 
        fonts: [{ 
          text: '谢谢参与', 
          fontColor: '#fd408a', 
          fontSize: '12px', 
          fontWeight: 'bold', 
          top: '30%' 
        }],
        imgs: [{ 
          src: require('@/assets/img/icons/slots.png'),
          top: '55%',
          width: '36px',
          height: '36px'
        }]
      }
    ],
    buttons: [{
      radius: '30%',
      pointer: true,
      fonts: [{
        top: '-10px',
        text: 'Go',
        fontColor: '#fff',
        fontWeight: 'bold'
      }],
      imgs: [{
        top: '-58px',
        src: require('@/assets/img/lucky_btn.png'),
        width: '80px',
        height: '100px'
      }]
    }]
  },
  {
    name: '赢取iPhone手机',
    description: 'VIP专享iPhone抽奖，大奖等你来拿！',
    rules: 'VIP用户专享，每日可参与多次',
    // 参与条件
    conditions: ['vipLevel', 'dailyBet', 'realNameAuth'],
    claimText: 'Claim VIP Reward',
    lockedText: 'VIP Requirements',
    blocks: [{ 
      padding: '15px', 
      background: '#24262b',
      imgs: [{
        src: require('@/assets/img/lucky_bg.png'),
        width: '100%',
        height: '100%',
        rotate: 'true'
      }]
    }],
    prizes: [
      { 
        fonts: [{ 
          text: 'iPhone 15', 
          fontColor: '#FFD700', 
          fontSize: '12px', 
          fontWeight: 'bold', 
          top: '25%' 
        }],
        imgs: [{ 
          src: require('@/assets/img/phone-iphone15.webp'),
          top: '50%',
          width: '40px',
          height: '40px'
        }]
      },
      { 
        fonts: [{ 
          text: '+₱500', 
          fontColor: '#fd408a', 
          fontSize: '14px', 
          fontWeight: 'bold', 
          top: '30%' 
        }],
        imgs: [{ 
          src: require('@/assets/img/icons/bonus.svg'),
          top: '55%',
          width: '36px',
          height: '36px'
        }]
      },
      { 
        fonts: [{ 
          text: '+₱200', 
          fontColor: '#ffffe3', 
          fontSize: '14px', 
          fontWeight: 'bold', 
          top: '30%' 
        }],
        imgs: [{ 
          src: require('@/assets/img/icons/bonus.svg'),
          top: '55%',
          width: '36px',
          height: '36px'
        }]
      },
      { 
        fonts: [{ 
          text: '+₱100', 
          fontColor: '#fd408a', 
          fontSize: '14px', 
          fontWeight: 'bold', 
          top: '30%' 
        }],
        imgs: [{ 
          src: require('@/assets/img/icons/bonus.svg'),
          top: '55%',
          width: '36px',
          height: '36px'
        }]
      },
      { 
        fonts: [{ 
          text: '+₱50', 
          fontColor: '#ffffe3', 
          fontSize: '14px', 
          fontWeight: 'bold', 
          top: '30%' 
        }],
        imgs: [{ 
          src: require('@/assets/img/icons/bonus.svg'),
          top: '55%',
          width: '36px',
          height: '36px'
        }]
      },
      { 
        fonts: [{ 
          text: '谢谢参与', 
          fontColor: '#fd408a', 
          fontSize: '12px', 
          fontWeight: 'bold', 
          top: '30%' 
        }],
        imgs: [{ 
          src: require('@/assets/img/icons/slots.png'),
          top: '55%',
          width: '36px',
          height: '36px'
        }]
      }
    ],
    buttons: [{
      radius: '30%',
      pointer: true,
      fonts: [{
        top: '-10px',
        text: 'Go',
        fontColor: '#fff',
        fontWeight: 'bold'
      }],
      imgs: [{
        top: '-58px',
        src: require('@/assets/img/lucky_btn.png'),
        width: '80px',
        height: '100px'
      }]
    }]
  },
  {
    name: '红包抽奖',
    type: 'image',
    image: require('@/assets/img/lucky_raffle.webp'),
    description: '点击红包，领取惊喜奖励！',
    rules: '每日限制3次，重置时间：每日00:00',
    // 参与条件
    conditions: ['dailyBet', 'appDownload'],
    claimText: 'Claim Red Packet',
    lockedText: 'Complete Tasks',
    winners: [
      { name: 'Luc***ky', amount: '₱1,888.00' },
      { name: 'Red***et', amount: '₱2,500.00' },
      { name: 'Win***er', amount: '₱3,200.00' },
      { name: 'Gol***en', amount: '₱1,500.00' },
      { name: 'Bon***us', amount: '₱4,600.00' },
      { name: 'Ric***hy', amount: '₱2,999.00' },
      { name: 'Hap***py', amount: '₱1,800.00' },
      { name: 'Sup***er', amount: '₱3,800.00' },
      { name: 'Meg***aa', amount: '₱2,200.00' },
      { name: 'Ult***ra', amount: '₱5,500.00' }
    ]
  },
  {
    name: '开宝箱',
    type: 'image',
    image: require('@/assets/img/lucky_cash_box.webp'),
    description: '打开神秘宝箱，获得丰厚奖励！',
    rules: 'VIP用户专享，每日可开启5次',
    // 参与条件
    conditions: ['vipLevel', 'realNameAuth', 'dailyDeposit'],
    claimText: 'Open Treasure Box',
    lockedText: 'VIP Access Required',
    winners: [
      { name: 'Tre***re', amount: '₱2,888.00' },
      { name: 'Dia***nd', amount: '₱4,500.00' },
      { name: 'Gem***st', amount: '₱3,600.00' },
      { name: 'Gol***ox', amount: '₱5,200.00' },
      { name: 'Sil***er', amount: '₱2,100.00' },
      { name: 'Pla***um', amount: '₱6,800.00' },
      { name: 'Cry***al', amount: '₱3,300.00' },
      { name: 'Jew***el', amount: '₱4,700.00' },
      { name: 'Rar***ty', amount: '₱2,900.00' },
      { name: 'Epi***ic', amount: '₱7,200.00' }
    ]
  }
]

// 导出默认配置
export default luckyWheelConfig 