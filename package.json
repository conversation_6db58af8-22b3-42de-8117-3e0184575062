{"name": "brazil_webapp", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@fingerprintjs/fingerprintjs": "^4.2.2", "@lucky-canvas/vue": "^0.1.11", "@mdi/font": "^7.2.96", "axios": "^0.27.2", "canvas-confetti": "^1.6.0", "core-js": "^3.6.5", "crisp-sdk-web": "^1.0.21", "crypto-js": "^4.1.1", "dayjs": "^1.11.9", "eslint": "6.8.0", "swiper": "5", "typeface-roboto": "^1.1.13", "vue": "^2.6.11", "vue-awesome-swiper": "4", "vue-clipboard2": "^0.3.3", "vue-facebook-pixel": "^1.2.1", "vue-fullscreen": "^2.6.1", "vue-i18n": "8", "vue-router": "^3.2.0", "vuetify": "^2.6.0", "vuex": "3", "xgplayer": "^3.0.20", "xgplayer-hls": "^3.0.20"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.15", "@vue/cli-plugin-eslint": "~4.5.15", "@vue/cli-plugin-router": "~4.5.15", "@vue/cli-service": "~4.5.15", "babel-eslint": "^10.1.0", "eslint-plugin-vue": "^6.2.2", "sass": "~1.32.0", "sass-loader": "^10.0.0", "vue-cli-plugin-vuetify": "~2.5.1", "vue-template-compiler": "^2.6.11", "vuetify-loader": "^1.7.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}