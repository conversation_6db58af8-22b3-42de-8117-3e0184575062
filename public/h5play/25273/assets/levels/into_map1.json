{"layers": [[{"params": {"x": 5317, "vertices": [{"x": -5, "y": -110, "length": 110.11357772772621}, {"x": 890, "y": -110, "length": 896.7719888578144}, {"x": 890, "y": 70, "length": 892.7485648266257}, {"x": 860, "y": 70, "length": 862.8441342444185}, {"x": 825, "y": 25, "length": 825.3787009609589}, {"x": 760, "y": 25, "length": 760.4110730387873}, {"x": 725, "y": 80, "length": 729.4004387166216}, {"x": 690, "y": 80, "length": 694.6221994724903}, {"x": 650, "y": 25, "length": 650.4805915628843}, {"x": 585, "y": 25, "length": 585.533944361896}, {"x": 555, "y": 80, "length": 560.7361233236182}, {"x": 515, "y": 80, "length": 521.1765535785354}, {"x": 480, "y": 23, "length": 480.5507257303853}, {"x": 425, "y": 24, "length": 425.677107676699}, {"x": 395, "y": 80, "length": 403.01985062773275}, {"x": 355, "y": 80, "length": 363.90245945857527}, {"x": 319, "y": 26, "length": 320.0578072786227}, {"x": 266, "y": 27, "length": 267.36678926149375}, {"x": 236, "y": 81, "length": 249.51352668743232}, {"x": 192, "y": 81, "length": 208.38665984174708}, {"x": 160, "y": 27, "length": 162.2621335986927}, {"x": 104, "y": 28, "length": 107.70329614269008}, {"x": 75, "y": 85, "length": 113.35784048754634}, {"x": 30, "y": 85, "length": 90.13878188659973}, {"x": 0, "y": 35, "length": 35}], "y": -459, "rem": true, "isStatic": false, "rotation": -36, "graphic": true, "plr": false, "ignore": false, "id": -1, "density": 1, "safeId": 1, "line": true, "isWheel": false, "layer": 4, "physic": true}, "className": "frg.game.editor.objects::LandscapeShaper"}, {"params": {"x": 5419, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 35, "y": -35, "length": 49.49747468305833}, {"x": 95, "y": 3, "length": 95.04735661763561}, {"x": 140, "y": -15, "length": 140.80127840328723}, {"x": 156, "y": -85, "length": 177.65415840897168}, {"x": 211, "y": -85, "length": 227.477471412006}, {"x": 222, "y": -15, "length": 222.50617968946392}, {"x": 272, "y": 2, "length": 272.0073528417936}, {"x": 329, "y": -35, "length": 330.8564643467013}, {"x": 366, "y": 5, "length": 366.0341514121326}, {"x": 331, "y": 59, "length": 336.21719170797917}, {"x": 347, "y": 105, "length": 362.5382738415353}, {"x": 419, "y": 124, "length": 436.9633851937711}, {"x": 418, "y": 175, "length": 453.15449903978663}, {"x": 348, "y": 189, "length": 396.01136347332255}, {"x": 328, "y": 232, "length": 401.756144943671}, {"x": 366, "y": 302, "length": 474.51027386137804}, {"x": 330, "y": 335, "length": 470.2393007820593}, {"x": 272, "y": 293, "length": 399.7911955008514}, {"x": 227, "y": 314, "length": 387.45967532118743}, {"x": 205, "y": 385, "length": 436.1765697512878}, {"x": 157, "y": 384, "length": 414.85539649376625}, {"x": 141, "y": 314, "length": 344.2048808486016}, {"x": 99, "y": 297, "length": 313.06548835666956}, {"x": 34, "y": 332, "length": 333.73642294481436}, {"x": 0, "y": 299, "length": 299}, {"x": 37, "y": 234, "length": 236.90715480964266}, {"x": 20, "y": 192, "length": 193.03885619221847}, {"x": -50, "y": 174, "length": 181.0414317221337}, {"x": -52, "y": 124, "length": 134.46189051177288}, {"x": 17, "y": 106, "length": 107.35455276791944}, {"x": 37, "y": 64, "length": 73.92563831310488}], "y": -488, "rem": true, "isStatic": false, "rotation": 0, "graphic": true, "plr": true, "ignore": false, "id": -1, "density": 1, "safeId": 1, "line": true, "isWheel": false, "layer": 4, "physic": true}, "className": "frg.game.editor.objects::LandscapeShaper"}, {"params": {"snapToGrid": true, "vertices": [{"x": 0, "y": -155, "length": 155}, {"x": 0, "y": -445, "length": 445}, {"x": 100, "y": -445, "length": 456.0975772792484}, {"x": 100, "y": -155, "length": 184.45866745696716}], "wireframe": false, "width": 118, "rotation": 0, "textureMode": true, "height": 305.5, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 2, "smoothing": true, "y": -185, "repeatTexture": true, "textureOffset": 0, "x": 5554}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"snapToGrid": true, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 0, "y": -445, "length": 445}, {"x": 505, "y": -805, "length": 950.289429595005}, {"x": 1020, "y": -805, "length": 1299.394089566364}], "wireframe": false, "width": 1035.5, "rotation": 0, "textureMode": true, "height": 820.5, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 2, "smoothing": true, "y": 175, "repeatTexture": true, "textureOffset": 0, "x": 5014}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"snapToGrid": true, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 0, "y": -445, "length": 445}, {"x": 100, "y": -445, "length": 456.0975772792484}, {"x": 100, "y": 0, "length": 100}], "wireframe": false, "width": 118, "rotation": 0, "textureMode": true, "height": 460.5, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 2, "smoothing": true, "y": 105, "repeatTexture": true, "textureOffset": 0, "x": 5554}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"bg": true, "x": 4480, "id": -1, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 1385, "y": 0, "length": 1385}, {"x": 1390, "y": 405, "length": 1447.8000552562498}, {"x": 5, "y": 405, "length": 405.0308630215727}], "y": -190, "viscosity": 1.5, "rotation": 0, "density": 1.5, "sf": true}, "className": "frg.game.editor.objects::WaterShaper"}, {"params": {"snapToGrid": true, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 240, "y": 0, "length": 240}], "wireframe": false, "width": 253, "rotation": 0, "textureMode": true, "height": 13, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 0, "smoothing": true, "y": 390, "repeatTexture": true, "textureOffset": 0, "x": 1340}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"snapToGrid": true, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 240, "y": 0, "length": 240}], "wireframe": false, "width": 253, "rotation": 0, "textureMode": true, "height": 13, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 0, "smoothing": true, "y": 270, "repeatTexture": true, "textureOffset": 0, "x": 1570}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"snapToGrid": true, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 100, "y": -100, "length": 141.4213562373095}, {"x": 0, "y": -200, "length": 200}, {"x": 100, "y": -300, "length": 316.22776601683796}, {"x": 0, "y": -400, "length": 400}, {"x": 100, "y": -500, "length": 509.9019513592785}, {"x": 0, "y": -600, "length": 600}, {"x": 100, "y": -700, "length": 707.1067811865476}, {"x": 0, "y": -800, "length": 800}, {"x": 100, "y": -900, "length": 905.5385138137417}, {"x": 0, "y": -1000, "length": 1000}], "wireframe": false, "width": 117, "rotation": 0, "textureMode": true, "height": 1013, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 0, "smoothing": true, "y": 560, "repeatTexture": true, "textureOffset": 0, "x": -20}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"snapToGrid": true, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 100, "y": -100, "length": 141.4213562373095}, {"x": 0, "y": -200, "length": 200}, {"x": 100, "y": -300, "length": 316.22776601683796}, {"x": 0, "y": -400, "length": 400}, {"x": 100, "y": -500, "length": 509.9019513592785}, {"x": 0, "y": -600, "length": 600}, {"x": 100, "y": -700, "length": 707.1067811865476}, {"x": 0, "y": -800, "length": 800}, {"x": 100, "y": -900, "length": 905.5385138137417}, {"x": 0, "y": -1000, "length": 1000}], "wireframe": false, "width": 117, "rotation": 0, "textureMode": true, "height": 1013, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 0, "smoothing": true, "y": 460, "repeatTexture": true, "textureOffset": 0, "x": -20}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"x": -20, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 0, "y": -1115, "length": 1115}, {"x": -680, "y": -1115, "length": 1305.9957886608977}], "wireframe": false, "textureOffset": 0, "width": 696.5, "rotation": 0, "snapToGrid": true, "height": 1131.5, "action": 0, "y": 480, "stretchTexture": false, "type": 0, "isStatic": true, "textureMode": true, "id": -1, "density": "1", "safeId": -1, "shapeH": 20, "originOffsetRatio": 0, "repeatTexture": true, "graphic": true, "physic": true, "smoothing": true, "straightSides": true}, "className": "frg.game.editor.objects::DynamicPather"}, {"params": {"snapToGrid": true, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 0, "y": 250, "length": 250}], "wireframe": false, "width": 13, "rotation": 0, "textureMode": true, "height": 263, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 0, "smoothing": true, "y": 257, "repeatTexture": true, "textureOffset": 0, "x": 1580}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"snapToGrid": true, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 0, "y": 250, "length": 250}], "wireframe": false, "width": 13, "rotation": 0, "textureMode": true, "height": 263, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 0, "smoothing": true, "y": 387, "repeatTexture": true, "textureOffset": 0, "x": 1340}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"snapToGrid": true, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 0, "y": 250, "length": 250}], "wireframe": false, "width": 13, "rotation": 0, "textureMode": true, "height": 263, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 0, "smoothing": true, "y": 97, "repeatTexture": true, "textureOffset": 0, "x": 1810}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"x": 1060, "vertices": [{"x": -10, "y": 2, "length": 10.198039027185569}, {"x": 72, "y": -14, "length": 73.348483283569}, {"x": 107, "y": -23, "length": 109.4440496326776}, {"x": 150, "y": -36, "length": 154.2595215861893}, {"x": 188, "y": -49, "length": 194.28072472584614}, {"x": 231, "y": -65, "length": 239.97083156083784}, {"x": 279, "y": -85, "length": 291.66076184499}, {"x": 326, "y": -106, "length": 342.80023337214925}, {"x": 373, "y": -128, "length": 394.3513661698156}, {"x": 430, "y": -157, "length": 457.7652236682031}, {"x": 490, "y": -190, "length": 525.5473337388365}, {"x": 550, "y": -225, "length": 594.2432162002357}, {"x": 600, "y": -258, "length": 653.1186722181506}, {"x": 643, "y": -288, "length": 704.5516304714652}, {"x": 670, "y": -309, "length": 737.821794202367}, {"x": 701, "y": -334, "length": 776.5030585902415}, {"x": 757, "y": -381, "length": 847.4727134250401}], "wireframe": false, "textureOffset": 0, "width": 780, "rotation": 0, "snapToGrid": false, "height": 400.45, "action": 0, "y": 470, "stretchTexture": false, "type": 0, "isStatic": true, "textureMode": true, "id": -1, "density": "1", "safeId": -1, "shapeH": 20, "originOffsetRatio": 0, "repeatTexture": true, "graphic": true, "physic": true, "smoothing": true, "straightSides": true}, "className": "frg.game.editor.objects::DynamicPather"}, {"params": {"x": 2228, "vertices": [{"x": 350, "y": 0, "length": 350}, {"x": 348, "y": 31, "length": 349.3780187705002}, {"x": 343, "y": 68, "length": 349.67556391603915}, {"x": 335, "y": 98, "length": 349.04011230802683}, {"x": 323, "y": 133, "length": 349.31074990615446}, {"x": 310, "y": 162, "length": 349.77707186149297}, {"x": 291, "y": 194, "length": 349.73847372000694}, {"x": 272, "y": 219, "length": 349.20624278497655}, {"x": 247, "y": 247, "length": 349.31074990615446}, {"x": 224, "y": 268, "length": 349.28498393145964}, {"x": 194, "y": 291, "length": 349.73847372000694}, {"x": 167, "y": 307, "length": 349.48247452483224}, {"x": 133, "y": 323, "length": 349.31074990615446}, {"x": 104, "y": 334, "length": 349.817095065407}, {"x": 68, "y": 343, "length": 349.67556391603915}, {"x": 37, "y": 347, "length": 348.9670471548854}, {"x": 0, "y": 350, "length": 350}, {"x": -31, "y": 348, "length": 349.3780187705002}, {"x": -68, "y": 343, "length": 349.67556391603915}, {"x": -98, "y": 335, "length": 349.04011230802683}, {"x": -133, "y": 323, "length": 349.31074990615446}, {"x": -162, "y": 310, "length": 349.77707186149297}, {"x": -194, "y": 291, "length": 349.73847372000694}, {"x": -219, "y": 272, "length": 349.20624278497655}, {"x": -247, "y": 247, "length": 349.31074990615446}, {"x": -268, "y": 224, "length": 349.28498393145964}, {"x": -291, "y": 194, "length": 349.73847372000694}, {"x": -307, "y": 167, "length": 349.48247452483224}, {"x": -323, "y": 133, "length": 349.31074990615446}, {"x": -334, "y": 104, "length": 349.817095065407}, {"x": -343, "y": 68, "length": 349.67556391603915}, {"x": -347, "y": 37, "length": 348.9670471548854}, {"x": -350, "y": 0, "length": 350}, {"x": -348, "y": -31, "length": 349.3780187705002}, {"x": -343, "y": -68, "length": 349.67556391603915}, {"x": -335, "y": -98, "length": 349.04011230802683}, {"x": -323, "y": -133, "length": 349.31074990615446}, {"x": -310, "y": -162, "length": 349.77707186149297}, {"x": -291, "y": -194, "length": 349.73847372000694}, {"x": -272, "y": -219, "length": 349.20624278497655}, {"x": -247, "y": -247, "length": 349.31074990615446}, {"x": -224, "y": -268, "length": 349.28498393145964}, {"x": -194, "y": -291, "length": 349.73847372000694}, {"x": -167, "y": -307, "length": 349.48247452483224}, {"x": -133, "y": -323, "length": 349.31074990615446}, {"x": -104, "y": -334, "length": 349.817095065407}, {"x": -68, "y": -343, "length": 349.67556391603915}, {"x": -37, "y": -347, "length": 348.9670471548854}, {"x": 0, "y": -350, "length": 350}, {"x": 31, "y": -348, "length": 349.3780187705002}, {"x": 68, "y": -343, "length": 349.67556391603915}, {"x": 98, "y": -335, "length": 349.04011230802683}, {"x": 133, "y": -323, "length": 349.31074990615446}, {"x": 162, "y": -310, "length": 349.77707186149297}, {"x": 194, "y": -291, "length": 349.73847372000694}, {"x": 219, "y": -272, "length": 349.20624278497655}, {"x": 247, "y": -247, "length": 349.31074990615446}, {"x": 268, "y": -224, "length": 349.28498393145964}, {"x": 291, "y": -194, "length": 349.73847372000694}, {"x": 307, "y": -167, "length": 349.48247452483224}, {"x": 323, "y": -133, "length": 349.31074990615446}, {"x": 334, "y": -104, "length": 349.817095065407}, {"x": 343, "y": -68, "length": 349.67556391603915}, {"x": 347, "y": -37, "length": 348.9670471548854}], "y": 303, "rem": true, "isStatic": false, "rotation": 0, "graphic": true, "plr": true, "ignore": false, "id": -1, "density": 1, "safeId": -1, "line": true, "isWheel": false, "layer": 3, "physic": true}, "className": "frg.game.editor.objects::LandscapeShaper"}, {"params": {"x": 80, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 0, "y": -1115, "length": 1115}, {"x": -680, "y": -1115, "length": 1305.9957886608977}], "wireframe": false, "textureOffset": 0, "width": 696.5, "rotation": 0, "snapToGrid": true, "height": 1131.5, "action": 0, "y": 460, "stretchTexture": false, "type": 0, "isStatic": true, "textureMode": true, "id": -1, "density": "1", "safeId": -1, "shapeH": 20, "originOffsetRatio": 0, "repeatTexture": true, "graphic": true, "physic": true, "smoothing": true, "straightSides": true}, "className": "frg.game.editor.objects::DynamicPather"}, {"params": {"shape": true, "snapToGrid": true, "vertices": [{"x": -360, "y": 0, "length": 360}, {"x": 80, "y": 0, "length": 80}, {"x": 510, "y": 10, "length": 510.09802979427394}, {"x": 1295, "y": 10, "length": 1295.0386094630537}, {"x": 1680, "y": 10, "length": 1680.0297616411442}, {"x": 1800, "y": -5, "length": 1800.0069444310486}, {"x": 1945, "y": -35, "length": 1945.3148845366911}, {"x": 2045, "y": -65, "length": 2046.0327465610123}, {"x": 2100, "y": -85, "length": 2101.719534095832}, {"x": 2160, "y": -105, "length": 2162.550577443219}, {"x": 2200, "y": -115, "length": 2203.0036314087183}, {"x": 2230, "y": -115, "length": 2232.9632777992565}, {"x": 2265, "y": -110, "length": 2267.669508548369}, {"x": 2310, "y": -100, "length": 2312.1634890292685}, {"x": 2365, "y": -80, "length": 2366.3526787019723}, {"x": 2420, "y": -70, "length": 2421.012185016837}, {"x": 2465, "y": -70, "length": 2465.993714509427}, {"x": 2515, "y": -80, "length": 2516.272044116057}, {"x": 2555, "y": -105, "length": 2557.1566240650964}, {"x": 2590, "y": -140, "length": 2593.7810239108467}, {"x": 2630, "y": -190, "length": 2636.8541863364385}, {"x": 2660, "y": -205, "length": 2667.8877412664874}, {"x": 2695, "y": -205, "length": 2702.785600080036}, {"x": 2740, "y": -185, "length": 2746.238336343006}, {"x": 2795, "y": -175, "length": 2800.473174304657}, {"x": 2825, "y": -170, "length": 2830.11042187403}, {"x": 2860, "y": -170, "length": 2865.0479926172266}, {"x": 2900, "y": -195, "length": 2906.5486405701176}, {"x": 2925, "y": -225, "length": 2933.641082341192}, {"x": 2955, "y": -280, "length": 2968.2360081368192}, {"x": 2959, "y": -323, "length": 2976.576893009821}, {"x": 2961, "y": -370, "length": 2984.027647325004}, {"x": 2958, "y": -403, "length": 2985.3262803251505}, {"x": 2963, "y": -436, "length": 2994.906509392238}, {"x": 2985, "y": -455, "length": 3019.478431782549}, {"x": 4301, "y": -455, "length": 4325.000115606935}, {"x": 4370, "y": -456, "length": 4393.726891831126}, {"x": 4448, "y": -467, "length": 4472.448210991381}, {"x": 4508, "y": -485, "length": 4534.014666937018}, {"x": 4570, "y": -510, "length": 4598.369276167367}, {"x": 4629, "y": -542, "length": 4660.62281245758}, {"x": 4678, "y": -573, "length": 4712.962231972584}, {"x": 4733, "y": -616, "length": 4772.917870653129}, {"x": 4778, "y": -660, "length": 4823.36853246774}, {"x": 4891, "y": -660, "length": 4935.329877525919}, {"x": 4932, "y": -602, "length": 4968.604230566166}, {"x": 4994, "y": -536, "length": 5022.681753804435}, {"x": 5054, "y": -480, "length": 5076.742656467826}, {"x": 5118, "y": -431, "length": 5136.115750253298}, {"x": 5191, "y": -387, "length": 5205.405843928022}, {"x": 5252, "y": -355, "length": 5263.984137514094}, {"x": 5322, "y": -329, "length": 5332.15950624135}, {"x": 5401, "y": -307, "length": 5409.718107258455}, {"x": 5490, "y": -292, "length": 5497.759907453216}, {"x": 5582, "y": -285, "length": 5589.2708826822845}, {"x": 5663, "y": -286, "length": 5670.217367967475}, {"x": 5751, "y": -297, "length": 5758.663907539665}, {"x": 5827, "y": -312, "length": 5835.346862012574}, {"x": 5905, "y": -337, "length": 5914.608524661628}, {"x": 5968, "y": -364, "length": 5979.090231799483}, {"x": 6029, "y": -395, "length": 6041.925686401646}, {"x": 6091, "y": -438, "length": 6106.727847219}, {"x": 6156, "y": -490, "length": 6175.470508390434}, {"x": 6213, "y": -543, "length": 6236.683253140246}, {"x": 6264, "y": -601, "length": 6292.765449307642}, {"x": 6317, "y": -672, "length": 6352.642993274531}, {"x": 6358, "y": -741, "length": 6401.034681986967}, {"x": 6391, "y": -806, "length": 6441.623785971981}, {"x": 6420, "y": -884, "length": 6480.57528310566}, {"x": 6436, "y": -949, "length": 6505.589673503855}, {"x": 6447, "y": -1021, "length": 6527.346321438751}, {"x": 6455, "y": -1095, "length": 6547.216966009299}, {"x": 9875, "y": -1095, "length": 9935.52464643916}], "wireframe": false, "x": -430, "y": 450, "directed": true, "isRoad": true, "camera": false, "lineId": 0, "thick": 128, "physic": true, "cameraOffsetY": 170, "width": 10248, "rotation": 0, "textureMode": true, "height": 1173, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "smoothing": true, "direction": 90, "repeatTexture": true, "textureOffset": 0, "line": true}, "className": "frg.game.editor.objects::GroundPather"}, {"params": {"rotation": 0, "x": 6761, "vertices": [{"x": 0, "y": -990, "length": 990}, {"x": 2160, "y": -995, "length": 2378.155798092295}, {"x": 2160, "y": 565, "length": 2232.672165813871}, {"x": 0, "y": 570, "length": 570}], "y": -1203}, "className": "frg.game.editor.objects::<PERSON>ish<PERSON><PERSON><PERSON>"}, {"params": {"shape": false, "snapToGrid": true, "vertices": [{"x": -360, "y": 0, "length": 360}, {"x": 80, "y": 0, "length": 80}, {"x": 510, "y": 10, "length": 510.09802979427394}, {"x": 1295, "y": 10, "length": 1295.0386094630537}, {"x": 1680, "y": 10, "length": 1680.0297616411442}, {"x": 1800, "y": -5, "length": 1800.0069444310486}, {"x": 1945, "y": -35, "length": 1945.3148845366911}, {"x": 2045, "y": -65, "length": 2046.0327465610123}, {"x": 2100, "y": -85, "length": 2101.719534095832}, {"x": 2160, "y": -105, "length": 2162.550577443219}, {"x": 2200, "y": -115, "length": 2203.0036314087183}, {"x": 2985, "y": -455, "length": 3019.478431782549}, {"x": 4301, "y": -455, "length": 4325.000115606935}, {"x": 4370, "y": -456, "length": 4393.726891831126}, {"x": 4448, "y": -467, "length": 4472.448210991381}, {"x": 4508, "y": -485, "length": 4534.014666937018}, {"x": 4570, "y": -510, "length": 4598.369276167367}, {"x": 4629, "y": -542, "length": 4660.62281245758}, {"x": 4678, "y": -573, "length": 4712.962231972584}, {"x": 4733, "y": -616, "length": 4772.917870653129}, {"x": 4778, "y": -660, "length": 4823.36853246774}, {"x": 4891, "y": -660, "length": 4935.329877525919}, {"x": 4932, "y": -602, "length": 4968.604230566166}, {"x": 4994, "y": -536, "length": 5022.681753804435}, {"x": 5054, "y": -480, "length": 5076.742656467826}, {"x": 5118, "y": -431, "length": 5136.115750253298}, {"x": 5191, "y": -387, "length": 5205.405843928022}, {"x": 5252, "y": -355, "length": 5263.984137514094}, {"x": 5322, "y": -329, "length": 5332.15950624135}, {"x": 5401, "y": -307, "length": 5409.718107258455}, {"x": 5490, "y": -292, "length": 5497.759907453216}, {"x": 5582, "y": -285, "length": 5589.2708826822845}, {"x": 5663, "y": -286, "length": 5670.217367967475}, {"x": 5751, "y": -297, "length": 5758.663907539665}, {"x": 6080, "y": -380, "length": 6091.863425914931}, {"x": 6845, "y": -1075, "length": 6928.899624038438}, {"x": 7915, "y": -1080, "length": 7988.343069748569}, {"x": 10365, "y": -290, "length": 10369.05612869368}], "wireframe": false, "x": -430, "y": 450, "directed": false, "isRoad": false, "camera": true, "lineId": 0, "thick": 128, "physic": false, "cameraOffsetY": 120, "width": 10738, "rotation": 0, "textureMode": true, "height": 1103, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "smoothing": true, "direction": 90, "repeatTexture": true, "textureOffset": 0, "line": false}, "className": "frg.game.editor.objects::GroundPather"}], [{"params": {"x": 150, "y": 230, "scaleX": 1, "width": 9.3, "rotation": 90, "scaleY": 1, "height": 128.35}, "className": "SignPillar"}, {"params": {"rotation": 0, "x": 190, "height": 35.15, "y": 230, "width": 110.7}, "className": "Sign15"}, {"params": {"x": 550, "y": 360, "scaleX": 1, "width": 171.75, "rotation": 0, "scaleY": 1, "height": 213.45}, "className": "Dec13"}, {"params": {"x": 620, "y": 350, "scaleX": 1, "width": 197, "rotation": 0, "scaleY": 1, "height": 234.2}, "className": "Dec15"}, {"params": {"x": 2830, "y": -70, "scaleX": 1, "width": 9.3, "rotation": 0, "scaleY": 1, "height": 128.35}, "className": "SignPillar"}, {"params": {"x": 4420, "y": -330, "scaleX": 1, "width": 176.2, "rotation": 0, "scaleY": 1, "height": 242.2}, "className": "Dec16"}, {"params": {"x": 2504, "y": -110, "scaleX": 1, "width": 171.75, "rotation": 0, "scaleY": 1, "height": 213.45}, "className": "Dec13"}, {"params": {"x": 2614, "y": -120, "scaleX": 1, "width": 165.85, "rotation": 0, "scaleY": 1, "height": 244.8}, "className": "Dec14"}, {"params": {"x": 3910, "y": -120, "scaleX": 1, "width": 197, "rotation": 0, "scaleY": 1, "height": 234.2}, "className": "Dec15"}, {"params": {"x": 3830, "y": -110, "scaleX": 1, "width": 171.75, "rotation": 0, "scaleY": 1, "height": 213.45}, "className": "Dec13"}, {"params": {"x": 6160, "y": -760, "scaleX": 1, "width": 165.85, "rotation": 0, "scaleY": 1, "height": 244.8}, "className": "Dec14"}, {"params": {"x": 6120, "y": -760, "scaleX": 1, "width": 197, "rotation": 0, "scaleY": 1, "height": 234.2}, "className": "Dec15"}, {"params": {"x": 6040, "y": -750, "scaleX": 1, "width": 171.75, "rotation": 0, "scaleY": 1, "height": 213.45}, "className": "Dec13"}], null, null, [{"params": {"rotation": 0, "x": 170, "height": 32, "y": 370, "width": 64}, "className": "PlayerWP"}, {"params": {"x": 2830, "id": 0, "y": -150, "width": 128, "rotation": 0, "height": 967}, "className": "SafePointE"}], [{"params": {"fnl": false, "id": 0, "x": 800, "safeId": -1, "sndId": -1, "id_off": -1, "rotation": 0, "off": false, "height": 1043, "y": 270, "width": 238}, "className": "ToggleE"}, {"params": {"x": 2228, "y": 303, "width": 17, "rotation": 0, "graphic": 0, "height": 18, "time": 0, "snd": false, "id": 0, "safeId": -1, "useWeld": false, "rate": 300}, "className": "MotorJointE"}, {"params": {"snapToGrid": true, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": -432, "y": 320, "length": 537.6095237251661}], "y": -720, "body": false, "rotation": 0, "position": 0, "height": 333, "accelDist": 0, "width": 445, "stopOnEnd": true, "x": 5640, "startTime": "0", "sideWait": 0, "speed": 250, "active": 0, "id": 2, "safeId": -1, "cycle": 0}, "className": "frg.game.editor.objects::<PERSON>r<PERSON><PERSON>"}, {"params": {"fnl": false, "id": 2, "x": 4360, "safeId": -1, "sndId": -1, "id_off": -1, "rotation": 0, "off": false, "height": 1289, "y": -420, "width": 64}, "className": "ToggleE"}, {"params": {"x": 5600, "y": -340, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "time": 2.2, "snd": false, "id": 2, "safeId": -1, "useWeld": false, "rate": -71}, "className": "MotorJointE"}]], "settings": {"prizes": "13,20,28", "theme": 0, "countdown": 0, "gravityY": 500}}