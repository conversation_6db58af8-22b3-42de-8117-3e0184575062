{"frameRate": 30, "name": "bikes_skeleton", "version": "5.5", "compatibleVersion": "5.5", "armature": [{"name": "biker0_anim_btn", "bone": [{"inheritScale": false, "name": "wheel back", "transform": {"x": -42.9, "y": -17.95}}, {"inheritScale": false, "name": "wheel front", "transform": {"x": 40.2, "y": -17.95}}, {"inheritScale": false, "name": "suspend_back", "transform": {"x": -15.8, "y": -28.05, "skX": 161.71, "skY": 161.71}}, {"inheritScale": false, "name": "suspend_front", "transform": {"x": 40.15, "y": -17.5, "skX": 63, "skY": 63}}, {"inheritScale": false, "name": "base", "transform": {"x": -9.8, "y": -40.05}}, {"inheritScale": false, "name": "driver_corpse", "transform": {"x": -18.2, "y": -73.8, "skX": 15.73, "skY": 15.73}}, {"inheritScale": false, "name": "driver_hip", "transform": {"x": -17.15, "y": -58.55, "skX": 30, "skY": 30}}, {"inheritScale": false, "name": "driver_shin", "transform": {"x": -11.6, "y": -42.8, "skX": 3, "skY": 3}}, {"inheritScale": false, "name": "driver_arm", "transform": {"x": -14.15, "y": -79.65, "skX": 45, "skY": 45}}, {"inheritScale": false, "name": "driver_forearm", "transform": {"x": -1.1, "y": -73.5, "skX": 11, "skY": 11}}, {"inheritScale": false, "name": "driver_head", "transform": {"x": -11.6, "y": -99.55, "skX": 15, "skY": 15}}, {"inheritScale": false, "name": "fx", "transform": {"x": -104.2, "y": -4.9, "scX": 1.4022, "scY": 1.4022}}], "slot": [{"name": "wheel back", "parent": "wheel back"}, {"name": "wheel front", "parent": "wheel front"}, {"name": "suspend_back", "parent": "suspend_back"}, {"name": "suspend_front", "parent": "suspend_front"}, {"name": "base", "parent": "base"}, {"name": "driver_corpse", "parent": "driver_corpse"}, {"name": "driver_hip", "parent": "driver_hip"}, {"name": "driver_shin", "parent": "driver_shin"}, {"name": "driver_arm", "parent": "driver_arm"}, {"name": "driver_forearm", "parent": "driver_forearm"}, {"name": "driver_head", "parent": "driver_head"}, {"name": "fx", "parent": "fx"}], "skin": [{"slot": [{"name": "wheel back", "display": [{"name": "Duplicate Items Folder/wheel", "transform": {"x": 0.05, "y": 0.05}}]}, {"name": "wheel front", "display": [{"name": "Duplicate Items Folder/wheel", "transform": {"x": 0.05, "y": 0.05}}]}, {"name": "suspend_back", "display": [{"name": "Duplicate Items Folder/suspend_back", "transform": {"x": 17.3, "y": 0.05}}]}, {"name": "suspend_front", "display": [{"name": "Duplicate Items Folder/suspend_front", "transform": {"x": -8.7, "y": 4.05}}]}, {"name": "base", "display": [{"name": "Duplicate Items Folder/base", "transform": {"x": 1.8, "y": -8.7}}]}, {"name": "driver_corpse", "display": [{"name": "Duplicate Items Folder/driver_corpse", "transform": {"x": -4.95, "y": 0.55}}]}, {"name": "driver_hip", "display": [{"name": "Duplicate Items Folder/driver_hip", "transform": {"y": -0.8}}]}, {"name": "driver_shin", "display": [{"name": "Duplicate Items Folder/driver_shin", "transform": {"x": 3.95, "y": 1.5}}]}, {"name": "driver_arm", "display": [{"name": "Duplicate Items Folder/driver_arm", "transform": {"x": 2, "y": 0.2}}]}, {"name": "driver_forearm", "display": [{"name": "Duplicate Items Folder/driver_forearm", "transform": {"x": 0.45, "y": 1.65}}]}, {"name": "driver_head", "display": [{"name": "Duplicate Items Folder/driver_head", "transform": {"x": -4.8, "y": -0.35}}]}, {"name": "fx", "display": [{"name": "Duplicate Items Folder/dust1", "transform": {"x": -2.45}}, {"name": "Duplicate Items Folder/dust2", "transform": {"x": -5.95, "y": -2}}, {"name": "Duplicate Items Folder/dust3", "transform": {"x": -7.55, "y": -4}}, {"name": "Duplicate Items Folder/dust4", "transform": {"x": -10.1, "y": -6.05}}, {"name": "Duplicate Items Folder/dust5", "transform": {"x": -11.1, "y": -6.5}}, {"name": "Duplicate Items Folder/dust6", "transform": {"x": -12.2, "y": -6.3}}, {"name": "Duplicate Items Folder/dust7", "transform": {"x": -12.9, "y": -6.5}}, {"name": "Duplicate Items Folder/dust8", "transform": {"x": -13.7, "y": -6.05}}, {"name": "Duplicate Items Folder/dust9", "transform": {"x": -13.8, "y": -7.3}}, {"name": "Duplicate Items Folder/dust10", "transform": {"x": -26.45, "y": -3.55}}]}]}], "animation": [{"duration": 89, "playTimes": 0, "fadeInTime": 0.3, "name": "animation", "bone": [{"name": "wheel back", "translateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "x": 33.1}, {"duration": 18, "tweenEasing": 1, "x": -27}, {"duration": 11, "tweenEasing": -1, "x": 66.05}, {"duration": 4, "tweenEasing": 1, "x": 49.1}, {"duration": 3, "tweenEasing": 0, "x": 49.1, "y": 1}, {"duration": 19, "tweenEasing": 1, "x": 49.1}, {"tweenEasing": 0}], "rotateFrame": [{"duration": 20, "tweenEasing": 0, "clockwise": 2}, {"duration": 13, "tweenEasing": 0, "clockwise": -2}, {"duration": 18, "tweenEasing": 1, "clockwise": -4}, {"duration": 11, "tweenEasing": -1, "rotate": -51.47}, {"duration": 7, "tweenEasing": 1}, {"duration": 19, "tweenEasing": 1, "clockwise": 2}, {"tweenEasing": 0, "clockwise": 2}], "scaleFrame": [{"duration": 62, "tweenEasing": 0}, {"duration": 4, "tweenEasing": 1}, {"duration": 3, "tweenEasing": 0, "y": 0.9435}, {"duration": 20}]}, {"name": "wheel front", "translateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "x": 33.1}, {"duration": 18, "tweenEasing": 1, "x": -27}, {"duration": 11, "tweenEasing": -1, "x": 34.7, "y": -65}, {"duration": 4, "tweenEasing": 1, "x": 49.1}, {"duration": 3, "tweenEasing": 0, "x": 49.1, "y": 1}, {"duration": 19, "tweenEasing": 1, "x": 49.1}, {"tweenEasing": 0}], "rotateFrame": [{"duration": 20, "tweenEasing": 0, "clockwise": 2}, {"duration": 13, "tweenEasing": 0, "clockwise": -2}, {"duration": 18, "tweenEasing": 1, "clockwise": -2}, {"duration": 11, "tweenEasing": -1, "rotate": -51.47}, {"duration": 7, "tweenEasing": 1}, {"duration": 19, "tweenEasing": 1, "clockwise": 2}, {"tweenEasing": 0}], "scaleFrame": [{"duration": 62, "tweenEasing": 0}, {"duration": 4, "tweenEasing": 1}, {"duration": 3, "tweenEasing": 0, "y": 0.9435}, {"duration": 20}]}, {"name": "suspend_back", "translateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "x": 33.1}, {"duration": 18, "tweenEasing": 1, "x": -27}, {"duration": 11, "tweenEasing": -1, "x": 47.9, "y": -17.4}, {"duration": 4, "tweenEasing": 1, "x": 49.1}, {"duration": 3, "tweenEasing": 0, "x": 49.1, "y": 1.5}, {"duration": 19, "tweenEasing": 1, "x": 49.1}, {"tweenEasing": 0}], "rotateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "rotate": -1.98}, {"duration": 18, "tweenEasing": 1}, {"duration": 11, "tweenEasing": -1, "rotate": -51.47}, {"duration": 27}], "scaleFrame": [{"duration": 89}]}, {"name": "suspend_front", "translateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "x": 33.5, "y": -0.45}, {"duration": 18, "tweenEasing": 1, "x": -27}, {"duration": 11, "tweenEasing": -1, "x": 35.05, "y": -65.1}, {"duration": 4, "tweenEasing": 1, "x": 49.1}, {"duration": 3, "tweenEasing": 0, "x": 46.85, "y": 0.1}, {"duration": 19, "tweenEasing": 1, "x": 49.1}, {"tweenEasing": 0}], "rotateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "rotate": 1.48}, {"duration": 18, "tweenEasing": 1}, {"duration": 11, "tweenEasing": -1, "rotate": -51.47}, {"duration": 4, "tweenEasing": 1}, {"duration": 3, "tweenEasing": 0, "rotate": 1.98}, {"duration": 20}], "scaleFrame": [{"duration": 89}]}, {"name": "base", "translateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "x": 34.1, "y": -1.75}, {"duration": 18, "tweenEasing": 1, "x": -27}, {"duration": 11, "tweenEasing": -1, "x": 36.25, "y": -17.6}, {"duration": 4, "tweenEasing": 1, "x": 49.1}, {"duration": 3, "tweenEasing": 0, "x": 50.15, "y": 7.4}, {"duration": 19, "tweenEasing": 1, "x": 49.1}, {"tweenEasing": 0}], "rotateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "rotate": 1.48}, {"duration": 18, "tweenEasing": 1}, {"duration": 11, "tweenEasing": -1, "rotate": -51.47}, {"duration": 4, "tweenEasing": 1}, {"duration": 3, "tweenEasing": 0, "rotate": 1.98}, {"duration": 20}], "scaleFrame": [{"duration": 89}]}, {"name": "driver_corpse", "translateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "x": 36.63, "y": -3.65}, {"duration": 18, "tweenEasing": 1, "x": -27.36, "y": 3.75}, {"duration": 11, "tweenEasing": -1, "x": 12.99, "y": 1.82}, {"duration": 4, "tweenEasing": 1, "x": 49.09, "y": 0.05}, {"duration": 3, "tweenEasing": 0, "x": 50.3, "y": 6.7}, {"duration": 19, "tweenEasing": 1, "x": 49.09, "y": 0.05}, {"tweenEasing": 0}], "rotateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "rotate": 5.18}, {"duration": 18, "tweenEasing": 1, "rotate": -8.7}, {"duration": 11, "tweenEasing": -1, "rotate": -51.47}, {"duration": 4, "tweenEasing": 1}, {"duration": 3, "tweenEasing": 0, "rotate": 13.9}, {"duration": 20}], "scaleFrame": [{"duration": 89}]}, {"name": "driver_hip", "translateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "x": 35.21, "y": -3.73}, {"duration": 18, "tweenEasing": 1, "x": -26.99, "y": 0.17}, {"duration": 11, "tweenEasing": -1, "x": 24.52, "y": -4.79}, {"duration": 4, "tweenEasing": 1, "x": 49.17, "y": -0.02}, {"duration": 3, "tweenEasing": 0, "x": 50.83, "y": 7.16}, {"duration": 19, "tweenEasing": 1, "x": 49.17, "y": -0.02}, {"tweenEasing": 0}], "rotateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "rotate": 5.17}, {"duration": 18, "tweenEasing": 1, "rotate": -15}, {"duration": 11, "tweenEasing": -1, "rotate": -51.47}, {"duration": 4, "tweenEasing": 1}, {"duration": 3, "tweenEasing": 0, "rotate": 1.98}, {"duration": 20}], "scaleFrame": [{"duration": 89}]}, {"name": "driver_shin", "translateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "x": 34.15, "y": -1.8}, {"duration": 18, "tweenEasing": 1, "x": -27}, {"duration": 11, "tweenEasing": -1, "x": 34.75, "y": -15.17}, {"duration": 4, "tweenEasing": 1, "x": 49.1}, {"duration": 3, "tweenEasing": 0, "x": 50.3, "y": 7.35}, {"duration": 19, "tweenEasing": 1, "x": 49.1}, {"tweenEasing": 0}], "rotateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "rotate": 1.49}, {"duration": 18, "tweenEasing": 1}, {"duration": 11, "tweenEasing": -1, "rotate": -51.47}, {"duration": 4, "tweenEasing": 1}, {"duration": 3, "tweenEasing": 0, "rotate": 1.98}, {"duration": 20}], "scaleFrame": [{"duration": 89}]}, {"name": "driver_arm", "translateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "x": 37.19, "y": -3.23}, {"duration": 18, "tweenEasing": 1, "x": -28.23, "y": 3.14}, {"duration": 11, "tweenEasing": -1, "x": 7, "y": 0.74}, {"duration": 4, "tweenEasing": 1, "x": 49.17}, {"duration": 3, "tweenEasing": 0, "x": 51.58, "y": 8.02}, {"duration": 19, "tweenEasing": 1, "x": 49.17}, {"tweenEasing": 0}], "rotateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "rotate": 5.17}, {"duration": 18, "tweenEasing": 1, "rotate": -8.71}, {"duration": 11, "tweenEasing": -1, "rotate": -51.47}, {"duration": 4, "tweenEasing": 1}, {"duration": 3, "tweenEasing": 0, "rotate": 13.9}, {"duration": 20}], "scaleFrame": [{"duration": 89}]}, {"name": "driver_forearm", "translateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "x": 36.6, "y": -2.25}, {"duration": 18, "tweenEasing": 1, "x": -27.55, "y": 1.1}, {"duration": 11, "tweenEasing": -1, "x": 6.78, "y": -11.71}, {"duration": 4, "tweenEasing": 1, "x": 49.11, "y": -0.05}, {"duration": 3, "tweenEasing": 0, "x": 51.2, "y": 7.75}, {"duration": 19, "tweenEasing": 1, "x": 49.11, "y": -0.05}, {"tweenEasing": 0}], "rotateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "rotate": 5.17}, {"duration": 18, "tweenEasing": 1, "rotate": -8.7}, {"duration": 11, "tweenEasing": -1, "rotate": -51.47}, {"duration": 4, "tweenEasing": 1}, {"duration": 3, "tweenEasing": 0, "rotate": -13.02}, {"duration": 20}], "scaleFrame": [{"duration": 89}]}, {"name": "driver_head", "translateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "x": 38.95, "y": -3}, {"duration": 18, "tweenEasing": 1, "x": -31.3, "y": 3}, {"duration": 11, "tweenEasing": -1, "x": -9.6, "y": 6.3}, {"duration": 4, "tweenEasing": 1, "x": 49.1}, {"duration": 3, "tweenEasing": 0, "x": 56.3, "y": 9}, {"duration": 19, "tweenEasing": 1, "x": 49.1}, {"tweenEasing": 0}], "rotateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "rotate": 5.17}, {"duration": 18, "tweenEasing": 1, "rotate": -8.71}, {"duration": 11, "tweenEasing": -1, "rotate": -51.47}, {"duration": 4, "tweenEasing": 1}, {"duration": 3, "tweenEasing": 0, "rotate": 13.9}, {"duration": 20}], "scaleFrame": [{"duration": 89}]}, {"name": "fx", "translateFrame": [{"duration": 33, "tweenEasing": 0, "x": 104.2, "y": 4.9}, {"tweenEasing": 0}, {"tweenEasing": 0, "x": 13.4, "y": 0.3}, {"tweenEasing": 0, "x": 24.65, "y": 0.65}, {"tweenEasing": 0, "x": 33.9, "y": 0.9}, {"tweenEasing": 0, "x": 43.2, "y": 1.15}, {"tweenEasing": 0, "x": 51.75, "y": 1.35}, {"tweenEasing": 0, "x": 57.7, "y": 1.45}, {"tweenEasing": 0, "x": 67.2, "y": 1.5}, {"tweenEasing": 0, "x": 76.85, "y": 0.9}, {"tweenEasing": 0, "x": 81.9, "y": 1.15}, {"tweenEasing": 0, "x": 90.4, "y": 1.35}, {"tweenEasing": 0, "x": 92.1, "y": 1.45}, {"tweenEasing": 0, "x": 97.3, "y": 1.5}, {"tweenEasing": 0, "x": 102.65, "y": 0.9}, {"tweenEasing": 0, "x": 103.35, "y": 1.15}, {"tweenEasing": 0, "x": 107.6, "y": 1.35}, {"tweenEasing": 0, "x": 109.25, "y": 1.45}, {"tweenEasing": 0, "x": 110.2, "y": 1.5}, {"tweenEasing": 0, "x": 111.25, "y": 0.9}, {"tweenEasing": 0, "x": 111.95, "y": 1.15}, {"tweenEasing": 0, "x": 107.6, "y": 1.35}, {"tweenEasing": 0, "x": 109.25, "y": 1.45}, {"tweenEasing": 0, "x": 110.2, "y": 1.5}, {"tweenEasing": 0, "x": 100.45, "y": 1.6}, {"x": 110.05, "y": 1.6}, {"duration": 31, "tweenEasing": 0, "x": 104.2, "y": 4.9}], "scaleFrame": [{"duration": 33, "tweenEasing": 0, "x": 0.7132, "y": 0.7132}, {"tweenEasing": 0}, {"tweenEasing": 0, "x": 0.9325, "y": 0.9325}, {"tweenEasing": 0, "x": 0.873, "y": 0.873}, {"tweenEasing": 0, "x": 0.8214, "y": 0.8214}, {"tweenEasing": 0, "x": 0.7778, "y": 0.7778}, {"tweenEasing": 0, "x": 0.742, "y": 0.742}, {"tweenEasing": 0, "x": 0.7143, "y": 0.7143}, {"tweenEasing": 0, "x": 0.6944, "y": 0.6944}, {"tweenEasing": 0, "x": 0.8214, "y": 0.8214}, {"tweenEasing": 0, "x": 0.7778, "y": 0.7778}, {"tweenEasing": 0, "x": 0.742, "y": 0.742}, {"tweenEasing": 0, "x": 0.7143, "y": 0.7143}, {"tweenEasing": 0, "x": 0.6944, "y": 0.6944}, {"tweenEasing": 0, "x": 0.8214, "y": 0.8214}, {"tweenEasing": 0, "x": 0.7778, "y": 0.7778}, {"tweenEasing": 0, "x": 0.742, "y": 0.742}, {"tweenEasing": 0, "x": 0.7143, "y": 0.7143}, {"tweenEasing": 0, "x": 0.6944, "y": 0.6944}, {"tweenEasing": 0, "x": 0.8214, "y": 0.8214}, {"tweenEasing": 0, "x": 0.7778, "y": 0.7778}, {"tweenEasing": 0, "x": 0.742, "y": 0.742}, {"tweenEasing": 0, "x": 0.7143, "y": 0.7143}, {"tweenEasing": 0, "x": 0.6944, "y": 0.6944}, {"tweenEasing": 0, "x": 0.6825, "y": 0.6825}, {"x": 0.6786, "y": 0.6786}, {"duration": 31, "tweenEasing": 0, "x": 0.7132, "y": 0.7132}]}], "slot": [{"name": "fx", "displayFrame": [{"duration": 33, "value": -1}, {}, {"value": 1}, {"value": 2}, {"value": 3}, {"value": 4}, {"value": 5}, {"value": 6}, {"value": 7}, {"value": 3}, {"value": 4}, {"value": 5}, {"value": 6}, {"value": 7}, {"value": 3}, {"value": 4}, {"value": 5}, {"value": 6}, {"value": 7}, {"value": 3}, {"value": 4}, {"value": 5}, {"value": 6}, {"value": 7}, {"value": 8}, {"value": 9}, {"duration": 31, "value": -1}]}]}]}, {"name": "biker1_anim_btn", "bone": [{"inheritScale": false, "name": "wheel back", "transform": {"x": -42.9, "y": -17.95}}, {"inheritScale": false, "name": "wheel front", "transform": {"x": 40.2, "y": -17.95}}, {"inheritScale": false, "name": "suspend_front", "transform": {"x": 40.15, "y": -17.5, "skX": 63, "skY": 63}}, {"inheritScale": false, "name": "suspend_back", "transform": {"x": -15.8, "y": -28.05, "skX": 161.71, "skY": 161.71}}, {"inheritScale": false, "name": "base", "transform": {"x": -9.8, "y": -40.05}}, {"inheritScale": false, "name": "driver_hip", "transform": {"x": -17.15, "y": -58.55, "skX": 30, "skY": 30}}, {"inheritScale": false, "name": "driver_shin", "transform": {"x": -11.6, "y": -42.8, "skX": 3, "skY": 3}}, {"inheritScale": false, "name": "driver_corpse", "transform": {"x": -18.2, "y": -73.8, "skX": 15.73, "skY": 15.73}}, {"inheritScale": false, "name": "driver_head", "transform": {"x": -11.6, "y": -99.55, "skX": 15, "skY": 15}}, {"inheritScale": false, "name": "driver_arm", "transform": {"x": -14.15, "y": -79.65, "skX": 45, "skY": 45}}, {"inheritScale": false, "name": "driver_forearm", "transform": {"x": -1.1, "y": -73.5, "skX": 11, "skY": 11}}, {"inheritScale": false, "name": "fx", "transform": {"x": -104.2, "y": -4.9, "scX": 1.4022, "scY": 1.4022}}], "slot": [{"name": "wheel back", "parent": "wheel back"}, {"name": "wheel front", "parent": "wheel front"}, {"name": "suspend_front", "parent": "suspend_front"}, {"name": "suspend_back", "parent": "suspend_back"}, {"name": "base", "parent": "base"}, {"name": "driver_hip", "parent": "driver_hip"}, {"name": "driver_shin", "parent": "driver_shin"}, {"name": "driver_corpse", "parent": "driver_corpse"}, {"name": "driver_head", "parent": "driver_head"}, {"name": "driver_arm", "parent": "driver_arm"}, {"name": "driver_forearm", "parent": "driver_forearm"}, {"name": "fx", "parent": "fx"}], "skin": [{"slot": [{"name": "wheel back", "display": [{"name": "wheel", "transform": {"x": 0.25, "y": 0.25}}]}, {"name": "wheel front", "display": [{"name": "wheel", "transform": {"x": 0.25, "y": 0.25}}]}, {"name": "suspend_front", "display": [{"name": "suspend_front", "transform": {"x": -17.3, "y": 1}}]}, {"name": "suspend_back", "display": [{"name": "suspend_back", "transform": {"x": 19.15, "y": 0.25}}]}, {"name": "base", "display": [{"name": "base", "transform": {"x": -10, "y": -7.15}}]}, {"name": "driver_hip", "display": [{"name": "driver_hip", "transform": {"x": -2.15, "y": 0.2}}]}, {"name": "driver_shin", "display": [{"name": "driver_shin copy", "transform": {"x": 2.05, "y": 1.35}}]}, {"name": "driver_corpse", "display": [{"name": "driver_corpse copy", "transform": {"x": -0.7, "y": -3.75}}]}, {"name": "driver_head", "display": [{"name": "driver_head copy", "transform": {"x": -5.6, "y": 5.55}}]}, {"name": "driver_arm", "display": [{"name": "driver_arm copy", "transform": {"x": 1.15, "y": 0.2}}]}, {"name": "driver_forearm", "display": [{"name": "driver_forearm copy", "transform": {"x": 3.55, "y": -0.1}}]}, {"name": "fx", "display": [{"name": ".shit_folder/test/dust1", "transform": {"x": -2.45}}, {"name": ".shit_folder/test/dust2", "transform": {"x": -5.95, "y": -2}}, {"name": ".shit_folder/test/dust3", "transform": {"x": -7.55, "y": -4}}, {"name": ".shit_folder/test/dust4", "transform": {"x": -10.1, "y": -6.05}}, {"name": ".shit_folder/test/dust5", "transform": {"x": -11.1, "y": -6.5}}, {"name": ".shit_folder/test/dust6", "transform": {"x": -12.2, "y": -6.3}}, {"name": ".shit_folder/test/dust7", "transform": {"x": -12.9, "y": -6.5}}, {"name": ".shit_folder/test/dust8", "transform": {"x": -13.7, "y": -6.05}}, {"name": ".shit_folder/test/dust9", "transform": {"x": -13.8, "y": -7.3}}, {"name": ".shit_folder/test/dust10", "transform": {"x": -26.45, "y": -3.55}}]}]}], "animation": [{"duration": 89, "playTimes": 0, "fadeInTime": 0.3, "name": "animation", "bone": [{"name": "wheel back", "translateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "x": 33.1}, {"duration": 18, "tweenEasing": 1, "x": -27}, {"duration": 11, "tweenEasing": -1, "x": 66.05}, {"duration": 4, "tweenEasing": 1, "x": 49.1}, {"duration": 3, "tweenEasing": 0, "x": 49.1, "y": 1}, {"duration": 19, "tweenEasing": 1, "x": 49.1}, {"tweenEasing": 0}], "rotateFrame": [{"duration": 20, "tweenEasing": 0, "clockwise": 2}, {"duration": 13, "tweenEasing": 0, "clockwise": -2}, {"duration": 18, "tweenEasing": 1, "clockwise": -4}, {"duration": 11, "tweenEasing": -1, "rotate": -51.47}, {"duration": 7, "tweenEasing": 1}, {"duration": 19, "tweenEasing": 1, "clockwise": 2}, {"tweenEasing": 0, "clockwise": 2}], "scaleFrame": [{"duration": 62, "tweenEasing": 0}, {"duration": 4, "tweenEasing": 1}, {"duration": 3, "tweenEasing": 0, "y": 0.9435}, {"duration": 20}]}, {"name": "wheel front", "translateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "x": 33.1}, {"duration": 18, "tweenEasing": 1, "x": -27}, {"duration": 11, "tweenEasing": -1, "x": 34.7, "y": -65}, {"duration": 4, "tweenEasing": 1, "x": 49.1}, {"duration": 3, "tweenEasing": 0, "x": 49.1, "y": 1}, {"duration": 19, "tweenEasing": 1, "x": 49.1}, {"tweenEasing": 0}], "rotateFrame": [{"duration": 20, "tweenEasing": 0, "clockwise": 2}, {"duration": 13, "tweenEasing": 0, "clockwise": -2}, {"duration": 18, "tweenEasing": 1, "clockwise": -2}, {"duration": 11, "tweenEasing": -1, "rotate": -51.47}, {"duration": 7, "tweenEasing": 1}, {"duration": 19, "tweenEasing": 1, "clockwise": 2}, {"tweenEasing": 0}], "scaleFrame": [{"duration": 62, "tweenEasing": 0}, {"duration": 4, "tweenEasing": 1}, {"duration": 3, "tweenEasing": 0, "y": 0.9435}, {"duration": 20}]}, {"name": "suspend_front", "translateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "x": 33.5, "y": -0.45}, {"duration": 18, "tweenEasing": 1, "x": -27}, {"duration": 11, "tweenEasing": -1, "x": 35.05, "y": -65.1}, {"duration": 4, "tweenEasing": 1, "x": 49.1}, {"duration": 3, "tweenEasing": 0, "x": 46.85, "y": 0.1}, {"duration": 19, "tweenEasing": 1, "x": 49.1}, {"tweenEasing": 0}], "rotateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "rotate": 1.48}, {"duration": 18, "tweenEasing": 1}, {"duration": 11, "tweenEasing": -1, "rotate": -51.47}, {"duration": 4, "tweenEasing": 1}, {"duration": 3, "tweenEasing": 0, "rotate": 1.98}, {"duration": 20}], "scaleFrame": [{"duration": 89}]}, {"name": "suspend_back", "translateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "x": 33.1}, {"duration": 18, "tweenEasing": 1, "x": -27}, {"duration": 11, "tweenEasing": -1, "x": 47.9, "y": -17.4}, {"duration": 4, "tweenEasing": 1, "x": 49.1}, {"duration": 3, "tweenEasing": 0, "x": 49.1, "y": 1.5}, {"duration": 19, "tweenEasing": 1, "x": 49.1}, {"tweenEasing": 0}], "rotateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "rotate": -1.98}, {"duration": 18, "tweenEasing": 1}, {"duration": 11, "tweenEasing": -1, "rotate": -51.47}, {"duration": 27}], "scaleFrame": [{"duration": 89}]}, {"name": "base", "translateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "x": 34.1, "y": -1.75}, {"duration": 18, "tweenEasing": 1, "x": -27}, {"duration": 11, "tweenEasing": -1, "x": 36.25, "y": -17.6}, {"duration": 4, "tweenEasing": 1, "x": 49.1}, {"duration": 3, "tweenEasing": 0, "x": 50.15, "y": 7.4}, {"duration": 19, "tweenEasing": 1, "x": 49.1}, {"tweenEasing": 0}], "rotateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "rotate": 1.48}, {"duration": 18, "tweenEasing": 1}, {"duration": 11, "tweenEasing": -1, "rotate": -51.47}, {"duration": 4, "tweenEasing": 1}, {"duration": 3, "tweenEasing": 0, "rotate": 1.98}, {"duration": 20}], "scaleFrame": [{"duration": 89}]}, {"name": "driver_hip", "translateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "x": 35.21, "y": -3.73}, {"duration": 18, "tweenEasing": 1, "x": -26.99, "y": 0.17}, {"duration": 11, "tweenEasing": -1, "x": 24.52, "y": -4.79}, {"duration": 4, "tweenEasing": 1, "x": 49.17, "y": -0.02}, {"duration": 3, "tweenEasing": 0, "x": 50.83, "y": 7.16}, {"duration": 19, "tweenEasing": 1, "x": 49.17, "y": -0.02}, {"tweenEasing": 0}], "rotateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "rotate": 5.17}, {"duration": 18, "tweenEasing": 1, "rotate": -15}, {"duration": 11, "tweenEasing": -1, "rotate": -51.47}, {"duration": 4, "tweenEasing": 1}, {"duration": 3, "tweenEasing": 0, "rotate": 1.98}, {"duration": 20}], "scaleFrame": [{"duration": 89}]}, {"name": "driver_shin", "translateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "x": 34.15, "y": -1.8}, {"duration": 18, "tweenEasing": 1, "x": -27}, {"duration": 11, "tweenEasing": -1, "x": 34.75, "y": -15.17}, {"duration": 4, "tweenEasing": 1, "x": 49.1}, {"duration": 3, "tweenEasing": 0, "x": 50.3, "y": 7.35}, {"duration": 19, "tweenEasing": 1, "x": 49.1}, {"tweenEasing": 0}], "rotateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "rotate": 1.49}, {"duration": 18, "tweenEasing": 1}, {"duration": 11, "tweenEasing": -1, "rotate": -51.47}, {"duration": 4, "tweenEasing": 1}, {"duration": 3, "tweenEasing": 0, "rotate": 1.98}, {"duration": 20}], "scaleFrame": [{"duration": 89}]}, {"name": "driver_corpse", "translateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "x": 36.63, "y": -3.65}, {"duration": 18, "tweenEasing": 1, "x": -27.36, "y": 3.75}, {"duration": 11, "tweenEasing": -1, "x": 12.99, "y": 1.82}, {"duration": 4, "tweenEasing": 1, "x": 49.09, "y": 0.05}, {"duration": 3, "tweenEasing": 0, "x": 50.3, "y": 6.7}, {"duration": 19, "tweenEasing": 1, "x": 49.09, "y": 0.05}, {"tweenEasing": 0}], "rotateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "rotate": 5.18}, {"duration": 18, "tweenEasing": 1, "rotate": -8.7}, {"duration": 11, "tweenEasing": -1, "rotate": -51.47}, {"duration": 4, "tweenEasing": 1}, {"duration": 3, "tweenEasing": 0, "rotate": 13.9}, {"duration": 20}], "scaleFrame": [{"duration": 89}]}, {"name": "driver_head", "translateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "x": 38.95, "y": -3}, {"duration": 18, "tweenEasing": 1, "x": -31.3, "y": 3}, {"duration": 11, "tweenEasing": -1, "x": -9.6, "y": 6.3}, {"duration": 4, "tweenEasing": 1, "x": 49.1}, {"duration": 3, "tweenEasing": 0, "x": 56.3, "y": 9}, {"duration": 19, "tweenEasing": 1, "x": 49.1}, {"tweenEasing": 0}], "rotateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "rotate": 5.17}, {"duration": 18, "tweenEasing": 1, "rotate": -8.71}, {"duration": 11, "tweenEasing": -1, "rotate": -51.47}, {"duration": 4, "tweenEasing": 1}, {"duration": 3, "tweenEasing": 0, "rotate": 13.9}, {"duration": 20}], "scaleFrame": [{"duration": 89}]}, {"name": "driver_arm", "translateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "x": 37.19, "y": -3.23}, {"duration": 18, "tweenEasing": 1, "x": -28.23, "y": 3.14}, {"duration": 11, "tweenEasing": -1, "x": 7, "y": 0.74}, {"duration": 4, "tweenEasing": 1, "x": 49.17}, {"duration": 3, "tweenEasing": 0, "x": 51.58, "y": 8.02}, {"duration": 19, "tweenEasing": 1, "x": 49.17}, {"tweenEasing": 0}], "rotateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "rotate": 5.17}, {"duration": 18, "tweenEasing": 1, "rotate": -8.71}, {"duration": 11, "tweenEasing": -1, "rotate": -51.47}, {"duration": 4, "tweenEasing": 1}, {"duration": 3, "tweenEasing": 0, "rotate": 13.9}, {"duration": 20}], "scaleFrame": [{"duration": 89}]}, {"name": "driver_forearm", "translateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "x": 36.6, "y": -2.25}, {"duration": 18, "tweenEasing": 1, "x": -27.55, "y": 1.1}, {"duration": 11, "tweenEasing": -1, "x": 6.78, "y": -11.71}, {"duration": 4, "tweenEasing": 1, "x": 49.11, "y": -0.05}, {"duration": 3, "tweenEasing": 0, "x": 51.2, "y": 7.75}, {"duration": 19, "tweenEasing": 1, "x": 49.11, "y": -0.05}, {"tweenEasing": 0}], "rotateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "rotate": 5.17}, {"duration": 18, "tweenEasing": 1, "rotate": -8.7}, {"duration": 11, "tweenEasing": -1, "rotate": -51.47}, {"duration": 4, "tweenEasing": 1}, {"duration": 3, "tweenEasing": 0, "rotate": -13.02}, {"duration": 20}], "scaleFrame": [{"duration": 89}]}, {"name": "fx", "translateFrame": [{"duration": 33, "tweenEasing": 0, "x": 104.2, "y": 4.9}, {"tweenEasing": 0}, {"tweenEasing": 0, "x": 13.4, "y": 0.3}, {"tweenEasing": 0, "x": 24.65, "y": 0.65}, {"tweenEasing": 0, "x": 33.9, "y": 0.9}, {"tweenEasing": 0, "x": 43.2, "y": 1.15}, {"tweenEasing": 0, "x": 51.75, "y": 1.35}, {"tweenEasing": 0, "x": 57.7, "y": 1.45}, {"tweenEasing": 0, "x": 67.2, "y": 1.5}, {"tweenEasing": 0, "x": 76.85, "y": 0.9}, {"tweenEasing": 0, "x": 81.9, "y": 1.15}, {"tweenEasing": 0, "x": 90.4, "y": 1.35}, {"tweenEasing": 0, "x": 92.1, "y": 1.45}, {"tweenEasing": 0, "x": 97.3, "y": 1.5}, {"tweenEasing": 0, "x": 102.65, "y": 0.9}, {"tweenEasing": 0, "x": 103.35, "y": 1.15}, {"tweenEasing": 0, "x": 107.6, "y": 1.35}, {"tweenEasing": 0, "x": 109.25, "y": 1.45}, {"tweenEasing": 0, "x": 110.2, "y": 1.5}, {"tweenEasing": 0, "x": 111.25, "y": 0.9}, {"tweenEasing": 0, "x": 111.95, "y": 1.15}, {"tweenEasing": 0, "x": 107.6, "y": 1.35}, {"tweenEasing": 0, "x": 109.25, "y": 1.45}, {"tweenEasing": 0, "x": 110.2, "y": 1.5}, {"tweenEasing": 0, "x": 100.45, "y": 1.6}, {"x": 110.05, "y": 1.6}, {"duration": 31, "tweenEasing": 0, "x": 104.2, "y": 4.9}], "scaleFrame": [{"duration": 33, "tweenEasing": 0, "x": 0.7132, "y": 0.7132}, {"tweenEasing": 0}, {"tweenEasing": 0, "x": 0.9325, "y": 0.9325}, {"tweenEasing": 0, "x": 0.873, "y": 0.873}, {"tweenEasing": 0, "x": 0.8214, "y": 0.8214}, {"tweenEasing": 0, "x": 0.7778, "y": 0.7778}, {"tweenEasing": 0, "x": 0.742, "y": 0.742}, {"tweenEasing": 0, "x": 0.7143, "y": 0.7143}, {"tweenEasing": 0, "x": 0.6944, "y": 0.6944}, {"tweenEasing": 0, "x": 0.8214, "y": 0.8214}, {"tweenEasing": 0, "x": 0.7778, "y": 0.7778}, {"tweenEasing": 0, "x": 0.742, "y": 0.742}, {"tweenEasing": 0, "x": 0.7143, "y": 0.7143}, {"tweenEasing": 0, "x": 0.6944, "y": 0.6944}, {"tweenEasing": 0, "x": 0.8214, "y": 0.8214}, {"tweenEasing": 0, "x": 0.7778, "y": 0.7778}, {"tweenEasing": 0, "x": 0.742, "y": 0.742}, {"tweenEasing": 0, "x": 0.7143, "y": 0.7143}, {"tweenEasing": 0, "x": 0.6944, "y": 0.6944}, {"tweenEasing": 0, "x": 0.8214, "y": 0.8214}, {"tweenEasing": 0, "x": 0.7778, "y": 0.7778}, {"tweenEasing": 0, "x": 0.742, "y": 0.742}, {"tweenEasing": 0, "x": 0.7143, "y": 0.7143}, {"tweenEasing": 0, "x": 0.6944, "y": 0.6944}, {"tweenEasing": 0, "x": 0.6825, "y": 0.6825}, {"x": 0.6786, "y": 0.6786}, {"duration": 31, "tweenEasing": 0, "x": 0.7132, "y": 0.7132}]}], "slot": [{"name": "fx", "displayFrame": [{"duration": 33, "value": -1}, {}, {"value": 1}, {"value": 2}, {"value": 3}, {"value": 4}, {"value": 5}, {"value": 6}, {"value": 7}, {"value": 3}, {"value": 4}, {"value": 5}, {"value": 6}, {"value": 7}, {"value": 3}, {"value": 4}, {"value": 5}, {"value": 6}, {"value": 7}, {"value": 3}, {"value": 4}, {"value": 5}, {"value": 6}, {"value": 7}, {"value": 8}, {"value": 9}, {"duration": 31, "value": -1}]}]}]}, {"name": "biker2_anim_btn", "bone": [{"inheritScale": false, "name": "wheel back", "transform": {"x": -42.9, "y": -17.95}}, {"inheritScale": false, "name": "wheel front", "transform": {"x": 40.2, "y": -17.95}}, {"inheritScale": false, "name": "suspend_back", "transform": {"x": -15.8, "y": -28.05, "skX": 161.71, "skY": 161.71}}, {"inheritScale": false, "name": "suspend_front", "transform": {"x": 40.15, "y": -17.5, "skX": 63, "skY": 63}}, {"inheritScale": false, "name": "base", "transform": {"x": -9.8, "y": -40.05}}, {"inheritScale": false, "name": "driver_corpse", "transform": {"x": -18.2, "y": -73.8, "skX": 15.73, "skY": 15.73}}, {"inheritScale": false, "name": "driver_hip", "transform": {"x": -17.15, "y": -58.55, "skX": 30, "skY": 30}}, {"inheritScale": false, "name": "driver_shin", "transform": {"x": -11.6, "y": -42.8, "skX": 3, "skY": 3}}, {"inheritScale": false, "name": "driver_head", "transform": {"x": -10.4, "y": -101.05, "skX": 15, "skY": 15}}, {"inheritScale": false, "name": "driver_arm", "transform": {"x": -14.15, "y": -79.65, "skX": 45, "skY": 45}}, {"inheritScale": false, "name": "driver_forearm", "transform": {"x": -1.1, "y": -73.5, "skX": 11, "skY": 11}}, {"inheritScale": false, "name": "fx", "transform": {"x": -104.2, "y": -4.9, "scX": 1.4022, "scY": 1.4022}}], "slot": [{"name": "wheel back", "parent": "wheel back"}, {"name": "wheel front", "parent": "wheel front"}, {"name": "suspend_back", "parent": "suspend_back"}, {"name": "suspend_front", "parent": "suspend_front"}, {"name": "base", "parent": "base"}, {"name": "driver_corpse", "parent": "driver_corpse"}, {"name": "driver_hip", "parent": "driver_hip"}, {"name": "driver_shin", "parent": "driver_shin"}, {"name": "driver_head", "parent": "driver_head"}, {"name": "driver_arm", "parent": "driver_arm"}, {"name": "driver_forearm", "parent": "driver_forearm"}, {"name": "fx", "parent": "fx"}], "skin": [{"slot": [{"name": "wheel back", "display": [{"type": "armature", "name": "wheel_sportbike_anim"}]}, {"name": "wheel front", "display": [{"name": "Duplicate Items Folder/wheel copy"}]}, {"name": "suspend_back", "display": [{"name": "Duplicate Items Folder/suspend_back copy", "transform": {"x": 16.3, "y": 0.5}}]}, {"name": "suspend_front", "display": [{"name": "Duplicate Items Folder/suspend_front copy", "transform": {"x": -16.25, "y": 1.55}}]}, {"name": "base", "display": [{"name": "Duplicate Items Folder/base copy", "transform": {"x": -7.2, "y": -10.05}}]}, {"name": "driver_corpse", "display": [{"name": "Duplicate Items Folder/driver_corpse copy", "transform": {"x": 1, "y": 0.6}}]}, {"name": "driver_hip", "display": [{"name": "Duplicate Items Folder/driver_hip copy", "transform": {"x": -0.7, "y": -0.6}}]}, {"name": "driver_shin", "display": [{"name": "Duplicate Items Folder/driver_shin copy", "transform": {"x": 2.25, "y": 0.35}}]}, {"name": "driver_head", "display": [{"name": "Duplicate Items Folder/driver_head copy", "transform": {"x": 2.85, "y": 1.3}}]}, {"name": "driver_arm", "display": [{"name": "Duplicate Items Folder/driver_arm copy", "transform": {"x": 0.85, "y": 0.35}}]}, {"name": "driver_forearm", "display": [{"name": "Duplicate Items Folder/driver_forearm copy", "transform": {"x": 0.25, "y": 0.8}}]}, {"name": "fx", "display": [{"name": "Duplicate Items Folder/dust1 copy", "transform": {"x": -2.45}}, {"name": "Duplicate Items Folder/dust2 copy", "transform": {"x": -5.95, "y": -2}}, {"name": "Duplicate Items Folder/dust3 copy", "transform": {"x": -7.55, "y": -4}}, {"name": "Duplicate Items Folder/dust4 copy", "transform": {"x": -10.1, "y": -6.05}}, {"name": "Duplicate Items Folder/dust5 copy", "transform": {"x": -11.1, "y": -6.5}}, {"name": "Duplicate Items Folder/dust6 copy", "transform": {"x": -12.2, "y": -6.3}}, {"name": "Duplicate Items Folder/dust7 copy", "transform": {"x": -12.9, "y": -6.5}}, {"name": "Duplicate Items Folder/dust8 copy", "transform": {"x": -13.7, "y": -6.05}}, {"name": "Duplicate Items Folder/dust9 copy", "transform": {"x": -13.8, "y": -7.3}}, {"name": "Duplicate Items Folder/dust10 copy", "transform": {"x": -26.45, "y": -3.55}}]}]}], "animation": [{"duration": 89, "playTimes": 0, "fadeInTime": 0.3, "name": "animation", "bone": [{"name": "wheel back", "translateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "x": 33.1}, {"duration": 18, "tweenEasing": 1, "x": -27}, {"duration": 11, "tweenEasing": -1, "x": 66.05}, {"duration": 4, "tweenEasing": 1, "x": 49.1}, {"duration": 3, "tweenEasing": 0, "x": 49.1, "y": 1}, {"duration": 19, "tweenEasing": 1, "x": 49.1}, {"tweenEasing": 0}], "rotateFrame": [{"duration": 20, "tweenEasing": 0, "clockwise": 2}, {"duration": 13, "tweenEasing": 0, "clockwise": -2}, {"duration": 18, "tweenEasing": 1, "clockwise": -4}, {"duration": 11, "tweenEasing": -1, "rotate": -51.47}, {"duration": 7, "tweenEasing": 1}, {"duration": 19, "tweenEasing": 1, "clockwise": 2}, {"tweenEasing": 0, "clockwise": 2}], "scaleFrame": [{"duration": 62, "tweenEasing": 0}, {"duration": 4, "tweenEasing": 1}, {"duration": 3, "tweenEasing": 0, "y": 0.9435}, {"duration": 20}]}, {"name": "wheel front", "translateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "x": 33.1}, {"duration": 18, "tweenEasing": 1, "x": -27}, {"duration": 11, "tweenEasing": -1, "x": 34.7, "y": -65}, {"duration": 4, "tweenEasing": 1, "x": 49.1}, {"duration": 3, "tweenEasing": 0, "x": 49.1, "y": 1}, {"duration": 19, "tweenEasing": 1, "x": 49.1}, {"tweenEasing": 0}], "rotateFrame": [{"duration": 20, "tweenEasing": 0, "clockwise": 2}, {"duration": 13, "tweenEasing": 0, "clockwise": -2}, {"duration": 18, "tweenEasing": 1, "clockwise": -2}, {"duration": 11, "tweenEasing": -1, "rotate": -51.47}, {"duration": 7, "tweenEasing": 1}, {"duration": 19, "tweenEasing": 1, "clockwise": 2}, {"tweenEasing": 0}], "scaleFrame": [{"duration": 62, "tweenEasing": 0}, {"duration": 4, "tweenEasing": 1}, {"duration": 3, "tweenEasing": 0, "y": 0.9435}, {"duration": 20}]}, {"name": "suspend_back", "translateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "x": 33.1}, {"duration": 18, "tweenEasing": 1, "x": -27}, {"duration": 11, "tweenEasing": -1, "x": 47.9, "y": -17.4}, {"duration": 4, "tweenEasing": 1, "x": 49.1}, {"duration": 3, "tweenEasing": 0, "x": 49.1, "y": 1.5}, {"duration": 19, "tweenEasing": 1, "x": 49.1}, {"tweenEasing": 0}], "rotateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "rotate": -1.98}, {"duration": 18, "tweenEasing": 1}, {"duration": 11, "tweenEasing": -1, "rotate": -51.47}, {"duration": 27}], "scaleFrame": [{"duration": 89}]}, {"name": "suspend_front", "translateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "x": 33.5, "y": -0.45}, {"duration": 18, "tweenEasing": 1, "x": -27}, {"duration": 11, "tweenEasing": -1, "x": 35.05, "y": -65.1}, {"duration": 4, "tweenEasing": 1, "x": 49.1}, {"duration": 3, "tweenEasing": 0, "x": 46.85, "y": 0.1}, {"duration": 19, "tweenEasing": 1, "x": 49.1}, {"tweenEasing": 0}], "rotateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "rotate": 1.48}, {"duration": 18, "tweenEasing": 1}, {"duration": 11, "tweenEasing": -1, "rotate": -51.47}, {"duration": 4, "tweenEasing": 1}, {"duration": 3, "tweenEasing": 0, "rotate": 1.98}, {"duration": 20}], "scaleFrame": [{"duration": 89}]}, {"name": "base", "translateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "x": 34.1, "y": -1.75}, {"duration": 18, "tweenEasing": 1, "x": -27}, {"duration": 11, "tweenEasing": -1, "x": 36.25, "y": -17.6}, {"duration": 4, "tweenEasing": 1, "x": 49.1}, {"duration": 3, "tweenEasing": 0, "x": 50.15, "y": 7.4}, {"duration": 19, "tweenEasing": 1, "x": 49.1}, {"tweenEasing": 0}], "rotateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "rotate": 1.48}, {"duration": 18, "tweenEasing": 1}, {"duration": 11, "tweenEasing": -1, "rotate": -51.47}, {"duration": 4, "tweenEasing": 1}, {"duration": 3, "tweenEasing": 0, "rotate": 1.98}, {"duration": 20}], "scaleFrame": [{"duration": 89}]}, {"name": "driver_corpse", "translateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "x": 36.63, "y": -3.65}, {"duration": 18, "tweenEasing": 1, "x": -27.36, "y": 3.75}, {"duration": 11, "tweenEasing": -1, "x": 12.99, "y": 1.82}, {"duration": 4, "tweenEasing": 1, "x": 49.09, "y": 0.05}, {"duration": 3, "tweenEasing": 0, "x": 50.3, "y": 6.7}, {"duration": 19, "tweenEasing": 1, "x": 49.09, "y": 0.05}, {"tweenEasing": 0}], "rotateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "rotate": 5.18}, {"duration": 18, "tweenEasing": 1, "rotate": -8.7}, {"duration": 11, "tweenEasing": -1, "rotate": -51.47}, {"duration": 4, "tweenEasing": 1}, {"duration": 3, "tweenEasing": 0, "rotate": 13.9}, {"duration": 20}], "scaleFrame": [{"duration": 89}]}, {"name": "driver_hip", "translateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "x": 35.21, "y": -3.73}, {"duration": 18, "tweenEasing": 1, "x": -26.99, "y": 0.17}, {"duration": 11, "tweenEasing": -1, "x": 24.52, "y": -4.79}, {"duration": 4, "tweenEasing": 1, "x": 49.17, "y": -0.02}, {"duration": 3, "tweenEasing": 0, "x": 50.83, "y": 7.16}, {"duration": 19, "tweenEasing": 1, "x": 49.17, "y": -0.02}, {"tweenEasing": 0}], "rotateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "rotate": 5.17}, {"duration": 18, "tweenEasing": 1, "rotate": -15}, {"duration": 11, "tweenEasing": -1, "rotate": -51.47}, {"duration": 4, "tweenEasing": 1}, {"duration": 3, "tweenEasing": 0, "rotate": 1.98}, {"duration": 20}], "scaleFrame": [{"duration": 89}]}, {"name": "driver_shin", "translateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "x": 34.15, "y": -1.8}, {"duration": 18, "tweenEasing": 1, "x": -27}, {"duration": 11, "tweenEasing": -1, "x": 34.75, "y": -15.17}, {"duration": 4, "tweenEasing": 1, "x": 49.1}, {"duration": 3, "tweenEasing": 0, "x": 50.3, "y": 7.35}, {"duration": 19, "tweenEasing": 1, "x": 49.1}, {"tweenEasing": 0}], "rotateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "rotate": 1.49}, {"duration": 18, "tweenEasing": 1}, {"duration": 11, "tweenEasing": -1, "rotate": -51.47}, {"duration": 4, "tweenEasing": 1}, {"duration": 3, "tweenEasing": 0, "rotate": 1.98}, {"duration": 20}], "scaleFrame": [{"duration": 89}]}, {"name": "driver_head", "translateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "x": 38.95, "y": -3}, {"duration": 18, "tweenEasing": 1, "x": -31.3, "y": 3}, {"duration": 11, "tweenEasing": -1, "x": -11, "y": 6.3}, {"duration": 4, "tweenEasing": 1, "x": 49.1}, {"duration": 3, "tweenEasing": 0, "x": 56.3, "y": 9}, {"duration": 19, "tweenEasing": 1, "x": 49.1}, {"tweenEasing": 0}], "rotateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "rotate": 5.17}, {"duration": 18, "tweenEasing": 1, "rotate": -8.71}, {"duration": 11, "tweenEasing": -1, "rotate": -51.47}, {"duration": 4, "tweenEasing": 1}, {"duration": 3, "tweenEasing": 0, "rotate": 13.9}, {"duration": 20}], "scaleFrame": [{"duration": 89}]}, {"name": "driver_arm", "translateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "x": 37.19, "y": -3.23}, {"duration": 18, "tweenEasing": 1, "x": -28.23, "y": 3.14}, {"duration": 11, "tweenEasing": -1, "x": 7, "y": 0.74}, {"duration": 4, "tweenEasing": 1, "x": 49.17}, {"duration": 3, "tweenEasing": 0, "x": 51.58, "y": 8.02}, {"duration": 19, "tweenEasing": 1, "x": 49.17}, {"tweenEasing": 0}], "rotateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "rotate": 5.17}, {"duration": 18, "tweenEasing": 1, "rotate": -8.71}, {"duration": 11, "tweenEasing": -1, "rotate": -51.47}, {"duration": 4, "tweenEasing": 1}, {"duration": 3, "tweenEasing": 0, "rotate": 13.9}, {"duration": 20}], "scaleFrame": [{"duration": 89}]}, {"name": "driver_forearm", "translateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "x": 36.6, "y": -2.25}, {"duration": 18, "tweenEasing": 1, "x": -27.55, "y": 1.1}, {"duration": 11, "tweenEasing": -1, "x": 6.78, "y": -11.71}, {"duration": 4, "tweenEasing": 1, "x": 49.11, "y": -0.05}, {"duration": 3, "tweenEasing": 0, "x": 51.2, "y": 7.75}, {"duration": 19, "tweenEasing": 1, "x": 49.11, "y": -0.05}, {"tweenEasing": 0}], "rotateFrame": [{"duration": 20, "tweenEasing": 0}, {"duration": 13, "tweenEasing": 0, "rotate": 5.17}, {"duration": 18, "tweenEasing": 1, "rotate": -8.7}, {"duration": 11, "tweenEasing": -1, "rotate": -51.47}, {"duration": 4, "tweenEasing": 1}, {"duration": 3, "tweenEasing": 0, "rotate": -13.02}, {"duration": 20}], "scaleFrame": [{"duration": 89}]}, {"name": "fx", "translateFrame": [{"duration": 33, "tweenEasing": 0, "x": 104.2, "y": 4.9}, {"tweenEasing": 0}, {"tweenEasing": 0, "x": 13.4, "y": 0.3}, {"tweenEasing": 0, "x": 24.65, "y": 0.65}, {"tweenEasing": 0, "x": 33.9, "y": 0.9}, {"tweenEasing": 0, "x": 43.2, "y": 1.15}, {"tweenEasing": 0, "x": 51.75, "y": 1.35}, {"tweenEasing": 0, "x": 57.7, "y": 1.45}, {"tweenEasing": 0, "x": 67.2, "y": 1.5}, {"tweenEasing": 0, "x": 76.85, "y": 0.9}, {"tweenEasing": 0, "x": 81.9, "y": 1.15}, {"tweenEasing": 0, "x": 90.4, "y": 1.35}, {"tweenEasing": 0, "x": 92.1, "y": 1.45}, {"tweenEasing": 0, "x": 97.3, "y": 1.5}, {"tweenEasing": 0, "x": 102.65, "y": 0.9}, {"tweenEasing": 0, "x": 103.35, "y": 1.15}, {"tweenEasing": 0, "x": 107.6, "y": 1.35}, {"tweenEasing": 0, "x": 109.25, "y": 1.45}, {"tweenEasing": 0, "x": 110.2, "y": 1.5}, {"tweenEasing": 0, "x": 111.25, "y": 0.9}, {"tweenEasing": 0, "x": 111.95, "y": 1.15}, {"tweenEasing": 0, "x": 107.6, "y": 1.35}, {"tweenEasing": 0, "x": 109.25, "y": 1.45}, {"tweenEasing": 0, "x": 110.2, "y": 1.5}, {"tweenEasing": 0, "x": 100.45, "y": 1.6}, {"x": 110.05, "y": 1.6}, {"duration": 31, "tweenEasing": 0, "x": 104.2, "y": 4.9}], "scaleFrame": [{"duration": 33, "tweenEasing": 0, "x": 0.7132, "y": 0.7132}, {"tweenEasing": 0}, {"tweenEasing": 0, "x": 0.9325, "y": 0.9325}, {"tweenEasing": 0, "x": 0.873, "y": 0.873}, {"tweenEasing": 0, "x": 0.8214, "y": 0.8214}, {"tweenEasing": 0, "x": 0.7778, "y": 0.7778}, {"tweenEasing": 0, "x": 0.742, "y": 0.742}, {"tweenEasing": 0, "x": 0.7143, "y": 0.7143}, {"tweenEasing": 0, "x": 0.6944, "y": 0.6944}, {"tweenEasing": 0, "x": 0.8214, "y": 0.8214}, {"tweenEasing": 0, "x": 0.7778, "y": 0.7778}, {"tweenEasing": 0, "x": 0.742, "y": 0.742}, {"tweenEasing": 0, "x": 0.7143, "y": 0.7143}, {"tweenEasing": 0, "x": 0.6944, "y": 0.6944}, {"tweenEasing": 0, "x": 0.8214, "y": 0.8214}, {"tweenEasing": 0, "x": 0.7778, "y": 0.7778}, {"tweenEasing": 0, "x": 0.742, "y": 0.742}, {"tweenEasing": 0, "x": 0.7143, "y": 0.7143}, {"tweenEasing": 0, "x": 0.6944, "y": 0.6944}, {"tweenEasing": 0, "x": 0.8214, "y": 0.8214}, {"tweenEasing": 0, "x": 0.7778, "y": 0.7778}, {"tweenEasing": 0, "x": 0.742, "y": 0.742}, {"tweenEasing": 0, "x": 0.7143, "y": 0.7143}, {"tweenEasing": 0, "x": 0.6944, "y": 0.6944}, {"tweenEasing": 0, "x": 0.6825, "y": 0.6825}, {"x": 0.6786, "y": 0.6786}, {"duration": 31, "tweenEasing": 0, "x": 0.7132, "y": 0.7132}]}], "slot": [{"name": "fx", "displayFrame": [{"duration": 33, "value": -1}, {}, {"value": 1}, {"value": 2}, {"value": 3}, {"value": 4}, {"value": 5}, {"value": 6}, {"value": 7}, {"value": 3}, {"value": 4}, {"value": 5}, {"value": 6}, {"value": 7}, {"value": 3}, {"value": 4}, {"value": 5}, {"value": 6}, {"value": 7}, {"value": 3}, {"value": 4}, {"value": 5}, {"value": 6}, {"value": 7}, {"value": 8}, {"value": 9}, {"duration": 31, "value": -1}]}]}]}, {"name": "wheel_sportbike_anim", "bone": [{"inheritScale": false, "name": "wheel_sprt"}], "slot": [{"name": "wheel_sprt", "parent": "wheel_sprt"}], "skin": [{"slot": [{"name": "wheel_sprt", "display": [{"name": "Duplicate Items Folder/wheel copy"}]}]}], "animation": [{"duration": 22, "playTimes": 0, "fadeInTime": 0.3, "name": "wheel_anim", "bone": [{"name": "wheel_sprt", "rotateFrame": [{"duration": 21, "tweenEasing": 0, "clockwise": 2}, {"tweenEasing": 0}], "scaleFrame": [{"duration": 22}]}]}]}]}