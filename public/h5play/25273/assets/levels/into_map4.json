{"layers": [[{"params": {"snapToGrid": true, "vertices": [{"x": 0, "y": 40, "length": 40}, {"x": 65, "y": 105, "length": 123.4908903522847}], "wireframe": false, "width": 78, "rotation": 0, "textureMode": true, "height": 78, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 0, "smoothing": true, "y": -460, "repeatTexture": true, "textureOffset": 0, "x": 12}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"snapToGrid": true, "vertices": [{"x": 0, "y": 40, "length": 40}, {"x": 65, "y": 105, "length": 123.4908903522847}], "wireframe": false, "width": 78, "rotation": 0, "textureMode": true, "height": 78, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 0, "smoothing": true, "y": -830, "repeatTexture": true, "textureOffset": 0, "x": 14}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"snapToGrid": true, "vertices": [{"x": 0, "y": 40, "length": 40}, {"x": 65, "y": 105, "length": 123.4908903522847}], "wireframe": false, "width": 78, "rotation": 0, "textureMode": true, "height": 78, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 0, "smoothing": true, "y": -700, "repeatTexture": true, "textureOffset": 0, "x": 13}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"snapToGrid": true, "vertices": [{"x": 0, "y": 40, "length": 40}, {"x": 65, "y": 105, "length": 123.4908903522847}], "wireframe": false, "width": 78, "rotation": 0, "textureMode": true, "height": 78, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 0, "smoothing": true, "y": -580, "repeatTexture": true, "textureOffset": 0, "x": 12}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"snapToGrid": true, "vertices": [{"x": 0, "y": 40, "length": 40}, {"x": 65, "y": 105, "length": 123.4908903522847}], "wireframe": false, "width": 78, "rotation": 0, "textureMode": true, "height": 78, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 0, "smoothing": true, "y": -960, "repeatTexture": true, "textureOffset": 0, "x": 14}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"snapToGrid": true, "vertices": [{"x": 0, "y": -5, "length": 5}, {"x": 250, "y": 695, "length": 738.596642288604}], "wireframe": false, "width": 263, "rotation": 0, "textureMode": true, "height": 713, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 0, "smoothing": true, "y": -270, "repeatTexture": true, "textureOffset": 0, "x": 1640}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"snapToGrid": true, "vertices": [{"x": 0, "y": -5, "length": 5}, {"x": 250, "y": 695, "length": 738.596642288604}], "wireframe": false, "width": 263, "rotation": 0, "textureMode": true, "height": 713, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 0, "smoothing": true, "y": -270, "repeatTexture": true, "textureOffset": 0, "x": 1260}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"snapToGrid": true, "vertices": [{"x": 35, "y": -1200, "length": 1200.5103081606587}, {"x": 35, "y": 5, "length": 35.35533905932738}, {"x": 90, "y": 5, "length": 90.13878188659973}], "wireframe": false, "width": 68, "rotation": 0, "textureMode": true, "height": 1218, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 0, "smoothing": true, "y": -320, "repeatTexture": true, "textureOffset": 0, "x": -20}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"x": 1050, "vertices": [{"x": 0, "y": -830, "length": 830}, {"x": 100, "y": -830, "length": 836.0023923410746}, {"x": 100, "y": 100, "length": 141.4213562373095}, {"x": 0, "y": 100, "length": 100}], "y": 120, "rem": true, "isStatic": false, "rotation": 0, "graphic": true, "plr": false, "ignore": false, "id": -1, "density": 3, "safeId": -1, "line": true, "isWheel": false, "layer": 3, "physic": true}, "className": "frg.game.editor.objects::LandscapeShaper"}, {"params": {"snapToGrid": true, "vertices": [{"x": -90, "y": 640, "length": 646.297145282261}, {"x": 28, "y": 0, "length": 28}, {"x": 75, "y": 0, "length": 75}, {"x": 175, "y": 635, "length": 658.6729082025463}], "wireframe": false, "width": 278, "rotation": 0, "textureMode": true, "height": 653, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 0, "smoothing": true, "y": -210, "repeatTexture": true, "textureOffset": 0, "x": 1048}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"bg": true, "x": 5340, "id": -1, "vertices": [{"x": -150, "y": 0, "length": 150}, {"x": 3035, "y": 0, "length": 3035}, {"x": 2990, "y": 1380, "length": 3293.0988445535613}, {"x": 240, "y": 1365, "length": 1385.938310315434}, {"x": 140, "y": 1365, "length": 1372.160704873886}], "y": 500, "viscosity": 1.5, "rotation": 0, "density": 1.5, "sf": true}, "className": "frg.game.editor.objects::WaterShaper"}, {"params": {"snapToGrid": true, "vertices": [{"x": 0, "y": -5, "length": 5}, {"x": 250, "y": 695, "length": 738.596642288604}], "wireframe": false, "width": 263, "rotation": 0, "textureMode": true, "height": 713, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 0, "smoothing": true, "y": -270, "repeatTexture": true, "textureOffset": 0, "x": 2020}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"shape": false, "snapToGrid": false, "vertices": [{"x": -1308, "y": -68, "length": 1309.7663913843567}, {"x": 1265, "y": -77, "length": 1267.3413115652784}, {"x": 2259, "y": -515, "length": 2316.960508942697}, {"x": 2775, "y": -518, "length": 2822.932694911446}, {"x": 3902, "y": -193, "length": 3906.7701493689133}, {"x": 4244, "y": -245, "length": 4251.065866344581}, {"x": 4752, "y": -128, "length": 4753.7235931425375}, {"x": 5172, "y": -55, "length": 5172.292431794629}, {"x": 5374, "y": -63, "length": 5374.36926531849}, {"x": 6584, "y": 167, "length": 6586.117596885133}, {"x": 8594, "y": 149, "length": 8595.29155991814}, {"x": 9477, "y": -140, "length": 9478.03402610478}, {"x": 10647, "y": -148, "length": 10648.028596881208}, {"x": 13144, "y": -150, "length": 13144.855875969124}, {"x": 21265, "y": 0, "length": 21265}], "wireframe": false, "x": -780, "y": 430, "directed": false, "isRoad": false, "camera": true, "lineId": 0, "thick": 128, "physic": false, "cameraOffsetY": 30, "width": 22586, "rotation": 0, "textureMode": true, "height": 698, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "smoothing": true, "direction": 90, "repeatTexture": true, "textureOffset": 0, "line": false}, "className": "frg.game.editor.objects::GroundPather"}, {"params": {"x": 80, "vertices": [{"x": -5, "y": -1475, "length": 1475.0084745519262}, {"x": -5, "y": -495, "length": 495.025251881154}, {"x": -5, "y": 0, "length": 5}, {"x": 375, "y": 0, "length": 375}, {"x": 510, "y": -30, "length": 510.88159097779203}], "wireframe": false, "textureOffset": 0, "width": 531.5, "rotation": 0, "snapToGrid": true, "height": 1491.5, "action": 0, "y": -320, "stretchTexture": false, "type": 0, "isStatic": true, "textureMode": true, "id": -1, "density": "1", "safeId": -1, "shapeH": 20, "originOffsetRatio": 0, "repeatTexture": true, "graphic": true, "physic": true, "smoothing": true, "straightSides": true}, "className": "frg.game.editor.objects::DynamicPather"}, {"params": {"x": 70, "vertices": [{"x": 58, "y": -121, "length": 134.18271125595876}, {"x": 57, "y": 21, "length": 60.74537019394976}, {"x": 79, "y": 70, "length": 105.55093557141026}, {"x": 143, "y": 78, "length": 162.88953311984167}, {"x": 459, "y": 34, "length": 460.25753660315}], "wireframe": false, "textureOffset": 0, "width": 418.5, "rotation": 0, "snapToGrid": false, "height": 215.6, "action": 0, "y": 210, "stretchTexture": false, "type": 0, "isStatic": true, "textureMode": true, "id": -1, "density": "1", "safeId": -1, "shapeH": 20, "originOffsetRatio": 0, "repeatTexture": true, "graphic": true, "physic": true, "smoothing": true, "straightSides": true}, "className": "frg.game.editor.objects::DynamicPather"}, {"params": {"snapToGrid": true, "vertices": [{"x": 0, "y": -5, "length": 5}, {"x": 250, "y": 695, "length": 738.596642288604}], "wireframe": false, "width": 263, "rotation": 0, "textureMode": true, "height": 713, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 0, "smoothing": true, "y": -270, "repeatTexture": true, "textureOffset": 0, "x": 2780}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"x": 10, "vertices": [{"x": 115, "y": 25, "length": 117.6860229593982}, {"x": 125, "y": 55, "length": 136.56500283747664}, {"x": 145, "y": 85, "length": 168.0773631397161}, {"x": 170, "y": 125, "length": 211.00947846009194}, {"x": -195, "y": 235, "length": 305.3686296920494}, {"x": -230, "y": 245, "length": 336.04315199093105}, {"x": -260, "y": 245, "length": 357.24641355792505}, {"x": -275, "y": 240, "length": 365}, {"x": -285, "y": 230, "length": 366.23080154459973}, {"x": -290, "y": 210, "length": 358.05027579936313}, {"x": -290, "y": 190, "length": 346.69871646719434}, {"x": -290, "y": 170, "length": 336.1547262794322}, {"x": -285, "y": 155, "length": 324.4225639501667}, {"x": -235, "y": 155, "length": 281.51376520518494}, {"x": -235, "y": 175, "length": 293.00170647967224}, {"x": -230, "y": 190, "length": 298.328677803526}, {"x": -215, "y": 195, "length": 290.25850547399983}, {"x": -195, "y": 190, "length": 272.2590678012396}], "y": 160, "rem": true, "isStatic": true, "rotation": 0, "graphic": true, "plr": false, "ignore": false, "id": -1, "density": 1, "safeId": -1, "line": true, "isWheel": false, "layer": 0, "physic": false}, "className": "frg.game.editor.objects::LandscapeShaper"}, {"params": {"snapToGrid": true, "vertices": [{"x": 0, "y": -5, "length": 5}, {"x": 250, "y": 695, "length": 738.596642288604}], "wireframe": false, "width": 263, "rotation": 0, "textureMode": true, "height": 713, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 0, "smoothing": true, "y": -270, "repeatTexture": true, "textureOffset": 0, "x": 2390}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"rotation": 0, "x": 190, "height": 324.1, "y": 139, "width": 345.1}, "className": "OvalTemplate"}, {"params": {"x": 291, "vertices": [{"x": 100, "y": -1, "length": 100.00499987500625}, {"x": 99, "y": 8, "length": 99.32270636667126}, {"x": 98, "y": 18, "length": 99.6393496566492}, {"x": 95, "y": 28, "length": 99.04039579888602}, {"x": 92, "y": 38, "length": 99.53893710503444}, {"x": 88, "y": 46, "length": 99.29753269845128}, {"x": 83, "y": 55, "length": 99.56907150315303}, {"x": 77, "y": 62, "length": 98.85848471426213}, {"x": 70, "y": 70, "length": 98.99494936611666}, {"x": 64, "y": 76, "length": 99.35793878699377}, {"x": 55, "y": 83, "length": 99.56907150315303}, {"x": 47, "y": 87, "length": 98.88377015466189}, {"x": 38, "y": 92, "length": 99.53893710503444}, {"x": 29, "y": 95, "length": 99.32774033471213}, {"x": 19, "y": 98, "length": 99.82484660644363}, {"x": 10, "y": 99, "length": 99.50376877284599}, {"x": 0, "y": 100, "length": 100}, {"x": -10, "y": 100, "length": 100.4987562112089}, {"x": -19, "y": 98, "length": 99.82484660644363}, {"x": -28, "y": 95, "length": 99.04039579888602}, {"x": -38, "y": 92, "length": 99.53893710503444}, {"x": -46, "y": 88, "length": 99.29753269845128}, {"x": -55, "y": 83, "length": 99.56907150315303}, {"x": -62, "y": 77, "length": 98.85848471426213}, {"x": -70, "y": 70, "length": 98.99494936611666}, {"x": -76, "y": 64, "length": 99.35793878699377}, {"x": -83, "y": 55, "length": 99.56907150315303}, {"x": -87, "y": 47, "length": 98.88377015466189}, {"x": -92, "y": 38, "length": 99.53893710503444}, {"x": -95, "y": 29, "length": 99.32774033471213}, {"x": -98, "y": 19, "length": 99.82484660644363}, {"x": -99, "y": 10, "length": 99.50376877284599}, {"x": -100, "y": 0, "length": 100}, {"x": -99, "y": -8, "length": 99.32270636667126}, {"x": -98, "y": -19, "length": 99.82484660644363}, {"x": -95, "y": -28, "length": 99.04039579888602}, {"x": -92, "y": -38, "length": 99.53893710503444}, {"x": -88, "y": -46, "length": 99.29753269845128}, {"x": -83, "y": -55, "length": 99.56907150315303}, {"x": -77, "y": -62, "length": 98.85848471426213}, {"x": -70, "y": -70, "length": 98.99494936611666}, {"x": -64, "y": -76, "length": 99.35793878699377}, {"x": -55, "y": -83, "length": 99.56907150315303}, {"x": -47, "y": -87, "length": 98.88377015466189}, {"x": -38, "y": -92, "length": 99.53893710503444}, {"x": -29, "y": -95, "length": 99.32774033471213}, {"x": -19, "y": -98, "length": 99.82484660644363}, {"x": -10, "y": -99, "length": 99.50376877284599}, {"x": 0, "y": -100, "length": 100}, {"x": 8, "y": -99, "length": 99.32270636667126}, {"x": 19, "y": -98, "length": 99.82484660644363}, {"x": 28, "y": -95, "length": 99.04039579888602}, {"x": 38, "y": -92, "length": 99.53893710503444}, {"x": 46, "y": -88, "length": 99.29753269845128}, {"x": 55, "y": -83, "length": 99.56907150315303}, {"x": 62, "y": -77, "length": 98.85848471426213}, {"x": 70, "y": -70, "length": 98.99494936611666}, {"x": 76, "y": -64, "length": 99.35793878699377}, {"x": 83, "y": -55, "length": 99.56907150315303}, {"x": 87, "y": -47, "length": 98.88377015466189}, {"x": 92, "y": -38, "length": 99.53893710503444}, {"x": 95, "y": -29, "length": 99.32774033471213}, {"x": 98, "y": -19, "length": 99.82484660644363}, {"x": 99, "y": -10, "length": 99.50376877284599}], "y": 154, "rem": true, "isStatic": false, "rotation": 0, "graphic": true, "plr": false, "ignore": false, "id": -1, "density": 0.8, "safeId": -1, "line": true, "isWheel": false, "layer": 6, "physic": true}, "className": "frg.game.editor.objects::LandscapeShaper"}, {"params": {"x": 120, "vertices": [{"x": 66, "y": -3, "length": 66.06814663663572}, {"x": 450, "y": 30, "length": 450.99889135118724}, {"x": 440, "y": 325, "length": 547.0146250330058}, {"x": 80, "y": 320, "length": 329.84845004941286}, {"x": 56, "y": 320, "length": 324.86304806795124}, {"x": 32, "y": 319, "length": 320.6009981269553}, {"x": 5, "y": 313, "length": 313.0399335548102}, {"x": -18, "y": 303, "length": 303.5341825890455}, {"x": -42, "y": 290, "length": 293.025596151599}, {"x": -63, "y": 271, "length": 278.22652641328074}, {"x": -80, "y": 254, "length": 266.3005820496831}, {"x": -99, "y": 225, "length": 245.81700510745793}, {"x": -105, "y": 210, "length": 234.7871376374779}, {"x": -108, "y": 195, "length": 222.9102958591191}, {"x": -111, "y": 177, "length": 208.92582415776178}, {"x": -112, "y": 158, "length": 193.66982212001952}, {"x": -111, "y": 138, "length": 177.1016657177453}, {"x": -108, "y": 120, "length": 161.44348856488452}, {"x": -102, "y": 101, "length": 143.54441821262157}, {"x": -92, "y": 81, "length": 122.57650672131263}, {"x": -80, "y": 63, "length": 101.82828683622247}, {"x": -65, "y": 46, "length": 79.63039620647382}, {"x": -47, "y": 30, "length": 55.758407437802596}, {"x": -26, "y": 17, "length": 31.064449134018133}, {"x": -1, "y": 7, "length": 7.0710678118654755}, {"x": 22, "y": 0, "length": 22}, {"x": 40, "y": -2, "length": 40.049968789001575}], "y": -10, "rem": true, "isStatic": true, "rotation": -9, "graphic": true, "plr": false, "ignore": false, "id": -1, "density": 1, "safeId": -1, "line": true, "isWheel": false, "layer": 4, "physic": false}, "className": "frg.game.editor.objects::LandscapeShaper"}, {"params": {"x": 355, "vertices": [{"x": 132, "y": 0, "length": 132}, {"x": 131, "y": 11, "length": 131.4610208388783}, {"x": 128, "y": 25, "length": 130.41855696180662}, {"x": 126, "y": 36, "length": 131.04197800704932}, {"x": 122, "y": 50, "length": 131.84839779079607}, {"x": 116, "y": 60, "length": 130.59862173851607}, {"x": 109, "y": 72, "length": 130.6330739131557}, {"x": 102, "y": 82, "length": 130.87398519186308}, {"x": 93, "y": 93, "length": 131.52186130069785}, {"x": 83, "y": 100, "length": 129.9576854210631}, {"x": 72, "y": 109, "length": 130.6330739131557}, {"x": 62, "y": 115, "length": 130.64838307457157}, {"x": 49, "y": 122, "length": 131.47243057006287}, {"x": 38, "y": 125, "length": 130.64838307457157}, {"x": 25, "y": 128, "length": 130.41855696180662}, {"x": 13, "y": 131, "length": 131.64345787011217}, {"x": 0, "y": 132, "length": 132}, {"x": -11, "y": 131, "length": 131.4610208388783}, {"x": -25, "y": 128, "length": 130.41855696180662}, {"x": -35, "y": 126, "length": 130.77079184588584}, {"x": -49, "y": 122, "length": 131.47243057006287}, {"x": -60, "y": 116, "length": 130.59862173851607}, {"x": -72, "y": 109, "length": 130.6330739131557}, {"x": -82, "y": 102, "length": 130.87398519186308}, {"x": -93, "y": 93, "length": 131.52186130069785}, {"x": -100, "y": 84, "length": 130.59862173851607}, {"x": -109, "y": 72, "length": 130.6330739131557}, {"x": -115, "y": 62, "length": 130.64838307457157}, {"x": -122, "y": 50, "length": 131.84839779079607}, {"x": -125, "y": 38, "length": 130.64838307457157}, {"x": -128, "y": 25, "length": 130.41855696180662}, {"x": -131, "y": 13, "length": 131.64345787011217}, {"x": -132, "y": 0, "length": 132}, {"x": -131, "y": -11, "length": 131.4610208388783}, {"x": -128, "y": -25, "length": 130.41855696180662}, {"x": -126, "y": -36, "length": 131.04197800704932}, {"x": -122, "y": -50, "length": 131.84839779079607}, {"x": -116, "y": -60, "length": 130.59862173851607}, {"x": -109, "y": -72, "length": 130.6330739131557}, {"x": -102, "y": -82, "length": 130.87398519186308}, {"x": -93, "y": -93, "length": 131.52186130069785}, {"x": -83, "y": -100, "length": 129.9576854210631}, {"x": -72, "y": -109, "length": 130.6330739131557}, {"x": -62, "y": -115, "length": 130.64838307457157}, {"x": -49, "y": -122, "length": 131.47243057006287}, {"x": -38, "y": -125, "length": 130.64838307457157}, {"x": -25, "y": -128, "length": 130.41855696180662}, {"x": -13, "y": -131, "length": 131.64345787011217}, {"x": 0, "y": -132, "length": 132}, {"x": 11, "y": -131, "length": 131.4610208388783}, {"x": 25, "y": -128, "length": 130.41855696180662}, {"x": 35, "y": -126, "length": 130.77079184588584}, {"x": 49, "y": -122, "length": 131.47243057006287}, {"x": 60, "y": -116, "length": 130.59862173851607}, {"x": 72, "y": -109, "length": 130.6330739131557}, {"x": 82, "y": -102, "length": 130.87398519186308}, {"x": 93, "y": -93, "length": 131.52186130069785}, {"x": 100, "y": -84, "length": 130.59862173851607}, {"x": 109, "y": -72, "length": 130.6330739131557}, {"x": 115, "y": -62, "length": 130.64838307457157}, {"x": 122, "y": -50, "length": 131.84839779079607}, {"x": 125, "y": -38, "length": 130.64838307457157}, {"x": 128, "y": -25, "length": 130.41855696180662}, {"x": 131, "y": -13, "length": 131.64345787011217}], "y": 276, "rem": true, "isStatic": true, "rotation": 0, "graphic": true, "plr": false, "ignore": false, "id": -1, "density": 1, "safeId": -1, "line": true, "isWheel": false, "layer": 0, "physic": false}, "className": "frg.game.editor.objects::LandscapeShaper"}, {"params": {"x": 1240, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 1635, "y": 0, "length": 1635}], "wireframe": false, "textureOffset": 0, "width": 1648, "rotation": 0, "snapToGrid": true, "height": 20, "action": 0, "y": -270, "stretchTexture": false, "type": 0, "isStatic": true, "textureMode": true, "id": -1, "density": "1", "safeId": -1, "shapeH": 20, "originOffsetRatio": 0, "repeatTexture": true, "graphic": true, "physic": true, "smoothing": true, "straightSides": true}, "className": "frg.game.editor.objects::DynamicPather"}, {"params": {"bg": true, "x": -420, "id": -1, "vertices": [{"x": -725, "y": 0, "length": 725}, {"x": 3510, "y": 0, "length": 3510}, {"x": 3490, "y": 300, "length": 3502.870251665054}, {"x": -760, "y": 285, "length": 811.6803558051655}], "y": 270, "viscosity": 1.5, "rotation": 0, "density": 1.5, "sf": true}, "className": "frg.game.editor.objects::WaterShaper"}, {"params": {"x": 7890, "vertices": [{"x": -17, "y": -31, "length": 35.35533905932738}, {"x": 152, "y": 20, "length": 153.3101431738944}, {"x": 24, "y": 135, "length": 137.11673858431726}], "wireframe": false, "textureOffset": 0, "width": 191.4, "rotation": -144, "snapToGrid": false, "height": 180.15, "action": 0, "y": 265, "stretchTexture": false, "type": 7, "isStatic": true, "textureMode": true, "id": -1, "density": "1", "safeId": -1, "shapeH": 16, "originOffsetRatio": 0, "repeatTexture": true, "graphic": true, "physic": true, "smoothing": true, "straightSides": true}, "className": "frg.game.editor.objects::DynamicPather"}, {"params": {"x": 5240, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 152, "y": 20, "length": 153.3101431738944}, {"x": 49, "y": 199, "length": 204.9438947614688}], "wireframe": false, "textureOffset": 0, "width": 171.4, "rotation": 0, "snapToGrid": false, "height": 213.45, "action": 0, "y": 140, "stretchTexture": false, "type": 7, "isStatic": true, "textureMode": true, "id": -1, "density": "1", "safeId": -1, "shapeH": 16, "originOffsetRatio": 0, "repeatTexture": true, "graphic": true, "physic": true, "smoothing": true, "straightSides": true}, "className": "frg.game.editor.objects::DynamicPather"}, {"params": {"x": 9693, "vertices": [{"x": 79, "y": -2, "length": 79.02531240052139}, {"x": 79, "y": 7, "length": 79.30952023559341}, {"x": 78, "y": 15, "length": 79.42921376924235}, {"x": 76, "y": 22, "length": 79.12016177940993}, {"x": 73, "y": 30, "length": 78.92401408950256}, {"x": 70, "y": 37, "length": 79.17701686727027}, {"x": 66, "y": 44, "length": 79.32212806020776}, {"x": 62, "y": 50, "length": 79.64923100695951}, {"x": 56, "y": 56, "length": 79.19595949289332}, {"x": 51, "y": 61, "length": 79.51100552753688}, {"x": 44, "y": 66, "length": 79.32212806020776}, {"x": 38, "y": 70, "length": 79.64923100695951}, {"x": 30, "y": 73, "length": 78.92401408950256}, {"x": 23, "y": 76, "length": 79.40403012442127}, {"x": 15, "y": 78, "length": 79.42921376924235}, {"x": 8, "y": 79, "length": 79.40403012442127}, {"x": 0, "y": 79, "length": 79}, {"x": -7, "y": 79, "length": 79.30952023559341}, {"x": -15, "y": 78, "length": 79.42921376924235}, {"x": -22, "y": 76, "length": 79.12016177940993}, {"x": -30, "y": 73, "length": 78.92401408950256}, {"x": -37, "y": 70, "length": 79.17701686727027}, {"x": -44, "y": 66, "length": 79.32212806020776}, {"x": -50, "y": 62, "length": 79.64923100695951}, {"x": -56, "y": 56, "length": 79.19595949289332}, {"x": -61, "y": 51, "length": 79.51100552753688}, {"x": -66, "y": 44, "length": 79.32212806020776}, {"x": -70, "y": 38, "length": 79.64923100695951}, {"x": -73, "y": 30, "length": 78.92401408950256}, {"x": -76, "y": 23, "length": 79.40403012442127}, {"x": -78, "y": 15, "length": 79.42921376924235}, {"x": -79, "y": 8, "length": 79.40403012442127}, {"x": -79, "y": 0, "length": 79}, {"x": -79, "y": -7, "length": 79.30952023559341}, {"x": -77, "y": -15, "length": 78.44743462982075}, {"x": -76, "y": -22, "length": 79.12016177940993}, {"x": -73, "y": -30, "length": 78.92401408950256}, {"x": -70, "y": -37, "length": 79.17701686727027}, {"x": -66, "y": -42, "length": 78.23042886243178}, {"x": -61, "y": -49, "length": 78.24321056807422}, {"x": -56, "y": -56, "length": 79.19595949289332}, {"x": -51, "y": -61, "length": 79.51100552753688}, {"x": -44, "y": -66, "length": 79.32212806020776}, {"x": -38, "y": -70, "length": 79.64923100695951}, {"x": -30, "y": -73, "length": 78.92401408950256}, {"x": -23, "y": -76, "length": 79.40403012442127}, {"x": -15, "y": -78, "length": 79.42921376924235}, {"x": -8, "y": -79, "length": 79.40403012442127}, {"x": 0, "y": -79, "length": 79}, {"x": 7, "y": -79, "length": 79.30952023559341}, {"x": 15, "y": -78, "length": 79.42921376924235}, {"x": 22, "y": -76, "length": 79.12016177940993}, {"x": 30, "y": -73, "length": 78.92401408950256}, {"x": 37, "y": -70, "length": 79.17701686727027}, {"x": 44, "y": -66, "length": 79.32212806020776}, {"x": 50, "y": -62, "length": 79.64923100695951}, {"x": 56, "y": -56, "length": 79.19595949289332}, {"x": 61, "y": -51, "length": 79.51100552753688}, {"x": 66, "y": -44, "length": 79.32212806020776}, {"x": 70, "y": -38, "length": 79.64923100695951}, {"x": 73, "y": -30, "length": 78.92401408950256}, {"x": 76, "y": -23, "length": 79.40403012442127}, {"x": 78, "y": -15, "length": 79.42921376924235}, {"x": 79, "y": -8, "length": 79.40403012442127}], "y": 186, "rem": true, "isStatic": false, "rotation": 0, "graphic": true, "plr": false, "ignore": false, "id": -1, "density": 9, "safeId": -1, "line": true, "isWheel": true, "layer": 0, "physic": true}, "className": "frg.game.editor.objects::LandscapeShaper"}, {"params": {"x": 10205, "vertices": [{"x": 79, "y": 0, "length": 79}, {"x": 79, "y": 7, "length": 79.30952023559341}, {"x": 78, "y": 15, "length": 79.42921376924235}, {"x": 76, "y": 22, "length": 79.12016177940993}, {"x": 73, "y": 30, "length": 78.92401408950256}, {"x": 70, "y": 37, "length": 79.17701686727027}, {"x": 66, "y": 44, "length": 79.32212806020776}, {"x": 62, "y": 50, "length": 79.64923100695951}, {"x": 56, "y": 56, "length": 79.19595949289332}, {"x": 51, "y": 61, "length": 79.51100552753688}, {"x": 44, "y": 66, "length": 79.32212806020776}, {"x": 38, "y": 70, "length": 79.64923100695951}, {"x": 30, "y": 73, "length": 78.92401408950256}, {"x": 23, "y": 76, "length": 79.40403012442127}, {"x": 15, "y": 78, "length": 79.42921376924235}, {"x": 8, "y": 79, "length": 79.40403012442127}, {"x": 0, "y": 79, "length": 79}, {"x": -7, "y": 79, "length": 79.30952023559341}, {"x": -15, "y": 78, "length": 79.42921376924235}, {"x": -22, "y": 76, "length": 79.12016177940993}, {"x": -30, "y": 73, "length": 78.92401408950256}, {"x": -37, "y": 70, "length": 79.17701686727027}, {"x": -44, "y": 66, "length": 79.32212806020776}, {"x": -50, "y": 62, "length": 79.64923100695951}, {"x": -56, "y": 56, "length": 79.19595949289332}, {"x": -61, "y": 51, "length": 79.51100552753688}, {"x": -66, "y": 44, "length": 79.32212806020776}, {"x": -70, "y": 37, "length": 79.17701686727027}, {"x": -73, "y": 30, "length": 78.92401408950256}, {"x": -76, "y": 23, "length": 79.40403012442127}, {"x": -78, "y": 15, "length": 79.42921376924235}, {"x": -79, "y": 8, "length": 79.40403012442127}, {"x": -79, "y": 0, "length": 79}, {"x": -79, "y": -7, "length": 79.30952023559341}, {"x": -78, "y": -15, "length": 79.42921376924235}, {"x": -76, "y": -22, "length": 79.12016177940993}, {"x": -73, "y": -30, "length": 78.92401408950256}, {"x": -70, "y": -37, "length": 79.17701686727027}, {"x": -66, "y": -44, "length": 79.32212806020776}, {"x": -62, "y": -50, "length": 79.64923100695951}, {"x": -56, "y": -56, "length": 79.19595949289332}, {"x": -51, "y": -61, "length": 79.51100552753688}, {"x": -44, "y": -66, "length": 79.32212806020776}, {"x": -38, "y": -70, "length": 79.64923100695951}, {"x": -30, "y": -73, "length": 78.92401408950256}, {"x": -23, "y": -76, "length": 79.40403012442127}, {"x": -15, "y": -78, "length": 79.42921376924235}, {"x": -8, "y": -79, "length": 79.40403012442127}, {"x": -2, "y": -79, "length": 79.02531240052139}, {"x": 7, "y": -79, "length": 79.30952023559341}, {"x": 15, "y": -78, "length": 79.42921376924235}, {"x": 22, "y": -76, "length": 79.12016177940993}, {"x": 30, "y": -73, "length": 78.92401408950256}, {"x": 37, "y": -70, "length": 79.17701686727027}, {"x": 44, "y": -66, "length": 79.32212806020776}, {"x": 50, "y": -62, "length": 79.64923100695951}, {"x": 56, "y": -56, "length": 79.19595949289332}, {"x": 61, "y": -51, "length": 79.51100552753688}, {"x": 66, "y": -44, "length": 79.32212806020776}, {"x": 70, "y": -38, "length": 79.64923100695951}, {"x": 73, "y": -30, "length": 78.92401408950256}, {"x": 76, "y": -23, "length": 79.40403012442127}, {"x": 78, "y": -15, "length": 79.42921376924235}, {"x": 79, "y": -8, "length": 79.40403012442127}], "y": 186, "rem": true, "isStatic": false, "rotation": 0, "graphic": true, "plr": false, "ignore": false, "id": -1, "density": 9, "safeId": -1, "line": true, "isWheel": true, "layer": 0, "physic": true}, "className": "frg.game.editor.objects::LandscapeShaper"}, {"params": {"bg": true, "x": 9050, "id": -1, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 3195, "y": 5, "length": 3195.003912360672}, {"x": 3195, "y": 105, "length": 3196.7248865049364}, {"x": 0, "y": 100, "length": 100}], "y": 210, "viscosity": 1.5, "rotation": 0, "density": 1.5, "sf": true}, "className": "frg.game.editor.objects::WaterShaper"}, {"params": {"x": 9590, "vertices": [{"x": 0, "y": -135, "length": 135}, {"x": 55, "y": -135, "length": 145.7737973711325}, {"x": 55, "y": -30, "length": 62.64982043070834}, {"x": 565, "y": -30, "length": 565.795899596312}, {"x": 585, "y": -60, "length": 588.0688735173798}, {"x": 550, "y": -160, "length": 572.8001396647875}, {"x": 530, "y": -185, "length": 561.3599558215744}, {"x": 480, "y": -185, "length": 514.4171459039833}, {"x": 475, "y": -215, "length": 521.3923666491484}, {"x": 555, "y": -215, "length": 595.1890455981192}, {"x": 585, "y": -165, "length": 607.8239876806442}, {"x": 630, "y": -60, "length": 632.8506932918696}, {"x": 870, "y": 0, "length": 870}, {"x": 890, "y": 135, "length": 900.1805374479055}, {"x": 685, "y": 135, "length": 698.176195526602}, {"x": 630, "y": 200, "length": 660.9841147864297}, {"x": 600, "y": 200, "length": 632.4555320336759}, {"x": 570, "y": 155, "length": 590.6987387831465}, {"x": 550, "y": 140, "length": 567.5385449465084}, {"x": 175, "y": 140, "length": 224.1093483101497}, {"x": 155, "y": 150, "length": 215.6965461012299}, {"x": 125, "y": 195, "length": 231.62469643800938}, {"x": 85, "y": 195, "length": 212.72047386182646}, {"x": 55, "y": 115, "length": 127.47548783981962}, {"x": 25, "y": 115, "length": 117.6860229593982}, {"x": 0, "y": 100, "length": 100}], "y": 10, "rem": true, "isStatic": false, "rotation": 0, "graphic": true, "plr": false, "ignore": false, "id": -1, "density": 1, "safeId": -1, "line": true, "isWheel": false, "layer": 4, "physic": true}, "className": "frg.game.editor.objects::LandscapeShaper"}, {"params": {"shape": true, "snapToGrid": false, "vertices": [{"x": -1290, "y": 0, "length": 1290}, {"x": 1617, "y": 1, "length": 1617.0003092145653}, {"x": 2370, "y": 0, "length": 2370}, {"x": 3745, "y": 0, "length": 3745}, {"x": 3795, "y": -15, "length": 3795.029644152994}, {"x": 3855, "y": -50, "length": 3855.324240579513}, {"x": 3905, "y": -90, "length": 3906.0369941924514}, {"x": 3945, "y": -145, "length": 3947.663866136528}, {"x": 4055, "y": -360, "length": 4070.9489065818548}, {"x": 4090, "y": -410, "length": 4110.498753192853}, {"x": 4125, "y": -445, "length": 4148.933597926099}, {"x": 4170, "y": -475, "length": 4196.966166172894}, {"x": 4220, "y": -490, "length": 4248.352621899458}, {"x": 4260, "y": -490, "length": 4288.088152078966}, {"x": 4305, "y": -480, "length": 4331.676927011063}], "wireframe": false, "x": -890, "y": 410, "directed": true, "isRoad": true, "camera": false, "lineId": 0, "thick": 128, "physic": true, "cameraOffsetY": 30, "width": 5610.4, "rotation": 0, "textureMode": true, "height": 573, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "smoothing": true, "direction": 90, "repeatTexture": true, "textureOffset": 0, "line": true}, "className": "frg.game.editor.objects::GroundPather"}, {"params": {"rotation": 0, "x": 12280, "height": 1582, "y": -514, "width": 2312}, "className": "FinishZone"}, {"params": {"shape": true, "snapToGrid": false, "vertices": [{"x": 6439, "y": -360, "length": 6449.055822366558}, {"x": 6484, "y": -370, "length": 6494.548175200489}, {"x": 6533, "y": -366, "length": 6543.244225917293}, {"x": 6568, "y": -354, "length": 6577.532972171253}, {"x": 7032, "y": -92, "length": 7032.601794499671}, {"x": 7098, "y": -61, "length": 7098.262111249485}, {"x": 7161, "y": -37, "length": 7161.0955865705355}, {"x": 7225, "y": -17, "length": 7225.019999972318}, {"x": 7279, "y": -5, "length": 7279.001717268653}, {"x": 7341, "y": 6, "length": 7341.00245198161}, {"x": 7405, "y": 14, "length": 7405.013234289322}, {"x": 7480, "y": 18, "length": 7480.0216577226565}, {"x": 7552, "y": 19, "length": 7552.023900915568}, {"x": 7613, "y": 17, "length": 7613.018980667262}, {"x": 7682, "y": 11, "length": 7682.007875549204}, {"x": 7740, "y": 3, "length": 7740.000581395327}, {"x": 7816, "y": -12, "length": 7816.009211867652}, {"x": 7882, "y": -31, "length": 7882.060961449106}, {"x": 7957, "y": -59, "length": 7957.218735211443}, {"x": 8017, "y": -88, "length": 8017.482959133746}, {"x": 8085, "y": -128, "length": 8086.0131709019615}, {"x": 8201, "y": -220, "length": 8203.950328957386}, {"x": 8255, "y": -250, "length": 8258.784716893884}, {"x": 8295, "y": -255, "length": 8298.918604252001}, {"x": 8330, "y": -250, "length": 8333.750656217164}, {"x": 8370, "y": -225, "length": 8373.023647404802}, {"x": 8400, "y": -195, "length": 8402.263088001946}, {"x": 8425, "y": -135, "length": 8426.081532954686}, {"x": 8795, "y": 1425, "length": 8909.694158611732}, {"x": 10495, "y": 1400, "length": 10587.966046413258}, {"x": 11025, "y": -85, "length": 11025.327659530123}, {"x": 11065, "y": -145, "length": 11065.95002699723}, {"x": 11094, "y": -153, "length": 11095.054979584373}, {"x": 11250, "y": -170, "length": 11251.28437112848}, {"x": 11360, "y": -185, "length": 11361.506282179313}, {"x": 11480, "y": -205, "length": 11481.830211251166}, {"x": 11585, "y": -230, "length": 11587.282899800108}, {"x": 11710, "y": -270, "length": 11713.11231056887}, {"x": 12130, "y": -440, "length": 12137.97759101573}, {"x": 12145, "y": -345, "length": 12149.899176536404}, {"x": 12155, "y": -295, "length": 12158.579275556827}, {"x": 12175, "y": -195, "length": 12176.561501507722}, {"x": 12190, "y": -145, "length": 12190.862356699792}, {"x": 12220, "y": -60, "length": 12220.147298621241}, {"x": 12260, "y": -20, "length": 12260.01631320285}, {"x": 21265, "y": 0, "length": 21265}], "wireframe": false, "x": -3140, "y": 290, "directed": true, "isRoad": true, "camera": false, "lineId": 0, "thick": 128, "physic": true, "cameraOffsetY": 30, "width": 14841.4, "rotation": 0, "textureMode": true, "height": 1963.85, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "smoothing": true, "direction": 90, "repeatTexture": true, "textureOffset": 0, "line": true}, "className": "frg.game.editor.objects::GroundPather"}], [{"params": {"x": 1750, "y": -340, "scaleX": 1, "width": 9.3, "rotation": 0, "scaleY": 1, "height": 128.35}, "className": "SignPillar"}], [{"params": {"x": 166, "y": 192, "width": 53.2, "rotation": 93, "height": 53.2, "stones": false, "count": 30, "impulse": 9000, "id": 1, "safeId": -1, "physic": false, "radius": 64}, "className": "Tnt1"}, {"params": {"x": 180, "y": 247, "width": 53.2, "rotation": 3, "height": 53.2, "stones": false, "count": 30, "impulse": 9000, "id": 1, "safeId": -1, "physic": false, "radius": 64}, "className": "Tnt1"}, {"params": {"x": 166, "y": 132, "width": 53.2, "rotation": 93, "height": 53.2, "stones": false, "count": 30, "impulse": 9000, "id": 1, "safeId": -1, "physic": false, "radius": 64}, "className": "Tnt1"}, {"params": {"x": 5420, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 5480, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 5600, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 5540, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 5780, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 5720, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 5840, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 5660, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 6020, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 6080, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 5900, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 6320, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 6200, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 5960, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 6260, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 6140, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 6380, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 6500, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 6440, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 6560, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 7270, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 7330, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 7750, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 6730, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 6790, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 7210, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 7690, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 7450, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 7630, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 7150, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 7390, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 6610, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 7030, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 7570, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 6910, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 6670, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 6970, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 7090, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 7510, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 6850, "id": -1, "safeId": -1, "width": 73, "rotation": 0, "active": true, "height": 20.5, "y": 160}, "className": "Plank0"}, {"params": {"x": 10080, "id": -1, "safeId": -1, "width": 73, "rotation": -90, "active": true, "height": 20.5, "y": -160}, "className": "Plank0"}, {"params": {"x": 10080, "id": -1, "safeId": -1, "width": 73, "rotation": -90, "active": true, "height": 20.5, "y": -100}, "className": "Plank0"}, {"params": {"x": 10080, "id": -1, "safeId": -1, "width": 73, "rotation": -90, "active": true, "height": 20.5, "y": -40}, "className": "Plank0"}], null, [{"params": {"rotation": 0, "x": 190, "height": 32, "y": -490, "width": 64}, "className": "PlayerWP"}, {"params": {"x": 1750, "id": 0, "y": -410, "width": 128, "rotation": 0, "height": 2833}, "className": "SafePointE"}], [{"params": {"fnl": false, "id": 1, "x": 280, "safeId": -1, "sndId": -1, "id_off": -1, "rotation": 0, "off": false, "height": 64, "y": -330, "width": 594}, "className": "ToggleE"}, {"params": {"x": 1100, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": -210}, "className": "PivotJointE"}, {"params": {"fnl": false, "id": 0, "x": 1750, "safeId": -1, "sndId": -1, "id_off": -1, "rotation": 0, "off": false, "height": 1640, "y": -400, "width": 276}, "className": "ToggleE"}, {"params": {"x": 5390, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 5450, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 5510, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 5570, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 5810, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 5690, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 5750, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 5630, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 6110, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 6050, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 6230, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 5990, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 6170, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 5930, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 6290, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 5870, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 6530, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 6470, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 6410, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 6350, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 7060, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 6820, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 6760, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 7480, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 6940, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 7600, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 7420, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 7120, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 6700, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 7660, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 6880, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 7720, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 7240, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 7300, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 6640, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 7000, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 7540, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 7360, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 6580, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 7180, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 7780, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 160}, "className": "PivotJointE"}, {"params": {"x": 9693, "y": 186, "width": 17, "rotation": 0, "graphic": 1, "height": 17, "time": 15, "snd": false, "id": 99, "safeId": -1, "useWeld": false, "rate": 600}, "className": "MotorJointE"}, {"params": {"x": 10205, "y": 186, "width": 17, "rotation": 0, "graphic": 1, "height": 17, "time": 15, "snd": false, "id": 99, "safeId": -1, "useWeld": false, "rate": 600}, "className": "MotorJointE"}, {"params": {"fnl": false, "id": 99, "x": 9900, "safeId": -1, "sndId": -1, "id_off": -1, "rotation": -18, "off": false, "height": 64, "y": -80, "width": 614}, "className": "ToggleE"}, {"params": {"x": 10080, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": -10}, "className": "PivotJointE"}, {"params": {"x": 10080, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": -70}, "className": "PivotJointE"}, {"params": {"x": 10080, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": -130}, "className": "PivotJointE"}, {"params": {"x": 10080, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": -190}, "className": "PivotJointE"}, {"params": {"fnl": false, "id": 21, "x": 2530, "safeId": -1, "sndId": 0, "id_off": -1, "rotation": 0, "off": false, "height": 1434, "y": -460, "width": 64}, "className": "ToggleE"}, {"params": {"fnl": false, "id": 22, "x": 6470, "safeId": -1, "sndId": 0, "id_off": -1, "rotation": 0, "off": false, "height": 1434, "y": -280, "width": 64}, "className": "ToggleE"}]], "settings": {"prizes": "19,25,32", "theme": 2, "countdown": 0, "gravityY": 500}}