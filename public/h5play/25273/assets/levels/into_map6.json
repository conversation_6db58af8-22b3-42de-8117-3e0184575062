{"layers": [[{"params": {"snapToGrid": true, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 0, "y": 720, "length": 720}], "wireframe": false, "width": 13, "rotation": 0, "textureMode": true, "height": 733, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 0, "smoothing": true, "y": 410, "repeatTexture": true, "textureOffset": 0, "x": 2840}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"snapToGrid": true, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 0, "y": 720, "length": 720}], "wireframe": false, "width": 13, "rotation": 0, "textureMode": true, "height": 733, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 0, "smoothing": true, "y": 410, "repeatTexture": true, "textureOffset": 0, "x": 3000}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"snapToGrid": true, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 0, "y": 790, "length": 790}], "wireframe": false, "width": 13, "rotation": 0, "textureMode": true, "height": 803, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 0, "smoothing": true, "y": 340, "repeatTexture": true, "textureOffset": 0, "x": 4250}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"snapToGrid": true, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 0, "y": 790, "length": 790}], "wireframe": false, "width": 13, "rotation": 0, "textureMode": true, "height": 803, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 0, "smoothing": true, "y": 340, "repeatTexture": true, "textureOffset": 0, "x": 3700}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"snapToGrid": true, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 0, "y": 790, "length": 790}], "wireframe": false, "width": 13, "rotation": 0, "textureMode": true, "height": 803, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 0, "smoothing": true, "y": 340, "repeatTexture": true, "textureOffset": 0, "x": 3430}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"snapToGrid": true, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 0, "y": 790, "length": 790}], "wireframe": false, "width": 13, "rotation": 0, "textureMode": true, "height": 803, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 0, "smoothing": true, "y": 340, "repeatTexture": true, "textureOffset": 0, "x": 3990}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"snapToGrid": true, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 0, "y": 790, "length": 790}], "wireframe": false, "width": 13, "rotation": 0, "textureMode": true, "height": 803, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 0, "smoothing": true, "y": 390, "repeatTexture": true, "textureOffset": 0, "x": 4570}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"snapToGrid": true, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 0, "y": 720, "length": 720}], "wireframe": false, "width": 13, "rotation": 0, "textureMode": true, "height": 733, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 0, "smoothing": true, "y": 410, "repeatTexture": true, "textureOffset": 0, "x": 2680}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"snapToGrid": true, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 0, "y": 630, "length": 630}], "wireframe": false, "width": 13, "rotation": 0, "textureMode": true, "height": 643, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 0, "smoothing": true, "y": 1580, "repeatTexture": true, "textureOffset": 0, "x": 11860}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"snapToGrid": true, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 0, "y": 790, "length": 790}], "wireframe": false, "width": 13, "rotation": 0, "textureMode": true, "height": 803, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 0, "smoothing": true, "y": 410, "repeatTexture": true, "textureOffset": 0, "x": 4970}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"snapToGrid": true, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 0, "y": 790, "length": 790}], "wireframe": false, "width": 13, "rotation": 0, "textureMode": true, "height": 803, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 0, "smoothing": true, "y": 400, "repeatTexture": true, "textureOffset": 0, "x": 4760}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"snapToGrid": true, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 0, "y": 790, "length": 790}], "wireframe": false, "width": 13, "rotation": 0, "textureMode": true, "height": 803, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 0, "smoothing": true, "y": 410, "repeatTexture": true, "textureOffset": 0, "x": 5160}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"snapToGrid": true, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 0, "y": 720, "length": 720}], "wireframe": false, "width": 13, "rotation": 0, "textureMode": true, "height": 733, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 0, "smoothing": true, "y": 400, "repeatTexture": true, "textureOffset": 0, "x": 2530}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"snapToGrid": true, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 0, "y": 630, "length": 630}], "wireframe": false, "width": 13, "rotation": 0, "textureMode": true, "height": 643, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 0, "smoothing": true, "y": 1580, "repeatTexture": true, "textureOffset": 0, "x": 11020}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"x": 9370, "vertices": [{"x": -45, "y": -20, "length": 49.24428900898052}, {"x": 180, "y": -20, "length": 181.10770276274835}, {"x": 190, "y": 670, "length": 696.419413859206}, {"x": -35, "y": 670, "length": 670.9135562797937}], "y": 1520, "rem": true, "isStatic": true, "rotation": 0, "graphic": true, "plr": false, "ignore": false, "id": -1, "density": 1, "safeId": -1, "line": true, "isWheel": false, "layer": 0, "physic": true}, "className": "frg.game.editor.objects::LandscapeShaper"}, {"params": {"snapToGrid": true, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 0, "y": 630, "length": 630}], "wireframe": false, "width": 13, "rotation": 0, "textureMode": true, "height": 643, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 0, "smoothing": true, "y": 1580, "repeatTexture": true, "textureOffset": 0, "x": 11300}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"snapToGrid": true, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 0, "y": 630, "length": 630}], "wireframe": false, "width": 13, "rotation": 0, "textureMode": true, "height": 643, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 0, "smoothing": true, "y": 1580, "repeatTexture": true, "textureOffset": 0, "x": 11590}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"x": 13845, "vertices": [{"x": 145, "y": 0, "length": 145}, {"x": 174, "y": 12, "length": 174.41330224498358}, {"x": 223, "y": 105, "length": 246.48326515201796}, {"x": 123, "y": 73, "length": 143.0314650697531}, {"x": 89, "y": 113, "length": 143.8401890988746}, {"x": 94, "y": 145, "length": 172.80335644888382}, {"x": 52, "y": 240, "length": 245.56872765073325}, {"x": 17, "y": 142, "length": 143.01398533010678}, {"x": -29, "y": 140, "length": 142.97202523570826}, {"x": -52, "y": 165, "length": 173}, {"x": -153, "y": 192, "length": 245.50560075077718}, {"x": -99, "y": 104, "length": 143.58621103713267}, {"x": -130, "y": 60, "length": 143.17821063276352}, {"x": -164, "y": 59, "length": 174.28998823799375}, {"x": -250, "y": 0, "length": 250}, {"x": -144, "y": -10, "length": 144.34680460612904}, {"x": -130, "y": -60, "length": 143.17821063276352}, {"x": -149, "y": -88, "length": 173.04623659588788}, {"x": -153, "y": -192, "length": 245.50560075077718}, {"x": -78, "y": -120, "length": 143.12232530251876}, {"x": -29, "y": -140, "length": 142.97202523570826}, {"x": -20, "y": -171, "length": 172.16561793807728}, {"x": 52, "y": -240, "length": 245.56872765073325}, {"x": 42, "y": -138, "length": 144.2497833620557}, {"x": 89, "y": -113, "length": 143.8401890988746}, {"x": 118, "y": -124, "length": 171.1724276862369}, {"x": 223, "y": -105, "length": 246.48326515201796}, {"x": 134, "y": -50, "length": 143.024473430249}], "y": 1642, "rem": true, "isStatic": false, "rotation": 0, "graphic": true, "plr": true, "ignore": false, "id": -1, "density": 1, "safeId": -1, "line": true, "isWheel": false, "layer": 3, "physic": true}, "className": "frg.game.editor.objects::LandscapeShaper"}, {"params": {"snapToGrid": true, "vertices": [{"x": -430, "y": 0, "length": 430}, {"x": 55, "y": 0, "length": 55}, {"x": 90, "y": 15, "length": 91.2414379544733}, {"x": 90, "y": 635, "length": 641.346240341362}], "wireframe": false, "width": 535.5, "rotation": 0, "textureMode": true, "height": 650.5, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "type": 2, "smoothing": true, "y": 1634, "repeatTexture": true, "textureOffset": 0, "x": 13770}, "className": "frg.game.editor.objects::<PERSON><PERSON><PERSON><PERSON>"}, {"params": {"x": 3500, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 100, "y": 0, "length": 100}, {"x": 100, "y": 165, "length": 192.93781381574738}, {"x": 185, "y": 165, "length": 247.8911051248108}, {"x": 185, "y": 0, "length": 185}, {"x": 495, "y": 0, "length": 495}, {"x": 495, "y": 165, "length": 521.7758139277826}, {"x": 590, "y": 165, "length": 612.6377396145294}, {"x": 590, "y": 0, "length": 590}, {"x": 680, "y": 0, "length": 680}, {"x": 690, "y": 330, "length": 764.8529270389178}, {"x": 330, "y": 330, "length": 466.6904755831214}, {"x": 330, "y": 165, "length": 368.9512162874653}, {"x": 235, "y": 165, "length": 287.14108030722457}, {"x": 235, "y": 330, "length": 405.123437979093}, {"x": 0, "y": 330, "length": 330}], "y": 0, "rem": true, "isStatic": true, "rotation": 0, "graphic": true, "plr": false, "ignore": false, "id": -1, "density": 1, "safeId": -1, "line": true, "isWheel": false, "layer": 6, "physic": true}, "className": "frg.game.editor.objects::LandscapeShaper"}, {"params": {"x": 13230, "vertices": [{"x": -45, "y": -20, "length": 49.24428900898052}, {"x": 180, "y": -20, "length": 181.10770276274835}, {"x": 180, "y": 1025, "length": 1040.6848706500925}, {"x": -45, "y": 1025, "length": 1025.9873293564594}], "y": 1220, "rem": true, "isStatic": true, "rotation": 0, "graphic": true, "plr": false, "ignore": false, "id": -1, "density": 1, "safeId": -1, "line": true, "isWheel": false, "layer": 0, "physic": true}, "className": "frg.game.editor.objects::LandscapeShaper"}, {"params": {"rotation": 0, "x": 15760, "height": 2676, "y": 290, "width": 2300}, "className": "FinishZone"}, {"params": {"bg": true, "x": 2130, "id": -1, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 3680, "y": 0, "length": 3680}, {"x": 3700, "y": 755, "length": 3776.2448278680235}, {"x": 20, "y": 755, "length": 755.2648542067875}], "y": 570, "viscosity": 1.5, "rotation": 0, "density": 1.5, "sf": true}, "className": "frg.game.editor.objects::WaterShaper"}, {"params": {"bg": true, "x": 9120, "id": -1, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 5060, "y": 0, "length": 5060}, {"x": 5070, "y": 850, "length": 5140.758698869263}, {"x": 10, "y": 850, "length": 850.0588214941364}], "y": 1660, "viscosity": 1.5, "rotation": 0, "density": 1.5, "sf": true}, "className": "frg.game.editor.objects::WaterShaper"}, {"params": {"x": -130, "vertices": [{"x": -630, "y": -1215, "length": 1368.6215693170993}, {"x": 105, "y": -1220, "length": 1224.5101061240778}, {"x": 100, "y": 150, "length": 180.27756377319946}, {"x": -635, "y": 155, "length": 653.64363379444}], "y": 310, "rem": true, "isStatic": true, "rotation": 0, "graphic": true, "plr": false, "ignore": false, "id": -1, "density": 1, "safeId": -1, "line": true, "isWheel": false, "layer": 0, "physic": true}, "className": "frg.game.editor.objects::LandscapeShaper"}, {"params": {"x": 2520, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 65, "y": 0, "length": 65}, {"x": 65, "y": 150, "length": 163.47782724271815}, {"x": 125, "y": 150, "length": 195.25624189766637}, {"x": 125, "y": 0, "length": 125}, {"x": 470, "y": 0, "length": 470}, {"x": 480, "y": 360, "length": 600}, {"x": 315, "y": 360, "length": 478.3565615730592}, {"x": 315, "y": 190, "length": 367.8654645383282}, {"x": 225, "y": 190, "length": 294.49108645254444}, {"x": 225, "y": 360, "length": 424.5291509425472}, {"x": 0, "y": 360, "length": 360}], "y": 20, "rem": true, "isStatic": true, "rotation": 0, "graphic": true, "plr": false, "ignore": false, "id": -1, "density": 1, "safeId": -1, "line": true, "isWheel": false, "layer": 6, "physic": true}, "className": "frg.game.editor.objects::LandscapeShaper"}, {"params": {"shape": true, "snapToGrid": true, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 1400, "y": 0, "length": 1400}, {"x": 2021, "y": -10, "length": 2021.0247400761823}, {"x": 2103, "y": -13, "length": 2103.0401803104}, {"x": 2188, "y": -19, "length": 2188.082493874488}, {"x": 2280, "y": -31, "length": 2280.210735875086}, {"x": 2362, "y": -46, "length": 2362.447883023031}, {"x": 2408, "y": -57, "length": 2408.6745317705336}, {"x": 2475, "y": -76, "length": 2476.1665937492976}, {"x": 2538, "y": -98, "length": 2539.8913362583053}, {"x": 2621, "y": -134, "length": 2624.4231747185895}, {"x": 2682, "y": -170, "length": 2687.3823695187107}, {"x": 2719, "y": -196, "length": 2726.055208538521}, {"x": 2749, "y": -223, "length": 2758.030094107024}, {"x": 2784, "y": -206, "length": 2791.6110044202073}, {"x": 2809, "y": -163, "length": 2813.725288652039}, {"x": 2886, "y": 166, "length": 2890.7701395994804}, {"x": 2968, "y": 668, "length": 3042.2439086963427}, {"x": 5851, "y": 667, "length": 5888.895482176603}, {"x": 6309, "y": 106, "length": 6309.8904110927315}, {"x": 6346, "y": 72, "length": 6346.408433121839}, {"x": 6386, "y": 45, "length": 6386.158547984853}, {"x": 7180, "y": 41, "length": 7180.117060327081}, {"x": 7483, "y": 37, "length": 7483.091473448658}, {"x": 7541, "y": 33, "length": 7541.072204932134}, {"x": 7584, "y": 28, "length": 7584.051687587578}, {"x": 7630, "y": 20, "length": 7630.026212274765}, {"x": 7681, "y": 6, "length": 7681.002343444507}, {"x": 7729, "y": -12, "length": 7729.009315559142}, {"x": 7784, "y": -38, "length": 7784.092753815309}, {"x": 7830, "y": -67, "length": 7830.286648648311}, {"x": 7870, "y": -97, "length": 7870.59775366522}, {"x": 7902, "y": -127, "length": 7903.020498518272}, {"x": 7928, "y": -156, "length": 7929.5346647833}, {"x": 7949, "y": -188, "length": 7951.222861925076}, {"x": 7969, "y": -227, "length": 7972.232435146381}, {"x": 8020, "y": -333, "length": 8026.910302227127}, {"x": 8055, "y": -319, "length": 8061.314160854916}, {"x": 8081, "y": -298, "length": 8086.492750259534}, {"x": 9139, "y": 596, "length": 9158.413454305282}, {"x": 9174, "y": 625, "length": 9195.265140277359}, {"x": 9211, "y": 651, "length": 9233.976499861801}, {"x": 9243, "y": 672, "length": 9267.396236268309}, {"x": 9271, "y": 687, "length": 9296.419203112562}, {"x": 9306, "y": 705, "length": 9332.666339262323}, {"x": 9355, "y": 725, "length": 9383.051209494703}, {"x": 9405, "y": 741, "length": 9434.14574829115}, {"x": 9467, "y": 755, "length": 9497.058176088003}, {"x": 9516, "y": 762, "length": 9546.46007690809}, {"x": 9563, "y": 766, "length": 9593.629396636083}, {"x": 9604, "y": 767, "length": 9634.578610401184}, {"x": 9653, "y": 765, "length": 9683.265668151422}, {"x": 9685, "y": 784, "length": 9716.680554592705}, {"x": 9702, "y": 815, "length": 9736.171167353212}, {"x": 10083, "y": 1738, "length": 10231.6925774771}, {"x": 14185, "y": 1762, "length": 14294.01514620717}, {"x": 14402, "y": 1718, "length": 14504.107280353383}, {"x": 14539, "y": 1684, "length": 14636.20090733931}, {"x": 14678, "y": 1614, "length": 14766.47148102755}, {"x": 14767, "y": 1481, "length": 14841.079812466476}, {"x": 14771, "y": 1239, "length": 14822.872933409366}, {"x": 14795, "y": 1189, "length": 14842.700091290668}, {"x": 14832, "y": 1153, "length": 14876.74806535353}, {"x": 17975, "y": 1135, "length": 18010.798149998795}, {"x": 22720, "y": -25, "length": 22720.013754397245}], "wireframe": false, "x": -680, "y": 450, "directed": true, "isRoad": true, "camera": false, "lineId": 0, "thick": 128, "physic": true, "cameraOffsetY": 170, "width": 22734.55, "rotation": 0, "textureMode": true, "height": 2172.35, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "smoothing": true, "direction": 90, "repeatTexture": true, "textureOffset": 0, "line": true}, "className": "frg.game.editor.objects::GroundPather"}, {"params": {"shape": false, "snapToGrid": false, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 1400, "y": -5, "length": 1400.0089285429576}, {"x": 2021, "y": -10, "length": 2021.0247400761823}, {"x": 2103, "y": -13, "length": 2103.0401803104}, {"x": 2188, "y": -19, "length": 2188.082493874488}, {"x": 2280, "y": -31, "length": 2280.210735875086}, {"x": 2362, "y": -46, "length": 2362.447883023031}, {"x": 2408, "y": -57, "length": 2408.6745317705336}, {"x": 2475, "y": -76, "length": 2476.1665937492976}, {"x": 2538, "y": -98, "length": 2539.8913362583053}, {"x": 2621, "y": -134, "length": 2624.4231747185895}, {"x": 2682, "y": -170, "length": 2687.3823695187107}, {"x": 2740, "y": -176, "length": 2745.64673619896}, {"x": 2779, "y": -151, "length": 2783.0993514425604}, {"x": 2804, "y": -106, "length": 2806.0028510320512}, {"x": 2847, "y": -15, "length": 2847.0395150050167}, {"x": 2922, "y": 32, "length": 2922.175217196943}, {"x": 3003, "y": 65, "length": 3003.703380828407}, {"x": 3101, "y": 87, "length": 3102.2201727150186}, {"x": 6372, "y": 109, "length": 6372.932213667426}, {"x": 7540, "y": 61, "length": 7540.246746625737}, {"x": 7825, "y": 145, "length": 7826.3433351725635}, {"x": 8157, "y": 185, "length": 8159.097621673613}, {"x": 8738, "y": 272, "length": 8742.23243799889}, {"x": 9062, "y": 539, "length": 9078.015476964114}, {"x": 9230, "y": 672, "length": 9254.430506519566}, {"x": 9427, "y": 750, "length": 9456.787456636635}, {"x": 9646, "y": 769, "length": 9676.604621456847}, {"x": 9859, "y": 1195, "length": 9931.158341301381}, {"x": 15019, "y": 1186, "length": 15065.754445098328}, {"x": 15110, "y": 1157, "length": 15154.232049166993}, {"x": 15493, "y": 1150, "length": 15535.621937984974}, {"x": 18004, "y": 1159, "length": 18041.266502105667}, {"x": 22720, "y": -25, "length": 22720.013754397245}], "wireframe": false, "x": -690, "y": 460, "directed": false, "isRoad": false, "camera": true, "lineId": 0, "thick": 128, "physic": false, "cameraOffsetY": 170, "width": 22734.8, "rotation": 0, "textureMode": true, "height": 1440.1, "straightSides": true, "originOffsetRatio": 0, "stretchTexture": false, "smoothing": true, "direction": 90, "repeatTexture": true, "textureOffset": 0, "line": false}, "className": "frg.game.editor.objects::GroundPather"}, {"params": {"x": 4630, "vertices": [{"x": -60, "y": 0, "length": 60}, {"x": 65, "y": 0, "length": 65}, {"x": 65, "y": 150, "length": 163.47782724271815}, {"x": 125, "y": 150, "length": 195.25624189766637}, {"x": 125, "y": 0, "length": 125}, {"x": 400, "y": 0, "length": 400}, {"x": 400, "y": 150, "length": 427.20018726587654}, {"x": 460, "y": 150, "length": 483.83881613611777}, {"x": 460, "y": 0, "length": 460}, {"x": 525, "y": 0, "length": 525}, {"x": 530, "y": 245, "length": 583.8878316937253}, {"x": 315, "y": 245, "length": 399.06139878469827}, {"x": 315, "y": 75, "length": 323.80549717384355}, {"x": 225, "y": 75, "length": 237.17082451262846}, {"x": 225, "y": 245, "length": 332.6409475695979}, {"x": -60, "y": 245, "length": 252.23996511258878}], "y": 140, "rem": true, "isStatic": true, "rotation": 0, "graphic": true, "plr": false, "ignore": false, "id": -1, "density": 1, "safeId": -1, "line": true, "isWheel": false, "layer": 6, "physic": true}, "className": "frg.game.editor.objects::LandscapeShaper"}, {"params": {"x": 2690, "vertices": [{"x": 65, "y": -80, "length": 103.07764064044152}, {"x": 355, "y": 105, "length": 370.20264720825537}, {"x": -235, "y": 105, "length": 257.390753524675}], "y": -50, "rem": true, "isStatic": true, "rotation": 0, "graphic": true, "plr": false, "ignore": false, "id": -1, "density": 1, "safeId": -1, "line": true, "isWheel": false, "layer": 0, "physic": true}, "className": "frg.game.editor.objects::LandscapeShaper"}, {"params": {"x": 2480, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 555, "y": 0, "length": 555}, {"x": 555, "y": 65, "length": 558.7933428379404}, {"x": 0, "y": 65, "length": 65}], "y": 350, "rem": true, "isStatic": true, "rotation": 0, "graphic": true, "plr": false, "ignore": false, "id": -1, "density": 1, "safeId": -1, "line": true, "isWheel": false, "layer": 0, "physic": true}, "className": "frg.game.editor.objects::LandscapeShaper"}, {"params": {"x": 3320, "vertices": [{"x": 515, "y": -85, "length": 521.9674319342156}, {"x": 915, "y": 95, "length": 919.9184746487049}, {"x": 105, "y": 100, "length": 145}], "y": -80, "rem": true, "isStatic": true, "rotation": 0, "graphic": true, "plr": false, "ignore": false, "id": -1, "density": 1, "safeId": -1, "line": true, "isWheel": false, "layer": 0, "physic": true}, "className": "frg.game.editor.objects::LandscapeShaper"}, {"params": {"x": 3380, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 920, "y": 0, "length": 920}, {"x": 920, "y": 65, "length": 922.2933372848358}, {"x": 0, "y": 65, "length": 65}], "y": 290, "rem": true, "isStatic": true, "rotation": 0, "graphic": true, "plr": false, "ignore": false, "id": -1, "density": 1, "safeId": -1, "line": true, "isWheel": false, "layer": 0, "physic": true}, "className": "frg.game.editor.objects::LandscapeShaper"}, {"params": {"x": 4520, "vertices": [{"x": 5, "y": 55, "length": 55.226805085936306}, {"x": 115, "y": -25, "length": 117.6860229593982}, {"x": 570, "y": -25, "length": 570.5479822065801}, {"x": 680, "y": 55, "length": 682.22063879657}, {"x": 680, "y": 95, "length": 686.6039615382364}, {"x": 5, "y": 95, "length": 95.13148795220224}], "y": 90, "rem": true, "isStatic": true, "rotation": 0, "graphic": true, "plr": false, "ignore": false, "id": -1, "density": 1, "safeId": -1, "line": true, "isWheel": false, "layer": 0, "physic": true}, "className": "frg.game.editor.objects::LandscapeShaper"}, {"params": {"x": 7130, "vertices": [{"x": 2, "y": 0, "length": 2}, {"x": 3, "y": -1085, "length": 1085.004147457511}], "wireframe": false, "textureOffset": 0, "width": 14, "rotation": 0, "snapToGrid": false, "height": 1098, "action": 0, "y": -420, "stretchTexture": false, "type": 3, "isStatic": true, "textureMode": true, "id": -1, "density": "1", "safeId": -1, "shapeH": 6, "originOffsetRatio": 0, "repeatTexture": true, "graphic": true, "physic": true, "smoothing": true, "straightSides": true}, "className": "frg.game.editor.objects::DynamicPather"}, {"params": {"x": 4520, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 680, "y": 0, "length": 680}, {"x": 680, "y": 70, "length": 683.593446428504}, {"x": 0, "y": 70, "length": 70}], "y": 350, "rem": true, "isStatic": true, "rotation": 0, "graphic": true, "plr": false, "ignore": false, "id": -1, "density": 1, "safeId": -1, "line": true, "isWheel": false, "layer": 0, "physic": true}, "className": "frg.game.editor.objects::LandscapeShaper"}, {"params": {"x": 7582, "vertices": [{"x": 2, "y": 0, "length": 2}, {"x": 3, "y": -1085, "length": 1085.004147457511}], "wireframe": false, "textureOffset": 0, "width": 14, "rotation": 0, "snapToGrid": false, "height": 1098, "action": 0, "y": -410, "stretchTexture": false, "type": 3, "isStatic": true, "textureMode": true, "id": -1, "density": "1", "safeId": -1, "shapeH": 6, "originOffsetRatio": 0, "repeatTexture": true, "graphic": true, "physic": true, "smoothing": true, "straightSides": true}, "className": "frg.game.editor.objects::DynamicPather"}, {"params": {"x": 11030, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 115, "y": 0, "length": 115}, {"x": 115, "y": 100, "length": 152.3975065412817}, {"x": 200, "y": 100, "length": 223.60679774997897}, {"x": 200, "y": 0, "length": 200}, {"x": 505, "y": 10, "length": 505.0990001969911}, {"x": 505, "y": 105, "length": 515.8003489723519}, {"x": 585, "y": 105, "length": 594.3483826847685}, {"x": 585, "y": 10, "length": 585.0854638426766}, {"x": 665, "y": 10, "length": 665.0751837198559}, {"x": 665, "y": 105, "length": 673.238442158497}, {"x": 735, "y": 105, "length": 742.4621202458749}, {"x": 735, "y": 10, "length": 735.0680240630795}, {"x": 845, "y": 0, "length": 845}, {"x": 845, "y": 205, "length": 869.5113570276123}, {"x": 405, "y": 205, "length": 453.9273069556402}, {"x": 405, "y": 80, "length": 412.82562904936026}, {"x": 315, "y": 80, "length": 325}, {"x": 315, "y": 205, "length": 375.8324094593227}, {"x": 0, "y": 205, "length": 205}], "y": 1340, "rem": true, "isStatic": true, "rotation": 0, "graphic": true, "plr": false, "ignore": false, "id": -1, "density": 1, "safeId": -1, "line": true, "isWheel": false, "layer": 6, "physic": true}, "className": "frg.game.editor.objects::LandscapeShaper"}, {"params": {"x": 7193, "vertices": [{"x": 52, "y": 54, "length": 74.96665925596525}, {"x": -12, "y": -25, "length": 27.730849247724095}, {"x": -24, "y": -43, "length": 49.24428900898052}, {"x": -32, "y": -58, "length": 66.24198064671678}, {"x": -39, "y": -75, "length": 84.53401682163222}, {"x": -45, "y": -92, "length": 102.41581909060729}, {"x": -49, "y": -108, "length": 118.59595271340417}, {"x": -52, "y": -124, "length": 134.46189051177288}, {"x": -54, "y": -145, "length": 154.72879499304582}, {"x": -54, "y": -162, "length": 170.7629936490925}, {"x": -54, "y": -176, "length": 184.09780009549272}, {"x": -52, "y": -197, "length": 203.74739262135355}, {"x": -48, "y": -218, "length": 223.22186272854188}, {"x": -43, "y": -235, "length": 238.9016534057477}, {"x": -38, "y": -249, "length": 251.88290930509754}, {"x": -32, "y": -262, "length": 263.9469643697385}, {"x": -25, "y": -275, "length": 276.1340254296815}, {"x": -16, "y": -289, "length": 289.44256770558127}, {"x": -6, "y": -303, "length": 303.0594001181947}, {"x": 6, "y": -318, "length": 318.0565987367657}, {"x": 22, "y": -334, "length": 334.72376670920755}, {"x": 35, "y": -345, "length": 346.77081768799405}, {"x": 49, "y": -355, "length": 358.36573496917924}, {"x": 65, "y": -365, "length": 370.74249823833253}, {"x": 80, "y": -373, "length": 381.4826339428834}, {"x": 94, "y": -379, "length": 390.4830342025118}, {"x": 108, "y": -384, "length": 398.8984833262719}, {"x": 124, "y": -388, "length": 407.33278777923096}, {"x": 144, "y": -392, "length": 417.612260356422}, {"x": 160, "y": -394, "length": 425.2481628414166}, {"x": 179, "y": -394, "length": 432.75512706379345}, {"x": 198, "y": -394, "length": 440.95351228899403}, {"x": 215, "y": -392, "length": 447.0894765033058}, {"x": 234, "y": -389, "length": 453.957046426201}, {"x": 248, "y": -385, "length": 457.961788799022}, {"x": 262, "y": -380, "length": 461.5668965599678}, {"x": 278, "y": -374, "length": 466.00429182572987}, {"x": 293, "y": -366, "length": 468.83365920121395}, {"x": 310, "y": -356, "length": 472.05508153180597}, {"x": 328, "y": -342, "length": 473.86495966678103}, {"x": 346, "y": -325, "length": 474.70095849913764}, {"x": 361, "y": -309, "length": 475.1862792631959}, {"x": 373, "y": -293, "length": 474.31845842218706}, {"x": 380, "y": -283, "length": 473.8027015541385}, {"x": 386, "y": -272, "length": 472.2075814723859}, {"x": 392, "y": -261, "length": 470.9405482648526}, {"x": 397, "y": -250, "length": 469.1577559840613}, {"x": 402, "y": -236, "length": 466.1544808322666}, {"x": 407, "y": -218, "length": 461.7066168033549}, {"x": 411, "y": -199, "length": 456.64209179618996}, {"x": 413, "y": -183, "length": 451.7277941415604}, {"x": 414, "y": -167, "length": 446.4134854593889}, {"x": 414, "y": -152, "length": 441.02154142399894}, {"x": 413, "y": -133, "length": 433.8870820847286}, {"x": 410, "y": -114, "length": 425.55375688624815}, {"x": 406, "y": -96, "length": 417.1953978653168}, {"x": 400, "y": -79, "length": 407.72662410002124}, {"x": 395, "y": -66, "length": 400.47596681948346}, {"x": 385, "y": -46, "length": 387.7383138148718}, {"x": 376, "y": -31, "length": 377.27576121452597}, {"x": 364, "y": -14, "length": 364.2691312752153}, {"x": 275, "y": 93, "length": 290.29984498790213}], "wireframe": false, "textureOffset": 0, "width": 488, "rotation": -6, "snapToGrid": false, "height": 503.5, "action": 0, "y": -180, "stretchTexture": false, "type": 0, "isStatic": true, "textureMode": true, "id": -1, "density": "1", "safeId": -1, "shapeH": 20, "originOffsetRatio": 0, "repeatTexture": true, "graphic": true, "physic": true, "smoothing": true, "straightSides": true}, "className": "frg.game.editor.objects::DynamicPather"}, {"params": {"x": 10960, "vertices": [{"x": 500, "y": -55, "length": 503.0159043211258}, {"x": 975, "y": 110, "length": 981.1855074347562}, {"x": 995, "y": 135, "length": 1004.1165271023079}, {"x": -10, "y": 125, "length": 125.39936203984452}, {"x": 5, "y": 105, "length": 105.11898020814318}], "y": 1236, "rem": true, "isStatic": true, "rotation": 0, "graphic": true, "plr": false, "ignore": false, "id": -1, "density": 1, "safeId": -1, "line": true, "isWheel": false, "layer": 0, "physic": true}, "className": "frg.game.editor.objects::LandscapeShaper"}, {"params": {"x": 7355, "vertices": [{"x": 66, "y": 0, "length": 66}, {"x": 64, "y": 13, "length": 65.30696746902278}, {"x": 59, "y": 28, "length": 65.30696746902278}, {"x": 51, "y": 40, "length": 64.81512169239521}, {"x": 40, "y": 51, "length": 64.81512169239521}, {"x": 28, "y": 59, "length": 65.30696746902278}, {"x": 13, "y": 64, "length": 65.30696746902278}, {"x": 0, "y": 66, "length": 66}, {"x": -13, "y": 64, "length": 65.30696746902278}, {"x": -28, "y": 59, "length": 65.30696746902278}, {"x": -40, "y": 51, "length": 64.81512169239521}, {"x": -51, "y": 40, "length": 64.81512169239521}, {"x": -59, "y": 28, "length": 65.30696746902278}, {"x": -64, "y": 13, "length": 65.30696746902278}, {"x": -66, "y": 0, "length": 66}, {"x": -64, "y": -13, "length": 65.30696746902278}, {"x": -59, "y": -28, "length": 65.30696746902278}, {"x": -51, "y": -40, "length": 64.81512169239521}, {"x": -40, "y": -51, "length": 64.81512169239521}, {"x": -28, "y": -59, "length": 65.30696746902278}, {"x": -13, "y": -64, "length": 65.30696746902278}, {"x": 0, "y": -66, "length": 66}, {"x": 13, "y": -64, "length": 65.30696746902278}, {"x": 28, "y": -59, "length": 65.30696746902278}, {"x": 40, "y": -51, "length": 64.81512169239521}, {"x": 51, "y": -40, "length": 64.81512169239521}, {"x": 59, "y": -28, "length": 65.30696746902278}, {"x": 64, "y": -13, "length": 65.30696746902278}], "y": -358, "rem": true, "isStatic": true, "rotation": 0, "graphic": true, "plr": false, "ignore": false, "id": -1, "density": 1, "safeId": -1, "line": true, "isWheel": false, "layer": -1, "physic": false}, "className": "frg.game.editor.objects::LandscapeShaper"}, {"params": {"x": 10970, "vertices": [{"x": 0, "y": 0, "length": 0}, {"x": 975, "y": 0, "length": 975}, {"x": 975, "y": 60, "length": 976.8444093098962}, {"x": 0, "y": 60, "length": 60}], "y": 1530, "rem": true, "isStatic": true, "rotation": 0, "graphic": true, "plr": false, "ignore": false, "id": -1, "density": 1, "safeId": -1, "line": true, "isWheel": false, "layer": 0, "physic": true}, "className": "frg.game.editor.objects::LandscapeShaper"}, {"params": {"x": 7355, "vertices": [{"x": 30, "y": 0, "length": 30}, {"x": 29, "y": 6, "length": 29.614185789921695}, {"x": 27, "y": 13, "length": 29.966648127543394}, {"x": 23, "y": 18, "length": 29.206163733020468}, {"x": 18, "y": 23, "length": 29.206163733020468}, {"x": 13, "y": 27, "length": 29.966648127543394}, {"x": 6, "y": 29, "length": 29.614185789921695}, {"x": 0, "y": 30, "length": 30}, {"x": -6, "y": 29, "length": 29.614185789921695}, {"x": -13, "y": 27, "length": 29.966648127543394}, {"x": -18, "y": 23, "length": 29.206163733020468}, {"x": -23, "y": 18, "length": 29.206163733020468}, {"x": -27, "y": 13, "length": 29.966648127543394}, {"x": -29, "y": 6, "length": 29.614185789921695}, {"x": -30, "y": 0, "length": 30}, {"x": -29, "y": -6, "length": 29.614185789921695}, {"x": -27, "y": -13, "length": 29.966648127543394}, {"x": -23, "y": -18, "length": 29.206163733020468}, {"x": -18, "y": -23, "length": 29.206163733020468}, {"x": -10, "y": -30, "length": 31.622776601683793}, {"x": -10, "y": -85, "length": 85.58621384311844}, {"x": -30, "y": -80, "length": 85.44003745317531}, {"x": 0, "y": -115, "length": 115}, {"x": 30, "y": -80, "length": 85.44003745317531}, {"x": 10, "y": -85, "length": 85.58621384311844}, {"x": 10, "y": -30, "length": 31.622776601683793}, {"x": 18, "y": -23, "length": 29.206163733020468}, {"x": 23, "y": -18, "length": 29.206163733020468}, {"x": 27, "y": -13, "length": 29.966648127543394}, {"x": 29, "y": -6, "length": 29.614185789921695}], "y": -358, "rem": true, "isStatic": true, "rotation": -66, "graphic": true, "plr": false, "ignore": false, "id": -1, "density": 1, "safeId": -1, "line": true, "isWheel": false, "layer": 0, "physic": false}, "className": "frg.game.editor.objects::LandscapeShaper"}, {"params": {"x": 7355, "vertices": [{"x": 30, "y": 0, "length": 30}, {"x": 29, "y": 6, "length": 29.614185789921695}, {"x": 27, "y": 13, "length": 29.966648127543394}, {"x": 23, "y": 18, "length": 29.206163733020468}, {"x": 18, "y": 23, "length": 29.206163733020468}, {"x": 13, "y": 27, "length": 29.966648127543394}, {"x": 6, "y": 29, "length": 29.614185789921695}, {"x": 0, "y": 30, "length": 30}, {"x": -6, "y": 29, "length": 29.614185789921695}, {"x": -13, "y": 27, "length": 29.966648127543394}, {"x": -18, "y": 23, "length": 29.206163733020468}, {"x": -23, "y": 18, "length": 29.206163733020468}, {"x": -27, "y": 13, "length": 29.966648127543394}, {"x": -29, "y": 6, "length": 29.614185789921695}, {"x": -30, "y": 0, "length": 30}, {"x": -29, "y": -6, "length": 29.614185789921695}, {"x": -27, "y": -13, "length": 29.966648127543394}, {"x": -23, "y": -18, "length": 29.206163733020468}, {"x": -18, "y": -23, "length": 29.206163733020468}, {"x": -10, "y": -30, "length": 31.622776601683793}, {"x": -10, "y": -160, "length": 160.31219541881399}, {"x": -30, "y": -155, "length": 157.87653403846943}, {"x": 0, "y": -195, "length": 195}, {"x": 30, "y": -155, "length": 157.87653403846943}, {"x": 10, "y": -160, "length": 160.31219541881399}, {"x": 10, "y": -30, "length": 31.622776601683793}, {"x": 18, "y": -23, "length": 29.206163733020468}, {"x": 23, "y": -18, "length": 29.206163733020468}, {"x": 27, "y": -13, "length": 29.966648127543394}, {"x": 29, "y": -6, "length": 29.614185789921695}], "y": -358, "rem": true, "isStatic": false, "rotation": 0, "graphic": true, "plr": false, "ignore": false, "id": -1, "density": 1, "safeId": -1, "line": true, "isWheel": false, "layer": 0, "physic": true}, "className": "frg.game.editor.objects::LandscapeShaper"}], [{"params": {"x": 4760, "y": 0, "scaleX": 1, "width": 9.3, "rotation": 0, "scaleY": 1, "height": 128.35}, "className": "SignPillar"}, {"params": {"x": 850, "y": 350, "scaleX": 1, "width": 171.75, "rotation": 0, "scaleY": 1, "height": 213.45}, "className": "Dec13"}, {"params": {"x": 950, "y": 330, "scaleX": 1, "width": 176.2, "rotation": 0, "scaleY": 1, "height": 242.2}, "className": "Dec16"}, {"params": {"x": 1010, "y": 340, "scaleX": 1, "width": 165.85, "rotation": 0, "scaleY": 1, "height": 244.8}, "className": "Dec14"}, {"params": {"x": 920, "y": 340, "scaleX": 1, "width": 197, "rotation": 0, "scaleY": 1, "height": 234.2}, "className": "Dec15"}, {"params": {"x": 1880, "y": 250, "scaleX": 1, "width": 165.85, "rotation": 0, "scaleY": 1, "height": 244.8}, "className": "Dec14"}, {"params": {"x": 1740, "y": 280, "scaleX": 1, "width": 171.75, "rotation": 0, "scaleY": 1, "height": 213.45}, "className": "Dec13"}, {"params": {"x": 6200, "y": 390, "scaleX": 1, "width": 197, "rotation": 0, "scaleY": 1, "height": 234.2}, "className": "Dec15"}, {"params": {"x": 6140, "y": 400, "scaleX": 1, "width": 171.75, "rotation": 0, "scaleY": 1, "height": 213.45}, "className": "Dec13"}, {"params": {"x": 6260, "y": 380, "scaleX": 1, "width": 165.85, "rotation": 0, "scaleY": 1, "height": 244.8}, "className": "Dec14"}, {"params": {"x": 9430, "y": 1440, "scaleX": 1, "width": 9.3, "rotation": 0, "scaleY": 1, "height": 128.35}, "className": "SignPillar"}, {"params": {"x": 7110, "y": 310, "scaleX": 1, "width": 176.2, "rotation": 0, "scaleY": 1, "height": 242.2}, "className": "Dec16"}, {"params": {"x": 6990, "y": 340, "scaleX": 1, "width": 171.75, "rotation": 0, "scaleY": 1, "height": 213.45}, "className": "Dec13"}, {"params": {"x": 8440, "y": 880, "scaleX": 1, "width": 165.85, "rotation": 12, "scaleY": 1, "height": 244.8}, "className": "Dec14"}, {"params": {"x": 7880, "y": 420, "scaleX": 1, "width": 197, "rotation": 21, "scaleY": 1, "height": 234.2}, "className": "Dec15"}, {"params": {"x": 8380, "y": 860, "scaleX": 1, "width": 197, "rotation": 6, "scaleY": 1, "height": 234.2}, "className": "Dec15"}, {"params": {"x": 7920, "y": 440, "scaleX": 1, "width": 165.85, "rotation": 21, "scaleY": 1, "height": 244.8}, "className": "Dec14"}, {"params": {"x": 7830, "y": 400, "scaleX": 1, "width": 176.2, "rotation": 0, "scaleY": 1, "height": 242.2}, "className": "Dec16"}, {"params": {"x": 14330, "y": 1500, "scaleX": 1, "width": 171.75, "rotation": 0, "scaleY": 1, "height": 213.45}, "className": "Dec13"}, {"params": {"x": 14480, "y": 1490, "scaleX": 1, "width": 165.85, "rotation": 0, "scaleY": 1, "height": 244.8}, "className": "Dec14"}, {"params": {"x": 14420, "y": 1480, "scaleX": 1, "width": 176.2, "rotation": 0, "scaleY": 1, "height": 242.2}, "className": "Dec16"}, {"params": {"x": 100, "y": 386, "scaleX": 1, "width": 9.3, "rotation": 0, "scaleY": 1, "height": 128.35}, "className": "SignPillar"}, {"params": {"rotation": 6, "x": 102, "height": 35.15, "y": 320, "width": 110.7}, "className": "Sign15"}], [{"params": {"x": 9570, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1510}, "className": "Plank0"}, {"params": {"x": 9628, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1503}, "className": "Plank0"}, {"params": {"x": 9743, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1489}, "className": "Plank0"}, {"params": {"x": 9685, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1496}, "className": "Plank0"}, {"params": {"x": 9852, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1476}, "className": "Plank0"}, {"params": {"x": 9909, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1469}, "className": "Plank0"}, {"params": {"x": 9967, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1462}, "className": "Plank0"}, {"params": {"x": 9794, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1483}, "className": "Plank0"}, {"params": {"x": 10417, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1408}, "className": "Plank0"}, {"params": {"x": 10078, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1449}, "className": "Plank0"}, {"params": {"x": 10135, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1442}, "className": "Plank0"}, {"params": {"x": 10244, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1429}, "className": "Plank0"}, {"params": {"x": 10359, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1415}, "className": "Plank0"}, {"params": {"x": 10193, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1435}, "className": "Plank0"}, {"params": {"x": 10302, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1422}, "className": "Plank0"}, {"params": {"x": 10020, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1456}, "className": "Plank0"}, {"params": {"x": 10589, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1387}, "className": "Plank0"}, {"params": {"x": 10532, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1394}, "className": "Plank0"}, {"params": {"x": 10474, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1401}, "className": "Plank0"}, {"params": {"x": 10647, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1380}, "className": "Plank0"}, {"params": {"x": 10705, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1373}, "className": "Plank0"}, {"params": {"x": 10763, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1366}, "className": "Plank0"}, {"params": {"x": 10823, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1359}, "className": "Plank0"}, {"params": {"x": 10883, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1352}, "className": "Plank0"}, {"params": {"x": 10940, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1346}, "className": "Plank0"}, {"params": {"x": 12234, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1319}, "className": "Plank0"}, {"params": {"x": 12125, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1332}, "className": "Plank0"}, {"params": {"x": 12873, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1242}, "className": "Plank0"}, {"params": {"x": 12637, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1270}, "className": "Plank0"}, {"params": {"x": 12464, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1291}, "className": "Plank0"}, {"params": {"x": 12522, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1284}, "className": "Plank0"}, {"params": {"x": 12183, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1325}, "className": "Plank0"}, {"params": {"x": 12579, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1277}, "className": "Plank0"}, {"params": {"x": 12349, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1305}, "className": "Plank0"}, {"params": {"x": 12010, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1346}, "className": "Plank0"}, {"params": {"x": 12753, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1256}, "className": "Plank0"}, {"params": {"x": 11957, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1352}, "className": "Plank0"}, {"params": {"x": 12292, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1312}, "className": "Plank0"}, {"params": {"x": 12695, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1263}, "className": "Plank0"}, {"params": {"x": 12068, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1339}, "className": "Plank0"}, {"params": {"x": 12407, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1298}, "className": "Plank0"}, {"params": {"x": 12813, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1249}, "className": "Plank0"}, {"params": {"x": 13167, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1207}, "className": "Plank0"}, {"params": {"x": 13107, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1214}, "className": "Plank0"}, {"params": {"x": 12989, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1228}, "className": "Plank0"}, {"params": {"x": 13047, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1221}, "className": "Plank0"}, {"params": {"x": 12931, "id": -1, "safeId": -1, "width": 73, "rotation": -6, "active": true, "height": 20.5, "y": 1235}, "className": "Plank0"}], null, [{"params": {"rotation": 0, "x": 240, "height": 32, "y": 330, "width": 64}, "className": "PlayerWP"}, {"params": {"x": 4760, "id": 0, "y": -70, "width": 128, "rotation": 0, "height": 2930}, "className": "SafePointE"}, {"params": {"x": 9430, "id": 0, "y": 1360, "width": 128, "rotation": 0, "height": 1950}, "className": "SafePointE"}], [{"params": {"x": 9543, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1513}, "className": "PivotJointE"}, {"params": {"x": 9601, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1506}, "className": "PivotJointE"}, {"params": {"x": 9658, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1499}, "className": "PivotJointE"}, {"params": {"x": 9716, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1492}, "className": "PivotJointE"}, {"params": {"x": 9825, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1479}, "className": "PivotJointE"}, {"params": {"x": 9940, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1465}, "className": "PivotJointE"}, {"params": {"x": 9767, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1486}, "className": "PivotJointE"}, {"params": {"x": 9882, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1472}, "className": "PivotJointE"}, {"params": {"x": 10051, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1452}, "className": "PivotJointE"}, {"params": {"x": 10166, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1438}, "className": "PivotJointE"}, {"params": {"x": 9993, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1459}, "className": "PivotJointE"}, {"params": {"x": 10332, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1418}, "className": "PivotJointE"}, {"params": {"x": 10108, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1445}, "className": "PivotJointE"}, {"params": {"x": 10217, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1432}, "className": "PivotJointE"}, {"params": {"x": 10390, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1411}, "className": "PivotJointE"}, {"params": {"x": 10275, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1425}, "className": "PivotJointE"}, {"params": {"x": 10620, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1383}, "className": "PivotJointE"}, {"params": {"x": 10562, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1390}, "className": "PivotJointE"}, {"params": {"x": 10505, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1397}, "className": "PivotJointE"}, {"params": {"x": 10450, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1403}, "className": "PivotJointE"}, {"params": {"x": 10675, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1376}, "className": "PivotJointE"}, {"params": {"x": 10734, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1369}, "className": "PivotJointE"}, {"params": {"x": 10795, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1361}, "className": "PivotJointE"}, {"params": {"x": 10855, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1355}, "className": "PivotJointE"}, {"params": {"x": 10912, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1349}, "className": "PivotJointE"}, {"params": {"x": 10969, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1343}, "className": "PivotJointE"}, {"params": {"x": 12665, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1266}, "className": "PivotJointE"}, {"params": {"x": 12098, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1335}, "className": "PivotJointE"}, {"params": {"x": 12440, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1293}, "className": "PivotJointE"}, {"params": {"x": 11983, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1349}, "className": "PivotJointE"}, {"params": {"x": 12322, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1308}, "className": "PivotJointE"}, {"params": {"x": 12724, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1259}, "className": "PivotJointE"}, {"params": {"x": 12785, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1251}, "className": "PivotJointE"}, {"params": {"x": 12156, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1328}, "className": "PivotJointE"}, {"params": {"x": 12495, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1287}, "className": "PivotJointE"}, {"params": {"x": 12552, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1280}, "className": "PivotJointE"}, {"params": {"x": 12380, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1301}, "className": "PivotJointE"}, {"params": {"x": 12610, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1273}, "className": "PivotJointE"}, {"params": {"x": 12265, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1315}, "className": "PivotJointE"}, {"params": {"x": 12845, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1245}, "className": "PivotJointE"}, {"params": {"x": 12041, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1342}, "className": "PivotJointE"}, {"params": {"x": 12207, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1322}, "className": "PivotJointE"}, {"params": {"x": 13018, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1225}, "className": "PivotJointE"}, {"params": {"x": 12959, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1232}, "className": "PivotJointE"}, {"params": {"x": 13139, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1210}, "className": "PivotJointE"}, {"params": {"x": 13079, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1217}, "className": "PivotJointE"}, {"params": {"x": 12907, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1238}, "className": "PivotJointE"}, {"params": {"x": 11931, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1355}, "className": "PivotJointE"}, {"params": {"x": 13199, "id": -1, "safeId": -1, "width": 17, "rotation": 0, "graphic": 0, "height": 17, "y": 1204}, "className": "PivotJointE"}, {"params": {"x": 13845, "y": 1642, "width": 17, "rotation": 0, "graphic": 1, "height": 17, "time": 0, "snd": false, "id": 0, "safeId": -1, "useWeld": false, "rate": -250}, "className": "MotorJointE"}, {"params": {"fnl": false, "id": 0, "x": 9430, "safeId": -1, "sndId": -1, "id_off": -1, "rotation": 0, "off": false, "height": 1456, "y": 1340, "width": 349}, "className": "ToggleE"}, {"params": {"fnl": false, "id": 7, "x": 7240, "safeId": -1, "sndId": -1, "id_off": -1, "rotation": 0, "off": false, "height": 1164, "y": -30, "width": 64}, "className": "ToggleE"}, {"params": {"x": 7355, "y": -358, "width": 17, "rotation": 0, "graphic": 1, "height": 17, "time": -1, "snd": false, "id": 7, "safeId": -1, "useWeld": false, "rate": -100}, "className": "MotorJointE"}, {"params": {"fnl": false, "id": 23, "x": 3580, "safeId": -1, "sndId": 0, "id_off": -1, "rotation": 0, "off": false, "height": 1501, "y": -230, "width": 64}, "className": "ToggleE"}, {"params": {"fnl": false, "id": 24, "x": 7570, "safeId": -1, "sndId": 0, "id_off": -1, "rotation": 0, "off": false, "height": 64, "y": -320, "width": 296}, "className": "ToggleE"}, {"params": {"fnl": false, "id": 26, "x": 13320, "safeId": -1, "sndId": 0, "id_off": -1, "rotation": 0, "off": false, "height": 2372, "y": 1200, "width": 64}, "className": "ToggleE"}]], "settings": {"prizes": "25,32,40", "theme": 0, "countdown": 0, "gravityY": 500}}