﻿{"settings":{"gravityY":500,"countdown":0,"theme":0,"prizes":"26,31,36"},"layers":[[{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":261,"graphic":true,"shapeH":20,"height":73,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":0,"x":0},{"y":-59,"x":58}],"width":72.3,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":392}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":261,"graphic":true,"shapeH":20,"height":72.1,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":0,"x":0},{"y":58,"x":58}],"width":72.1,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":272}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":261,"graphic":true,"shapeH":20,"height":72.1,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":0,"x":0},{"y":58,"x":58}],"width":72.1,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":32}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":261,"graphic":true,"shapeH":20,"height":73,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":0,"x":0},{"y":-59,"x":58}],"width":72.3,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":-208}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":261,"graphic":true,"shapeH":20,"height":72.1,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":0,"x":0},{"y":58,"x":58}],"width":72.1,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":-918}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":261,"graphic":true,"shapeH":20,"height":73,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":0,"x":0},{"y":-59,"x":58}],"width":72.3,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":-798}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":261,"graphic":true,"shapeH":20,"height":72.1,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":0,"x":0},{"y":58,"x":58}],"width":72.1,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":152}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":261,"graphic":true,"shapeH":20,"height":73,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":0,"x":0},{"y":-59,"x":58}],"width":72.3,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":-328}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":261,"graphic":true,"shapeH":20,"height":73,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":0,"x":0},{"y":-59,"x":58}],"width":72.3,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":152}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":261,"graphic":true,"shapeH":20,"height":73,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":0,"x":0},{"y":-59,"x":58}],"width":72.3,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":-448}},{"className":"FinishZone","params":{"x":11430,"width":1049,"y":-2350,"rotation":0,"height":2340}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":261,"graphic":true,"shapeH":20,"height":72.1,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":0,"x":0},{"y":58,"x":58}],"width":72.1,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":-798}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":261,"graphic":true,"shapeH":20,"height":73,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":0,"x":0},{"y":-59,"x":58}],"width":72.3,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":32}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":261,"graphic":true,"shapeH":20,"height":72.1,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":0,"x":0},{"y":58,"x":58}],"width":72.1,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":-448}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":261,"graphic":true,"shapeH":20,"height":72.1,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":0,"x":0},{"y":58,"x":58}],"width":72.1,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":-568}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":261,"graphic":true,"shapeH":20,"height":72.1,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":0,"x":0},{"y":58,"x":58}],"width":72.1,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":-208}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":261,"graphic":true,"shapeH":20,"height":72.1,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":0,"x":0},{"y":58,"x":58}],"width":72.1,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":-88}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":261,"graphic":true,"shapeH":20,"height":73,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":0,"x":0},{"y":-59,"x":58}],"width":72.3,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":-916}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":261,"graphic":true,"shapeH":20,"height":72.1,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":0,"x":0},{"y":58,"x":58}],"width":72.1,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":-328}},{"className":"frg.game.editor.objects::PillarPather","params":{"straightSides":true,"vertices":[{"y":5,"x":0},{"y":-830,"x":0},{"y":-975,"x":120},{"y":5,"x":125}],"width":143,"y":200,"repeatTexture":true,"stretchTexture":false,"x":5870,"originOffsetRatio":0,"height":1011.35,"wireframe":false,"type":2,"snapToGrid":true,"textureMode":true,"textureOffset":0,"rotation":0,"smoothing":true}},{"className":"frg.game.editor.objects::PillarPather","params":{"straightSides":true,"vertices":[{"y":5,"x":0},{"y":-975,"x":0},{"y":-840,"x":125},{"y":5,"x":125}],"width":143,"y":210,"repeatTexture":true,"stretchTexture":false,"x":5360,"originOffsetRatio":0,"height":1009.45,"wireframe":false,"type":2,"snapToGrid":true,"textureMode":true,"textureOffset":0,"rotation":0,"smoothing":true}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":261,"graphic":true,"shapeH":20,"height":73,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":0,"x":0},{"y":-59,"x":58}],"width":72.3,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":-568}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":261,"graphic":true,"shapeH":20,"height":72.1,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":0,"x":0},{"y":58,"x":58}],"width":72.1,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":-688}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":261,"graphic":true,"shapeH":20,"height":73,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":0,"x":0},{"y":-59,"x":58}],"width":72.3,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":272}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":261,"graphic":true,"shapeH":20,"height":73,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":0,"x":0},{"y":-59,"x":58}],"width":72.3,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":-688}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":261,"graphic":true,"shapeH":20,"height":73,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":0,"x":0},{"y":-59,"x":58}],"width":72.3,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":-88}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":640,"graphic":true,"shapeH":20,"height":677.9,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":0,"x":0},{"y":-6,"x":57},{"y":-22,"x":109},{"y":-43,"x":153},{"y":-73,"x":196},{"y":-107,"x":232},{"y":-146,"x":263},{"y":-186,"x":286},{"y":-237,"x":307},{"y":-294,"x":319},{"y":-354,"x":321},{"y":-414,"x":309},{"y":-470,"x":285},{"y":-527,"x":249},{"y":-577,"x":206},{"y":-621,"x":148},{"y":-648,"x":80},{"y":-658,"x":-2}],"width":339.55,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":-310}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":961,"graphic":true,"shapeH":20,"height":73,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":0,"x":0},{"y":-59,"x":58}],"width":72.3,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":-438}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":961,"graphic":true,"shapeH":20,"height":73,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":0,"x":0},{"y":-59,"x":58}],"width":72.3,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":-798}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":961,"graphic":true,"shapeH":20,"height":73,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":0,"x":0},{"y":-59,"x":58}],"width":72.3,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":-678}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":961,"graphic":true,"shapeH":20,"height":72.1,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":0,"x":0},{"y":58,"x":58}],"width":72.1,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":-798}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":961,"graphic":true,"shapeH":20,"height":72.1,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":0,"x":0},{"y":58,"x":58}],"width":72.1,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":-678}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":961,"graphic":true,"shapeH":20,"height":72.1,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":0,"x":0},{"y":58,"x":58}],"width":72.1,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":-558}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":961,"graphic":true,"shapeH":20,"height":73,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":0,"x":0},{"y":-59,"x":58}],"width":72.3,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":-558}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":961,"graphic":true,"shapeH":20,"height":73,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":0,"x":0},{"y":-59,"x":58}],"width":72.3,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":-918}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":961,"graphic":true,"shapeH":20,"height":72.1,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":0,"x":0},{"y":58,"x":58}],"width":72.1,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":-918}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":1021,"graphic":true,"shapeH":20,"height":657.5,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":739,"x":-760},{"y":739,"x":0},{"y":1380,"x":0}],"width":776.5,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":-1808}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":961,"graphic":true,"shapeH":20,"height":551,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":150,"x":-637},{"y":150,"x":0},{"y":681,"x":0},{"y":681,"x":67}],"width":717,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":-1118}},{"className":"frg.game.editor.objects::LandscapeShaper","params":{"x":815,"graphic":true,"ignore":false,"y":-350,"physic":true,"safeId":-1,"vertices":[{"y":-2,"x":5},{"y":-35,"x":50},{"y":-57,"x":103},{"y":-65,"x":152},{"y":-67,"x":195},{"y":-59,"x":245},{"y":-45,"x":285},{"y":-20,"x":329},{"y":13,"x":367},{"y":54,"x":401},{"y":98,"x":424},{"y":147,"x":438},{"y":201,"x":442},{"y":252,"x":438},{"y":308,"x":417},{"y":355,"x":388},{"y":391,"x":355},{"y":420,"x":317},{"y":442,"x":272},{"y":455,"x":225},{"y":459,"x":172},{"y":452,"x":118},{"y":438,"x":74},{"y":417,"x":35},{"y":390,"x":0},{"y":350,"x":-35},{"y":314,"x":-58},{"y":271,"x":-75},{"y":227,"x":-82},{"y":180,"x":-83},{"y":132,"x":-76},{"y":89,"x":-62},{"y":41,"x":-35}],"layer":3,"rem":true,"plr":false,"density":1,"isStatic":false,"line":false,"rotation":0,"id":-1,"isWheel":false}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":323,"graphic":true,"shapeH":20,"height":205.7,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":0,"x":0},{"y":-192,"x":181}],"width":195.6,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":-448}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":321,"graphic":true,"shapeH":20,"height":1412,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":50,"x":2},{"y":1449,"x":0}],"width":22,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":-1028}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":261,"graphic":true,"shapeH":20,"height":1514,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":-51,"x":0},{"y":1450,"x":0}],"width":20,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":true,"isStatic":true,"smoothing":true,"y":-1028}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":323,"graphic":true,"shapeH":20,"height":171.55,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":0,"x":0},{"y":154,"x":178},{"y":153,"x":385}],"width":398.05,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":-788}},{"className":"frg.game.editor.objects::GroundPather","params":{"direction":90,"x":0,"width":12460,"y":420,"physic":true,"shape":true,"lineId":0,"height":1763,"wireframe":false,"snapToGrid":false,"isRoad":true,"line":true,"repeatTexture":true,"smoothing":true,"straightSides":true,"vertices":[{"y":2,"x":-491},{"y":-2,"x":2385},{"y":-480,"x":2394},{"y":-480,"x":3112},{"y":-32,"x":4768},{"y":-12,"x":4870},{"y":-9,"x":4966},{"y":-23,"x":5056},{"y":-46,"x":5137},{"y":-89,"x":5223},{"y":-141,"x":5293},{"y":-210,"x":5357},{"y":-218,"x":6792},{"y":-226,"x":6882},{"y":-243,"x":6977},{"y":-269,"x":7059},{"y":-315,"x":7147},{"y":-370,"x":7226},{"y":-433,"x":7295},{"y":-521,"x":7364},{"y":-613,"x":7413},{"y":-713,"x":7449},{"y":-807,"x":7469},{"y":-853,"x":7516},{"y":-853,"x":8603},{"y":-862,"x":8693},{"y":-888,"x":8793},{"y":-923,"x":8875},{"y":-965,"x":8951},{"y":-1030,"x":9032},{"y":-1091,"x":9093},{"y":-1161,"x":9147},{"y":-1235,"x":9192},{"y":-1310,"x":9223},{"y":-1377,"x":9242},{"y":-1451,"x":9254},{"y":-1560,"x":9258},{"y":-1660,"x":9256},{"y":-1693,"x":9281},{"y":-1693,"x":11956}],"textureOffset":0,"thick":128,"stretchTexture":false,"directed":true,"originOffsetRatio":0,"camera":false,"cameraOffsetY":120,"textureMode":true,"rotation":0}},{"className":"frg.game.editor.objects::PillarPather","params":{"straightSides":true,"vertices":[{"y":0,"x":0},{"y":156,"x":339},{"y":-122,"x":339}],"width":354.5,"y":-310,"repeatTexture":true,"stretchTexture":false,"x":650,"originOffsetRatio":0,"height":298.55,"wireframe":false,"type":2,"snapToGrid":true,"textureMode":true,"textureOffset":0,"rotation":0,"smoothing":true}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":1694,"graphic":true,"shapeH":6,"height":1038,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":0,"x":49},{"y":-1025,"x":386}],"width":350,"originOffsetRatio":0,"physic":true,"rotation":-60,"id":-1,"wireframe":false,"type":3,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":false,"smoothing":true,"y":13}},{"className":"frg.game.editor.objects::LandscapeShaper","params":{"x":1365,"graphic":true,"ignore":false,"y":123,"physic":true,"safeId":-1,"vertices":[{"y":20,"x":5},{"y":93,"x":64},{"y":140,"x":113},{"y":180,"x":183},{"y":210,"x":264},{"y":235,"x":384},{"y":270,"x":160},{"y":195,"x":35}],"layer":0,"rem":true,"plr":false,"density":10,"isStatic":false,"line":false,"rotation":-60,"id":-1,"isWheel":false}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":1392,"graphic":true,"shapeH":6,"height":904,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":51,"x":11},{"y":-840,"x":644}],"width":646,"originOffsetRatio":0,"physic":true,"rotation":-60,"id":-1,"wireframe":false,"type":3,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":false,"smoothing":true,"y":116}},{"className":"frg.game.editor.objects::DynamicPather","params":{"straightSides":true,"x":951,"graphic":true,"shapeH":20,"height":20,"stretchTexture":false,"safeId":-1,"action":0,"vertices":[{"y":43,"x":41},{"y":43,"x":7}],"width":47,"originOffsetRatio":0,"physic":true,"rotation":0,"id":-1,"wireframe":false,"type":2,"density":"1","textureMode":true,"repeatTexture":true,"textureOffset":0,"snapToGrid":false,"isStatic":true,"smoothing":true,"y":-898}},{"className":"frg.game.editor.objects::GroundPather","params":{"direction":90,"x":0,"width":12462,"y":420,"physic":false,"shape":false,"lineId":0,"height":1776,"wireframe":false,"snapToGrid":false,"isRoad":false,"line":false,"repeatTexture":true,"smoothing":true,"straightSides":true,"vertices":[{"y":1,"x":-493},{"y":-5,"x":1425},{"y":-480,"x":2394},{"y":-480,"x":3112},{"y":-32,"x":4768},{"y":-12,"x":4870},{"y":-9,"x":4966},{"y":-23,"x":5056},{"y":-46,"x":5137},{"y":-89,"x":5223},{"y":-141,"x":5293},{"y":-210,"x":5357},{"y":-218,"x":6792},{"y":-226,"x":6882},{"y":-243,"x":6977},{"y":-269,"x":7059},{"y":-315,"x":7147},{"y":-370,"x":7226},{"y":-433,"x":7295},{"y":-521,"x":7364},{"y":-613,"x":7413},{"y":-713,"x":7449},{"y":-807,"x":7469},{"y":-853,"x":7516},{"y":-853,"x":8603},{"y":-862,"x":8693},{"y":-888,"x":8793},{"y":-923,"x":8875},{"y":-965,"x":8951},{"y":-1030,"x":9032},{"y":-1091,"x":9093},{"y":-1161,"x":9147},{"y":-1235,"x":9192},{"y":-1310,"x":9223},{"y":-1377,"x":9242},{"y":-1451,"x":9254},{"y":-1560,"x":9258},{"y":-1660,"x":9256},{"y":-1693,"x":9281},{"y":-1693,"x":11956}],"textureOffset":0,"thick":128,"stretchTexture":false,"directed":false,"originOffsetRatio":0,"camera":true,"cameraOffsetY":120,"textureMode":true,"rotation":0}},{"className":"frg.game.editor.objects::LandscapeShaper","params":{"x":385,"graphic":true,"ignore":false,"y":268,"physic":true,"safeId":2,"vertices":[{"y":0,"x":0},{"y":-11,"x":18},{"y":-19,"x":37},{"y":-21,"x":59},{"y":-14,"x":86},{"y":0,"x":105},{"y":14,"x":120},{"y":38,"x":129},{"y":64,"x":131},{"y":89,"x":124},{"y":110,"x":109},{"y":125,"x":85},{"y":132,"x":56},{"y":126,"x":24},{"y":109,"x":1},{"y":89,"x":-14},{"y":66,"x":-21},{"y":41,"x":-21},{"y":20,"x":-13}],"layer":3,"rem":true,"plr":false,"density":1,"isStatic":false,"line":false,"rotation":0,"id":-1,"isWheel":false}},{"className":"frg.game.editor.objects::LandscapeShaper","params":{"x":1615,"graphic":true,"ignore":false,"y":258,"physic":true,"safeId":2,"vertices":[{"y":0,"x":0},{"y":-11,"x":18},{"y":-19,"x":37},{"y":-21,"x":59},{"y":-14,"x":86},{"y":0,"x":105},{"y":14,"x":120},{"y":38,"x":129},{"y":64,"x":131},{"y":89,"x":124},{"y":110,"x":109},{"y":125,"x":85},{"y":132,"x":56},{"y":126,"x":24},{"y":109,"x":1},{"y":89,"x":-14},{"y":66,"x":-21},{"y":41,"x":-21},{"y":20,"x":-13}],"layer":3,"rem":true,"plr":false,"density":1,"isStatic":false,"line":false,"rotation":0,"id":-1,"isWheel":false}},{"className":"frg.game.editor.objects::LandscapeShaper","params":{"x":1805,"graphic":true,"ignore":false,"y":-50,"physic":true,"safeId":-1,"vertices":[{"y":-2,"x":5},{"y":-35,"x":50},{"y":-57,"x":103},{"y":-65,"x":152},{"y":-67,"x":195},{"y":-59,"x":245},{"y":-45,"x":285},{"y":-20,"x":329},{"y":13,"x":367},{"y":54,"x":401},{"y":98,"x":424},{"y":147,"x":438},{"y":201,"x":442},{"y":252,"x":438},{"y":308,"x":417},{"y":355,"x":388},{"y":391,"x":355},{"y":420,"x":317},{"y":442,"x":272},{"y":455,"x":225},{"y":459,"x":172},{"y":452,"x":118},{"y":438,"x":74},{"y":417,"x":35},{"y":390,"x":0},{"y":350,"x":-35},{"y":314,"x":-58},{"y":271,"x":-75},{"y":227,"x":-82},{"y":180,"x":-83},{"y":132,"x":-76},{"y":89,"x":-62},{"y":41,"x":-35}],"layer":3,"rem":true,"plr":false,"density":1,"isStatic":false,"line":false,"rotation":0,"id":-1,"isWheel":false}},{"className":"frg.game.editor.objects::LandscapeShaper","params":{"x":2235,"graphic":true,"ignore":false,"y":258,"physic":true,"safeId":2,"vertices":[{"y":0,"x":0},{"y":-11,"x":18},{"y":-19,"x":37},{"y":-21,"x":59},{"y":-14,"x":86},{"y":0,"x":105},{"y":14,"x":120},{"y":38,"x":129},{"y":64,"x":131},{"y":89,"x":124},{"y":110,"x":109},{"y":125,"x":85},{"y":132,"x":56},{"y":126,"x":24},{"y":109,"x":1},{"y":89,"x":-14},{"y":66,"x":-21},{"y":41,"x":-21},{"y":20,"x":-13}],"layer":3,"rem":true,"plr":false,"density":1,"isStatic":false,"line":false,"rotation":0,"id":-1,"isWheel":false}},{"className":"frg.game.editor.objects::PillarPather","params":{"straightSides":true,"vertices":[{"y":0,"x":0},{"y":0,"x":114},{"y":90,"x":114}],"width":129.5,"y":325,"repeatTexture":true,"stretchTexture":false,"x":320,"originOffsetRatio":0,"height":105.5,"wireframe":false,"type":2,"snapToGrid":true,"textureMode":true,"textureOffset":0,"rotation":0,"smoothing":true}},{"className":"frg.game.editor.objects::PillarPather","params":{"straightSides":true,"vertices":[{"y":0,"x":0},{"y":-105,"x":0},{"y":-275,"x":312},{"y":-105,"x":615},{"y":0,"x":615}],"width":633,"y":420,"repeatTexture":true,"stretchTexture":false,"x":1670,"originOffsetRatio":0,"height":291.8,"wireframe":false,"type":2,"snapToGrid":true,"textureMode":true,"textureOffset":0,"rotation":0,"smoothing":true}},{"className":"OvalTemplate","params":{"x":5880,"width":529.1,"y":-260,"rotation":0,"height":529.1}},{"className":"frg.game.editor.objects::LandscapeShaper","params":{"x":5670,"graphic":true,"ignore":false,"y":110,"physic":true,"safeId":1,"vertices":[{"y":-615,"x":0},{"y":-615,"x":425},{"y":100,"x":425},{"y":100,"x":0},{"y":4,"x":1},{"y":-2,"x":58},{"y":-22,"x":116},{"y":-62,"x":176},{"y":-106,"x":216},{"y":-160,"x":246},{"y":-219,"x":261},{"y":-282,"x":262},{"y":-347,"x":250},{"y":-400,"x":226},{"y":-452,"x":184},{"y":-490,"x":130},{"y":-516,"x":66},{"y":-525,"x":0}],"layer":4,"rem":true,"plr":false,"density":1,"isStatic":false,"line":false,"rotation":0,"id":-1,"isWheel":false}},{"className":"frg.game.editor.objects::LandscapeShaper","params":{"x":5670,"graphic":true,"ignore":false,"y":-1030,"physic":true,"safeId":1,"vertices":[{"y":-615,"x":0},{"y":-615,"x":435},{"y":100,"x":435},{"y":100,"x":0},{"y":4,"x":1},{"y":-2,"x":58},{"y":-22,"x":116},{"y":-62,"x":176},{"y":-106,"x":216},{"y":-160,"x":246},{"y":-219,"x":261},{"y":-282,"x":262},{"y":-347,"x":250},{"y":-400,"x":226},{"y":-452,"x":184},{"y":-490,"x":130},{"y":-516,"x":66},{"y":-525,"x":0}],"layer":4,"rem":true,"plr":false,"density":1,"isStatic":false,"line":false,"rotation":180,"id":-1,"isWheel":false}},{"className":"frg.game.editor.objects::LandscapeShaper","params":{"x":7788,"graphic":true,"ignore":false,"y":-2071,"physic":true,"safeId":2,"vertices":[{"y":-10,"x":90},{"y":10,"x":147},{"y":-82,"x":691},{"y":-182,"x":856},{"y":-100,"x":805},{"y":-75,"x":808},{"y":-22,"x":886},{"y":-44,"x":700},{"y":47,"x":154},{"y":80,"x":100},{"y":50,"x":-30}],"layer":4,"rem":true,"plr":false,"density":1,"isStatic":false,"line":false,"rotation":-141,"id":-1,"isWheel":false}}],[{"className":"SignPillar","params":{"x":2743,"scaleY":1,"y":-122,"height":128.35,"width":9.3,"rotation":0,"scaleX":1}},{"className":"SignPillar","params":{"x":7900,"scaleY":1,"y":-500,"height":128.35,"width":9.3,"rotation":0,"scaleX":1}},{"className":"Dec13","params":{"x":90,"scaleY":1,"y":330,"height":213.45,"width":171.75,"rotation":0,"scaleX":1}},{"className":"Dec14","params":{"x":690,"scaleY":1,"y":310,"height":244.8,"width":165.85,"rotation":0,"scaleX":1}},{"className":"Dec16","params":{"x":1220,"scaleY":1,"y":320,"height":242.2,"width":176.2,"rotation":15,"scaleX":1}},{"className":"Dec13","params":{"x":1080,"scaleY":1,"y":360,"height":213.45,"width":171.75,"rotation":-18,"scaleX":1}},{"className":"Dec13","params":{"x":2390,"scaleY":1,"y":-150,"height":213.45,"width":171.75,"rotation":0,"scaleX":1}},{"className":"Dec13","params":{"x":3100,"scaleY":1,"y":-150,"height":213.45,"width":171.75,"rotation":0,"scaleX":-1}},{"className":"Dec14","params":{"x":3750,"scaleY":1,"y":0,"height":244.8,"width":165.85,"rotation":12,"scaleX":1}},{"className":"Dec13","params":{"x":4220,"scaleY":1,"y":150,"height":213.45,"width":171.75,"rotation":12,"scaleX":1}},{"className":"Dec14","params":{"x":4320,"scaleY":1,"y":150,"height":244.8,"width":165.85,"rotation":6,"scaleX":1}},{"className":"Dec13","params":{"x":4860,"scaleY":1,"y":320,"height":213.45,"width":171.75,"rotation":0,"scaleX":1}},{"className":"Dec13","params":{"x":4960,"scaleY":0.859451862262825,"y":330,"height":183.45,"width":141.75,"rotation":0,"scaleX":-0.8253275109170306}},{"className":"Dec13","params":{"x":6920,"scaleY":1,"y":90,"height":213.45,"width":171.75,"rotation":0,"scaleX":1}},{"className":"Dec14","params":{"x":7480,"scaleY":1,"y":-530,"height":244.8,"width":165.85,"rotation":-27,"scaleX":1}},{"className":"Dec15","params":{"x":8540,"scaleY":1,"y":-530,"height":234.2,"width":197,"rotation":-18,"scaleX":1}},{"className":"Dec14","params":{"x":8610,"scaleY":1,"y":-540,"height":244.8,"width":165.85,"rotation":0,"scaleX":1}},{"className":"Dec13","params":{"x":10280,"scaleY":1,"y":-1360,"height":213.45,"width":171.75,"rotation":0,"scaleX":1}},{"className":"Dec14","params":{"x":10380,"scaleY":1,"y":-1380,"height":244.8,"width":165.85,"rotation":0,"scaleX":1}},{"className":"Dec15","params":{"x":10860,"scaleY":1,"y":-1380,"height":234.2,"width":197,"rotation":0,"scaleX":1}}],[{"className":"EggE","params":{"x":600,"width":45.65,"y":-840,"rotation":0,"height":57.4}}],null,[{"className":"PlayerWP","params":{"x":600,"width":64,"y":-710,"rotation":0,"height":32}},{"className":"SafePointE","params":{"x":2741,"width":128,"y":-193,"height":1689,"rotation":0,"id":1}},{"className":"SafePointE","params":{"x":7900,"width":128,"y":-570,"height":1271,"rotation":0,"id":2}}],[{"className":"MotorJointE","params":{"snd":false,"graphic":1,"y":-154,"height":17,"id":1,"safeId":-1,"x":993,"width":17,"rate":-180,"rotation":0,"time":800}},{"className":"frg.game.editor.objects::MoverPather","params":{"x":7434,"width":1453,"position":0,"y":-2333,"rotation":0,"safeId":2,"vertices":[{"y":0,"x":0},{"y":848,"x":1440}],"height":861,"active":0,"cycle":0,"speed":1500,"body":false,"startTime":"0.5","accelDist":0,"snapToGrid":true,"stopOnEnd":true,"id":3}},{"className":"PivotJointE","params":{"x":990,"graphic":0,"y":-855,"height":17,"safeId":-1,"width":17,"rotation":-60,"id":-1}},{"className":"PivotJointE","params":{"x":1007,"graphic":0,"y":-823,"height":17,"safeId":-1,"width":17,"rotation":-60,"id":-1}},{"className":"PivotJointE","params":{"x":1718,"graphic":0,"y":-33,"height":17,"safeId":-1,"width":17,"rotation":-60,"id":-1}},{"className":"PivotJointE","params":{"x":1441,"graphic":0,"y":129,"height":17,"safeId":-1,"width":17,"rotation":-60,"id":-1}},{"className":"MotorJointE","params":{"snd":false,"graphic":1,"y":324,"height":17,"id":1,"safeId":2,"x":440,"width":17,"rate":-150,"rotation":0,"time":800}},{"className":"MotorJointE","params":{"snd":false,"graphic":1,"y":314,"height":17,"id":1,"safeId":2,"x":1670,"width":17,"rate":-150,"rotation":0,"time":800}},{"className":"MotorJointE","params":{"snd":false,"graphic":1,"y":146,"height":17,"id":1,"safeId":-1,"x":1983,"width":17,"rate":-180,"rotation":0,"time":800}},{"className":"MotorJointE","params":{"snd":false,"graphic":1,"y":314,"height":17,"id":1,"safeId":2,"x":2290,"width":17,"rate":-150,"rotation":0,"time":800}},{"className":"ToggleE","params":{"x":620,"width":301,"off":false,"y":-640,"hint":-1,"height":64,"safeId":-1,"id_off":-1,"sndId":-1,"rotation":0,"id":1}},{"className":"frg.game.editor.objects::MoverPather","params":{"x":5940,"width":13,"position":0,"y":-140,"rotation":0,"safeId":1,"vertices":[{"y":0,"x":0},{"y":-624,"x":0}],"height":637,"active":0,"cycle":0,"speed":130,"body":false,"startTime":"0","accelDist":0,"snapToGrid":true,"stopOnEnd":true,"id":2}},{"className":"ToggleE","params":{"x":5140,"width":64,"off":false,"y":140,"hint":-1,"height":1155,"safeId":1,"id_off":-1,"sndId":-1,"rotation":0,"id":2}},{"className":"frg.game.editor.objects::MoverPather","params":{"x":5394,"width":13,"position":0,"y":-770,"rotation":0,"safeId":1,"vertices":[{"y":0,"x":0},{"y":624,"x":0}],"height":637,"active":0,"cycle":0,"speed":130,"body":false,"startTime":"0","accelDist":0,"snapToGrid":true,"stopOnEnd":true,"id":2}},{"className":"ToggleE","params":{"x":9370,"width":1194,"off":false,"y":-1380,"hint":-1,"height":64,"safeId":2,"id_off":-1,"sndId":-1,"rotation":0,"id":3}},{"className":"ToggleE","params":{"x":1700,"width":64,"off":false,"y":-110,"hint":-1,"height":807,"safeId":-1,"id_off":-1,"sndId":0,"rotation":0,"id":0}},{"className":"ToggleE","params":{"x":5680,"width":367,"off":false,"y":-160,"hint":-1,"height":237,"safeId":1,"id_off":-1,"sndId":0,"rotation":0,"id":0}},{"className":"ToggleE","params":{"x":9220,"width":367,"off":false,"y":-1510,"hint":-1,"height":237,"safeId":2,"id_off":-1,"sndId":0,"rotation":0,"id":0}}]]}