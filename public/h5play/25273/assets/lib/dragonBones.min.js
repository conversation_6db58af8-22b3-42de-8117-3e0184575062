"use strict";var __extends=this&&this.__extends||function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)if(e.hasOwnProperty(i))t[i]=e[i]};return function(e,i){t(e,i);function a(){this.constructor=e}e.prototype=i===null?Object.create(i):(a.prototype=i.prototype,new a)}}();var dragonBones;(function(t){})(dragonBones||(dragonBones={}));var dragonBones;(function(t){var e=function(){function e(i){this._clock=new t.WorldClock;this._events=[];this._objects=[];this._eventManager=null;this._eventManager=i;console.info("DragonBones: "+e.VERSION+"\nWebsite: http://www.dragonbones.com/\nSource: http://www.github.com/dragonbones/")}e.prototype.advanceTime=function(e){if(this._objects.length>0){for(var i=0,a=this._objects;i<a.length;i++){var r=a[i];r.returnToPool()}this._objects.length=0}this._clock.advanceTime(e);if(this._events.length>0){for(var n=0;n<this._events.length;++n){var s=this._events[n];var o=s.armature;if(o._armatureData!==null){o.eventDispatcher.dispatchDBEvent(s.type,s);if(s.type===t.EventObject.SOUND_EVENT){this._eventManager.dispatchDBEvent(s.type,s)}}this.bufferObject(s)}this._events.length=0}};e.prototype.bufferEvent=function(t){if(this._events.indexOf(t)<0){this._events.push(t)}};e.prototype.bufferObject=function(t){if(this._objects.indexOf(t)<0){this._objects.push(t)}};Object.defineProperty(e.prototype,"clock",{get:function(){return this._clock},enumerable:true,configurable:true});Object.defineProperty(e.prototype,"eventManager",{get:function(){return this._eventManager},enumerable:true,configurable:true});e.VERSION="5.6.0";e.yDown=true;e.debug=false;e.debugDraw=false;e.webAssembly=false;return e}();t.DragonBones=e;if(!console.warn){console.warn=function(){}}if(!console.assert){console.assert=function(){}}})(dragonBones||(dragonBones={}));var dragonBones;(function(t){var e=function(){function t(){this.hashCode=t._hashCode++;this._isInPool=false}t._returnObject=function(e){var i=String(e.constructor);var a=i in t._maxCountMap?t._maxCountMap[i]:t._defaultMaxCount;var r=t._poolsMap[i]=t._poolsMap[i]||[];if(r.length<a){if(!e._isInPool){e._isInPool=true;r.push(e)}else{console.warn("The object is already in the pool.")}}else{}};t.toString=function(){throw new Error};t.setMaxCount=function(e,i){if(i<0||i!==i){i=0}if(e!==null){var a=String(e);var r=a in t._poolsMap?t._poolsMap[a]:null;if(r!==null&&r.length>i){r.length=i}t._maxCountMap[a]=i}else{t._defaultMaxCount=i;for(var a in t._poolsMap){var r=t._poolsMap[a];if(r.length>i){r.length=i}if(a in t._maxCountMap){t._maxCountMap[a]=i}}}};t.clearPool=function(e){if(e===void 0){e=null}if(e!==null){var i=String(e);var a=i in t._poolsMap?t._poolsMap[i]:null;if(a!==null&&a.length>0){a.length=0}}else{for(var r in t._poolsMap){var a=t._poolsMap[r];a.length=0}}};t.borrowObject=function(e){var i=String(e);var a=i in t._poolsMap?t._poolsMap[i]:null;if(a!==null&&a.length>0){var r=a.pop();r._isInPool=false;return r}var n=new e;n._onClear();return n};t.prototype.returnToPool=function(){this._onClear();t._returnObject(this)};t._hashCode=0;t._defaultMaxCount=3e3;t._maxCountMap={};t._poolsMap={};return t}();t.BaseObject=e})(dragonBones||(dragonBones={}));var dragonBones;(function(t){var e=function(){function t(t,e,i,a,r,n){if(t===void 0){t=1}if(e===void 0){e=0}if(i===void 0){i=0}if(a===void 0){a=1}if(r===void 0){r=0}if(n===void 0){n=0}this.a=t;this.b=e;this.c=i;this.d=a;this.tx=r;this.ty=n}t.prototype.toString=function(){return"[object dragonBones.Matrix] a:"+this.a+" b:"+this.b+" c:"+this.c+" d:"+this.d+" tx:"+this.tx+" ty:"+this.ty};t.prototype.copyFrom=function(t){this.a=t.a;this.b=t.b;this.c=t.c;this.d=t.d;this.tx=t.tx;this.ty=t.ty;return this};t.prototype.copyFromArray=function(t,e){if(e===void 0){e=0}this.a=t[e];this.b=t[e+1];this.c=t[e+2];this.d=t[e+3];this.tx=t[e+4];this.ty=t[e+5];return this};t.prototype.identity=function(){this.a=this.d=1;this.b=this.c=0;this.tx=this.ty=0;return this};t.prototype.concat=function(t){var e=this.a*t.a;var i=0;var a=0;var r=this.d*t.d;var n=this.tx*t.a+t.tx;var s=this.ty*t.d+t.ty;if(this.b!==0||this.c!==0){e+=this.b*t.c;i+=this.b*t.d;a+=this.c*t.a;r+=this.c*t.b}if(t.b!==0||t.c!==0){i+=this.a*t.b;a+=this.d*t.c;n+=this.ty*t.c;s+=this.tx*t.b}this.a=e;this.b=i;this.c=a;this.d=r;this.tx=n;this.ty=s;return this};t.prototype.invert=function(){var t=this.a;var e=this.b;var i=this.c;var a=this.d;var r=this.tx;var n=this.ty;if(e===0&&i===0){this.b=this.c=0;if(t===0||a===0){this.a=this.b=this.tx=this.ty=0}else{t=this.a=1/t;a=this.d=1/a;this.tx=-t*r;this.ty=-a*n}return this}var s=t*a-e*i;if(s===0){this.a=this.d=1;this.b=this.c=0;this.tx=this.ty=0;return this}s=1/s;var o=this.a=a*s;e=this.b=-e*s;i=this.c=-i*s;a=this.d=t*s;this.tx=-(o*r+i*n);this.ty=-(e*r+a*n);return this};t.prototype.transformPoint=function(t,e,i,a){if(a===void 0){a=false}i.x=this.a*t+this.c*e;i.y=this.b*t+this.d*e;if(!a){i.x+=this.tx;i.y+=this.ty}};t.prototype.transformRectangle=function(t,e){if(e===void 0){e=false}var i=this.a;var a=this.b;var r=this.c;var n=this.d;var s=e?0:this.tx;var o=e?0:this.ty;var l=t.x;var h=t.y;var u=l+t.width;var f=h+t.height;var _=i*l+r*h+s;var m=a*l+n*h+o;var p=i*u+r*h+s;var c=a*u+n*h+o;var d=i*u+r*f+s;var y=a*u+n*f+o;var v=i*l+r*f+s;var g=a*l+n*f+o;var b=0;if(_>p){b=_;_=p;p=b}if(d>v){b=d;d=v;v=b}t.x=Math.floor(_<d?_:d);t.width=Math.ceil((p>v?p:v)-t.x);if(m>c){b=m;m=c;c=b}if(y>g){b=y;y=g;g=b}t.y=Math.floor(m<y?m:y);t.height=Math.ceil((c>g?c:g)-t.y)};return t}();t.Matrix=e})(dragonBones||(dragonBones={}));var dragonBones;(function(t){var e=function(){function t(t,e,i,a,r,n){if(t===void 0){t=0}if(e===void 0){e=0}if(i===void 0){i=0}if(a===void 0){a=0}if(r===void 0){r=1}if(n===void 0){n=1}this.x=t;this.y=e;this.skew=i;this.rotation=a;this.scaleX=r;this.scaleY=n}t.normalizeRadian=function(t){t=(t+Math.PI)%(Math.PI*2);t+=t>0?-Math.PI:Math.PI;return t};t.prototype.toString=function(){return"[object dragonBones.Transform] x:"+this.x+" y:"+this.y+" skewX:"+this.skew*180/Math.PI+" skewY:"+this.rotation*180/Math.PI+" scaleX:"+this.scaleX+" scaleY:"+this.scaleY};t.prototype.copyFrom=function(t){this.x=t.x;this.y=t.y;this.skew=t.skew;this.rotation=t.rotation;this.scaleX=t.scaleX;this.scaleY=t.scaleY;return this};t.prototype.identity=function(){this.x=this.y=0;this.skew=this.rotation=0;this.scaleX=this.scaleY=1;return this};t.prototype.add=function(t){this.x+=t.x;this.y+=t.y;this.skew+=t.skew;this.rotation+=t.rotation;this.scaleX*=t.scaleX;this.scaleY*=t.scaleY;return this};t.prototype.minus=function(t){this.x-=t.x;this.y-=t.y;this.skew-=t.skew;this.rotation-=t.rotation;this.scaleX/=t.scaleX;this.scaleY/=t.scaleY;return this};t.prototype.fromMatrix=function(e){var i=this.scaleX,a=this.scaleY;var r=t.PI_Q;this.x=e.tx;this.y=e.ty;this.rotation=Math.atan(e.b/e.a);var n=Math.atan(-e.c/e.d);this.scaleX=this.rotation>-r&&this.rotation<r?e.a/Math.cos(this.rotation):e.b/Math.sin(this.rotation);this.scaleY=n>-r&&n<r?e.d/Math.cos(n):-e.c/Math.sin(n);if(i>=0&&this.scaleX<0){this.scaleX=-this.scaleX;this.rotation=this.rotation-Math.PI}if(a>=0&&this.scaleY<0){this.scaleY=-this.scaleY;n=n-Math.PI}this.skew=n-this.rotation;return this};t.prototype.toMatrix=function(t){if(this.skew!==0||this.rotation!==0){t.a=Math.cos(this.rotation);t.b=Math.sin(this.rotation);if(this.skew===0){t.c=-t.b;t.d=t.a}else{t.c=-Math.sin(this.skew+this.rotation);t.d=Math.cos(this.skew+this.rotation)}if(this.scaleX!==1){t.a*=this.scaleX;t.b*=this.scaleX}if(this.scaleY!==1){t.c*=this.scaleY;t.d*=this.scaleY}}else{t.a=this.scaleX;t.b=0;t.c=0;t.d=this.scaleY}t.tx=this.x;t.ty=this.y;return this};t.PI_D=Math.PI*2;t.PI_H=Math.PI/2;t.PI_Q=Math.PI/4;t.RAD_DEG=180/Math.PI;t.DEG_RAD=Math.PI/180;return t}();t.Transform=e})(dragonBones||(dragonBones={}));var dragonBones;(function(t){var e=function(){function t(t,e,i,a,r,n,s,o){if(t===void 0){t=1}if(e===void 0){e=1}if(i===void 0){i=1}if(a===void 0){a=1}if(r===void 0){r=0}if(n===void 0){n=0}if(s===void 0){s=0}if(o===void 0){o=0}this.alphaMultiplier=t;this.redMultiplier=e;this.greenMultiplier=i;this.blueMultiplier=a;this.alphaOffset=r;this.redOffset=n;this.greenOffset=s;this.blueOffset=o}t.prototype.copyFrom=function(t){this.alphaMultiplier=t.alphaMultiplier;this.redMultiplier=t.redMultiplier;this.greenMultiplier=t.greenMultiplier;this.blueMultiplier=t.blueMultiplier;this.alphaOffset=t.alphaOffset;this.redOffset=t.redOffset;this.greenOffset=t.greenOffset;this.blueOffset=t.blueOffset};t.prototype.identity=function(){this.alphaMultiplier=this.redMultiplier=this.greenMultiplier=this.blueMultiplier=1;this.alphaOffset=this.redOffset=this.greenOffset=this.blueOffset=0};return t}();t.ColorTransform=e})(dragonBones||(dragonBones={}));var dragonBones;(function(t){var e=function(){function t(t,e){if(t===void 0){t=0}if(e===void 0){e=0}this.x=t;this.y=e}t.prototype.copyFrom=function(t){this.x=t.x;this.y=t.y};t.prototype.clear=function(){this.x=this.y=0};return t}();t.Point=e})(dragonBones||(dragonBones={}));var dragonBones;(function(t){var e=function(){function t(t,e,i,a){if(t===void 0){t=0}if(e===void 0){e=0}if(i===void 0){i=0}if(a===void 0){a=0}this.x=t;this.y=e;this.width=i;this.height=a}t.prototype.copyFrom=function(t){this.x=t.x;this.y=t.y;this.width=t.width;this.height=t.height};t.prototype.clear=function(){this.x=this.y=0;this.width=this.height=0};return t}();t.Rectangle=e})(dragonBones||(dragonBones={}));var dragonBones;(function(t){var e=function(t){__extends(e,t);function e(){var e=t!==null&&t.apply(this,arguments)||this;e.ints=[];e.floats=[];e.strings=[];return e}e.toString=function(){return"[class dragonBones.UserData]"};e.prototype._onClear=function(){this.ints.length=0;this.floats.length=0;this.strings.length=0};e.prototype.addInt=function(t){this.ints.push(t)};e.prototype.addFloat=function(t){this.floats.push(t)};e.prototype.addString=function(t){this.strings.push(t)};e.prototype.getInt=function(t){if(t===void 0){t=0}return t>=0&&t<this.ints.length?this.ints[t]:0};e.prototype.getFloat=function(t){if(t===void 0){t=0}return t>=0&&t<this.floats.length?this.floats[t]:0};e.prototype.getString=function(t){if(t===void 0){t=0}return t>=0&&t<this.strings.length?this.strings[t]:""};return e}(t.BaseObject);t.UserData=e;var i=function(t){__extends(e,t);function e(){var e=t!==null&&t.apply(this,arguments)||this;e.data=null;return e}e.toString=function(){return"[class dragonBones.ActionData]"};e.prototype._onClear=function(){if(this.data!==null){this.data.returnToPool()}this.type=0;this.name="";this.bone=null;this.slot=null;this.data=null};return e}(t.BaseObject);t.ActionData=i})(dragonBones||(dragonBones={}));var dragonBones;(function(t){var e=function(t){__extends(e,t);function e(){var e=t!==null&&t.apply(this,arguments)||this;e.frameIndices=[];e.cachedFrames=[];e.armatureNames=[];e.armatures={};e.userData=null;return e}e.toString=function(){return"[class dragonBones.DragonBonesData]"};e.prototype._onClear=function(){for(var t in this.armatures){this.armatures[t].returnToPool();delete this.armatures[t]}if(this.userData!==null){this.userData.returnToPool()}this.autoSearch=false;this.frameRate=0;this.version="";this.name="";this.stage=null;this.frameIndices.length=0;this.cachedFrames.length=0;this.armatureNames.length=0;this.binary=null;this.intArray=null;this.floatArray=null;this.frameIntArray=null;this.frameFloatArray=null;this.frameArray=null;this.timelineArray=null;this.userData=null};e.prototype.addArmature=function(t){if(t.name in this.armatures){console.warn("Same armature: "+t.name);return}t.parent=this;this.armatures[t.name]=t;this.armatureNames.push(t.name)};e.prototype.getArmature=function(t){return t in this.armatures?this.armatures[t]:null};e.prototype.dispose=function(){console.warn("已废弃");this.returnToPool()};return e}(t.BaseObject);t.DragonBonesData=e})(dragonBones||(dragonBones={}));var dragonBones;(function(t){var e=function(e){__extends(i,e);function i(){var i=e!==null&&e.apply(this,arguments)||this;i.aabb=new t.Rectangle;i.animationNames=[];i.sortedBones=[];i.sortedSlots=[];i.defaultActions=[];i.actions=[];i.bones={};i.slots={};i.constraints={};i.skins={};i.animations={};i.canvas=null;i.userData=null;return i}i.toString=function(){return"[class dragonBones.ArmatureData]"};i.prototype._onClear=function(){for(var t=0,e=this.defaultActions;t<e.length;t++){var i=e[t];i.returnToPool()}for(var a=0,r=this.actions;a<r.length;a++){var i=r[a];i.returnToPool()}for(var n in this.bones){this.bones[n].returnToPool();delete this.bones[n]}for(var n in this.slots){this.slots[n].returnToPool();delete this.slots[n]}for(var n in this.constraints){this.constraints[n].returnToPool();delete this.constraints[n]}for(var n in this.skins){this.skins[n].returnToPool();delete this.skins[n]}for(var n in this.animations){this.animations[n].returnToPool();delete this.animations[n]}if(this.canvas!==null){this.canvas.returnToPool()}if(this.userData!==null){this.userData.returnToPool()}this.type=0;this.frameRate=0;this.cacheFrameRate=0;this.scale=1;this.name="";this.aabb.clear();this.animationNames.length=0;this.sortedBones.length=0;this.sortedSlots.length=0;this.defaultActions.length=0;this.actions.length=0;this.defaultSkin=null;this.defaultAnimation=null;this.canvas=null;this.userData=null;this.parent=null};i.prototype.sortBones=function(){var t=this.sortedBones.length;if(t<=0){return}var e=this.sortedBones.concat();var i=0;var a=0;this.sortedBones.length=0;while(a<t){var r=e[i++];if(i>=t){i=0}if(this.sortedBones.indexOf(r)>=0){continue}var n=false;for(var s in this.constraints){var o=this.constraints[s];if(o.bone===r&&this.sortedBones.indexOf(o.target)<0){n=true;break}}if(n){continue}if(r.parent!==null&&this.sortedBones.indexOf(r.parent)<0){continue}this.sortedBones.push(r);a++}};i.prototype.cacheFrames=function(t){if(this.cacheFrameRate>0){return}this.cacheFrameRate=t;for(var e in this.animations){this.animations[e].cacheFrames(this.cacheFrameRate)}};i.prototype.setCacheFrame=function(t,e){var i=this.parent.cachedFrames;var a=i.length;i.length+=10;i[a]=t.a;i[a+1]=t.b;i[a+2]=t.c;i[a+3]=t.d;i[a+4]=t.tx;i[a+5]=t.ty;i[a+6]=e.rotation;i[a+7]=e.skew;i[a+8]=e.scaleX;i[a+9]=e.scaleY;return a};i.prototype.getCacheFrame=function(t,e,i){var a=this.parent.cachedFrames;t.a=a[i];t.b=a[i+1];t.c=a[i+2];t.d=a[i+3];t.tx=a[i+4];t.ty=a[i+5];e.rotation=a[i+6];e.skew=a[i+7];e.scaleX=a[i+8];e.scaleY=a[i+9];e.x=t.tx;e.y=t.ty};i.prototype.addBone=function(t){if(t.name in this.bones){console.warn("Same bone: "+t.name);return}this.bones[t.name]=t;this.sortedBones.push(t)};i.prototype.addSlot=function(t){if(t.name in this.slots){console.warn("Same slot: "+t.name);return}this.slots[t.name]=t;this.sortedSlots.push(t)};i.prototype.addConstraint=function(t){if(t.name in this.constraints){console.warn("Same constraint: "+t.name);return}this.constraints[t.name]=t};i.prototype.addSkin=function(t){if(t.name in this.skins){console.warn("Same skin: "+t.name);return}t.parent=this;this.skins[t.name]=t;if(this.defaultSkin===null){this.defaultSkin=t}if(t.name==="default"){this.defaultSkin=t}};i.prototype.addAnimation=function(t){if(t.name in this.animations){console.warn("Same animation: "+t.name);return}t.parent=this;this.animations[t.name]=t;this.animationNames.push(t.name);if(this.defaultAnimation===null){this.defaultAnimation=t}};i.prototype.addAction=function(t,e){if(e){this.defaultActions.push(t)}else{this.actions.push(t)}};i.prototype.getBone=function(t){return t in this.bones?this.bones[t]:null};i.prototype.getSlot=function(t){return t in this.slots?this.slots[t]:null};i.prototype.getConstraint=function(t){return t in this.constraints?this.constraints[t]:null};i.prototype.getSkin=function(t){return t in this.skins?this.skins[t]:null};i.prototype.getAnimation=function(t){return t in this.animations?this.animations[t]:null};return i}(t.BaseObject);t.ArmatureData=e;var i=function(e){__extends(i,e);function i(){var i=e!==null&&e.apply(this,arguments)||this;i.transform=new t.Transform;i.userData=null;return i}i.toString=function(){return"[class dragonBones.BoneData]"};i.prototype._onClear=function(){if(this.userData!==null){this.userData.returnToPool()}this.inheritTranslation=false;this.inheritRotation=false;this.inheritScale=false;this.inheritReflection=false;this.length=0;this.name="";this.transform.identity();this.userData=null;this.parent=null};return i}(t.BaseObject);t.BoneData=i;var a=function(e){__extends(i,e);function i(){var t=e!==null&&e.apply(this,arguments)||this;t.color=null;t.userData=null;return t}i.createColor=function(){return new t.ColorTransform};i.toString=function(){return"[class dragonBones.SlotData]"};i.prototype._onClear=function(){if(this.userData!==null){this.userData.returnToPool()}this.blendMode=0;this.displayIndex=0;this.zOrder=0;this.name="";this.color=null;this.userData=null;this.parent=null};i.DEFAULT_COLOR=new t.ColorTransform;return i}(t.BaseObject);t.SlotData=a})(dragonBones||(dragonBones={}));var dragonBones;(function(t){var e=function(t){__extends(e,t);function e(){return t!==null&&t.apply(this,arguments)||this}e.toString=function(){return"[class dragonBones.CanvasData]"};e.prototype._onClear=function(){this.hasBackground=false;this.color=0;this.x=0;this.y=0;this.width=0;this.height=0};return e}(t.BaseObject);t.CanvasData=e})(dragonBones||(dragonBones={}));var dragonBones;(function(t){var e=function(t){__extends(e,t);function e(){var e=t!==null&&t.apply(this,arguments)||this;e.displays={};return e}e.toString=function(){return"[class dragonBones.SkinData]"};e.prototype._onClear=function(){for(var t in this.displays){var e=this.displays[t];for(var i=0,a=e;i<a.length;i++){var r=a[i];if(r!==null){r.returnToPool()}}delete this.displays[t]}this.name="";this.parent=null};e.prototype.addDisplay=function(t,e){if(!(t in this.displays)){this.displays[t]=[]}if(e!==null){e.parent=this}var i=this.displays[t];i.push(e)};e.prototype.getDisplay=function(t,e){var i=this.getDisplays(t);if(i!==null){for(var a=0,r=i;a<r.length;a++){var n=r[a];if(n!==null&&n.name===e){return n}}}return null};e.prototype.getDisplays=function(t){if(!(t in this.displays)){return null}return this.displays[t]};return e}(t.BaseObject);t.SkinData=e})(dragonBones||(dragonBones={}));var dragonBones;(function(t){var e=function(t){__extends(e,t);function e(){return t!==null&&t.apply(this,arguments)||this}e.prototype._onClear=function(){this.order=0;this.name="";this.target=null;this.bone=null;this.root=null};return e}(t.BaseObject);t.ConstraintData=e;var i=function(t){__extends(e,t);function e(){return t!==null&&t.apply(this,arguments)||this}e.toString=function(){return"[class dragonBones.IKConstraintData]"};e.prototype._onClear=function(){t.prototype._onClear.call(this);this.scaleEnabled=false;this.bendPositive=false;this.weight=1};return e}(e);t.IKConstraintData=i})(dragonBones||(dragonBones={}));var dragonBones;(function(t){var e=function(e){__extends(i,e);function i(){var i=e!==null&&e.apply(this,arguments)||this;i.transform=new t.Transform;return i}i.prototype._onClear=function(){this.name="";this.path="";this.transform.identity();this.parent=null};return i}(t.BaseObject);t.DisplayData=e;var i=function(e){__extends(i,e);function i(){var i=e!==null&&e.apply(this,arguments)||this;i.pivot=new t.Point;return i}i.toString=function(){return"[class dragonBones.ImageDisplayData]"};i.prototype._onClear=function(){e.prototype._onClear.call(this);this.type=0;this.pivot.clear();this.texture=null};return i}(e);t.ImageDisplayData=i;var a=function(t){__extends(e,t);function e(){var e=t!==null&&t.apply(this,arguments)||this;e.actions=[];return e}e.toString=function(){return"[class dragonBones.ArmatureDisplayData]"};e.prototype._onClear=function(){t.prototype._onClear.call(this);for(var e=0,i=this.actions;e<i.length;e++){var a=i[e];a.returnToPool()}this.type=1;this.inheritAnimation=false;this.actions.length=0;this.armature=null};e.prototype.addAction=function(t){this.actions.push(t)};return e}(e);t.ArmatureDisplayData=a;var r=function(t){__extends(e,t);function e(){var e=t!==null&&t.apply(this,arguments)||this;e.weight=null;return e}e.toString=function(){return"[class dragonBones.MeshDisplayData]"};e.prototype._onClear=function(){t.prototype._onClear.call(this);if(this.weight!==null){this.weight.returnToPool()}this.type=2;this.inheritAnimation=false;this.offset=0;this.weight=null};return e}(i);t.MeshDisplayData=r;var n=function(t){__extends(e,t);function e(){var e=t!==null&&t.apply(this,arguments)||this;e.boundingBox=null;return e}e.toString=function(){return"[class dragonBones.BoundingBoxDisplayData]"};e.prototype._onClear=function(){t.prototype._onClear.call(this);if(this.boundingBox!==null){this.boundingBox.returnToPool()}this.type=3;this.boundingBox=null};return e}(e);t.BoundingBoxDisplayData=n;var s=function(t){__extends(e,t);function e(){var e=t!==null&&t.apply(this,arguments)||this;e.bones=[];return e}e.toString=function(){return"[class dragonBones.WeightData]"};e.prototype._onClear=function(){this.count=0;this.offset=0;this.bones.length=0};e.prototype.addBone=function(t){this.bones.push(t)};return e}(t.BaseObject);t.WeightData=s})(dragonBones||(dragonBones={}));var dragonBones;(function(t){var e=function(t){__extends(e,t);function e(){return t!==null&&t.apply(this,arguments)||this}e.prototype._onClear=function(){this.color=0;this.width=0;this.height=0};return e}(t.BaseObject);t.BoundingBoxData=e;var i=function(t){__extends(e,t);function e(){return t!==null&&t.apply(this,arguments)||this}e.toString=function(){return"[class dragonBones.RectangleBoundingBoxData]"};e._computeOutCode=function(t,e,i,a,r,n){var s=0;if(t<i){s|=1}else if(t>r){s|=2}if(e<a){s|=4}else if(e>n){s|=8}return s};e.rectangleIntersectsSegment=function(t,i,a,r,n,s,o,l,h,u,f){if(h===void 0){h=null}if(u===void 0){u=null}if(f===void 0){f=null}var _=t>n&&t<o&&i>s&&i<l;var m=a>n&&a<o&&r>s&&r<l;if(_&&m){return-1}var p=0;var c=e._computeOutCode(t,i,n,s,o,l);var d=e._computeOutCode(a,r,n,s,o,l);while(true){if((c|d)===0){p=2;break}else if((c&d)!==0){break}var y=0;var v=0;var g=0;var b=c!==0?c:d;if((b&4)!==0){y=t+(a-t)*(s-i)/(r-i);v=s;if(f!==null){g=-Math.PI*.5}}else if((b&8)!==0){y=t+(a-t)*(l-i)/(r-i);v=l;if(f!==null){g=Math.PI*.5}}else if((b&2)!==0){v=i+(r-i)*(o-t)/(a-t);y=o;if(f!==null){g=0}}else if((b&1)!==0){v=i+(r-i)*(n-t)/(a-t);y=n;if(f!==null){g=Math.PI}}if(b===c){t=y;i=v;c=e._computeOutCode(t,i,n,s,o,l);if(f!==null){f.x=g}}else{a=y;r=v;d=e._computeOutCode(a,r,n,s,o,l);if(f!==null){f.y=g}}}if(p){if(_){p=2;if(h!==null){h.x=a;h.y=r}if(u!==null){u.x=a;u.y=a}if(f!==null){f.x=f.y+Math.PI}}else if(m){p=1;if(h!==null){h.x=t;h.y=i}if(u!==null){u.x=t;u.y=i}if(f!==null){f.y=f.x+Math.PI}}else{p=3;if(h!==null){h.x=t;h.y=i}if(u!==null){u.x=a;u.y=r}}}return p};e.prototype._onClear=function(){t.prototype._onClear.call(this);this.type=0};e.prototype.containsPoint=function(t,e){var i=this.width*.5;if(t>=-i&&t<=i){var a=this.height*.5;if(e>=-a&&e<=a){return true}}return false};e.prototype.intersectsSegment=function(t,i,a,r,n,s,o){if(n===void 0){n=null}if(s===void 0){s=null}if(o===void 0){o=null}var l=this.width*.5;var h=this.height*.5;var u=e.rectangleIntersectsSegment(t,i,a,r,-l,-h,l,h,n,s,o);return u};return e}(e);t.RectangleBoundingBoxData=i;var a=function(t){__extends(e,t);function e(){return t!==null&&t.apply(this,arguments)||this}e.toString=function(){return"[class dragonBones.EllipseData]"};e.ellipseIntersectsSegment=function(t,e,i,a,r,n,s,o,l,h,u){if(l===void 0){l=null}if(h===void 0){h=null}if(u===void 0){u=null}var f=s/o;var _=f*f;e*=f;a*=f;var m=i-t;var p=a-e;var c=Math.sqrt(m*m+p*p);var d=m/c;var y=p/c;var v=(r-t)*d+(n-e)*y;var g=v*v;var b=t*t+e*e;var T=s*s;var A=T-b+g;var D=0;if(A>=0){var O=Math.sqrt(A);var S=v-O;var x=v+O;var B=S<0?-1:S<=c?0:1;var P=x<0?-1:x<=c?0:1;var M=B*P;if(M<0){return-1}else if(M===0){if(B===-1){D=2;i=t+x*d;a=(e+x*y)/f;if(l!==null){l.x=i;l.y=a}if(h!==null){h.x=i;h.y=a}if(u!==null){u.x=Math.atan2(a/T*_,i/T);u.y=u.x+Math.PI}}else if(P===1){D=1;t=t+S*d;e=(e+S*y)/f;if(l!==null){l.x=t;l.y=e}if(h!==null){h.x=t;h.y=e}if(u!==null){u.x=Math.atan2(e/T*_,t/T);u.y=u.x+Math.PI}}else{D=3;if(l!==null){l.x=t+S*d;l.y=(e+S*y)/f;if(u!==null){u.x=Math.atan2(l.y/T*_,l.x/T)}}if(h!==null){h.x=t+x*d;h.y=(e+x*y)/f;if(u!==null){u.y=Math.atan2(h.y/T*_,h.x/T)}}}}}return D};e.prototype._onClear=function(){t.prototype._onClear.call(this);this.type=1};e.prototype.containsPoint=function(t,e){var i=this.width*.5;if(t>=-i&&t<=i){var a=this.height*.5;if(e>=-a&&e<=a){e*=i/a;return Math.sqrt(t*t+e*e)<=i}}return false};e.prototype.intersectsSegment=function(t,i,a,r,n,s,o){if(n===void 0){n=null}if(s===void 0){s=null}if(o===void 0){o=null}var l=e.ellipseIntersectsSegment(t,i,a,r,0,0,this.width*.5,this.height*.5,n,s,o);return l};return e}(e);t.EllipseBoundingBoxData=a;var r=function(t){__extends(e,t);function e(){var e=t!==null&&t.apply(this,arguments)||this;e.vertices=[];e.weight=null;return e}e.toString=function(){return"[class dragonBones.PolygonBoundingBoxData]"};e.polygonIntersectsSegment=function(t,e,i,a,r,n,s,o){if(n===void 0){n=null}if(s===void 0){s=null}if(o===void 0){o=null}if(t===i){t=i+1e-6}if(e===a){e=a+1e-6}var l=r.length;var h=t-i;var u=e-a;var f=t*a-e*i;var _=0;var m=r[l-2];var p=r[l-1];var c=0;var d=0;var y=0;var v=0;var g=0;var b=0;for(var T=0;T<l;T+=2){var A=r[T];var D=r[T+1];if(m===A){m=A+1e-4}if(p===D){p=D+1e-4}var O=m-A;var S=p-D;var x=m*D-p*A;var B=h*S-u*O;var P=(f*O-h*x)/B;if((P>=m&&P<=A||P>=A&&P<=m)&&(h===0||P>=t&&P<=i||P>=i&&P<=t)){var M=(f*S-u*x)/B;if((M>=p&&M<=D||M>=D&&M<=p)&&(u===0||M>=e&&M<=a||M>=a&&M<=e)){if(s!==null){var E=P-t;if(E<0){E=-E}if(_===0){c=E;d=E;y=P;v=M;g=P;b=M;if(o!==null){o.x=Math.atan2(D-p,A-m)-Math.PI*.5;o.y=o.x}}else{if(E<c){c=E;y=P;v=M;if(o!==null){o.x=Math.atan2(D-p,A-m)-Math.PI*.5}}if(E>d){d=E;g=P;b=M;if(o!==null){o.y=Math.atan2(D-p,A-m)-Math.PI*.5}}}_++}else{y=P;v=M;g=P;b=M;_++;if(o!==null){o.x=Math.atan2(D-p,A-m)-Math.PI*.5;o.y=o.x}break}}}m=A;p=D}if(_===1){if(n!==null){n.x=y;n.y=v}if(s!==null){s.x=y;s.y=v}if(o!==null){o.y=o.x+Math.PI}}else if(_>1){_++;if(n!==null){n.x=y;n.y=v}if(s!==null){s.x=g;s.y=b}}return _};e.prototype._onClear=function(){t.prototype._onClear.call(this);if(this.weight!==null){this.weight.returnToPool()}this.type=2;this.x=0;this.y=0;this.vertices.length=0;this.weight=null};e.prototype.containsPoint=function(t,e){var i=false;if(t>=this.x&&t<=this.width&&e>=this.y&&e<=this.height){for(var a=0,r=this.vertices.length,n=r-2;a<r;a+=2){var s=this.vertices[n+1];var o=this.vertices[a+1];if(o<e&&s>=e||s<e&&o>=e){var l=this.vertices[n];var h=this.vertices[a];if((e-o)*(l-h)/(s-o)+h<t){i=!i}}n=a}}return i};e.prototype.intersectsSegment=function(t,a,r,n,s,o,l){if(s===void 0){s=null}if(o===void 0){o=null}if(l===void 0){l=null}var h=0;if(i.rectangleIntersectsSegment(t,a,r,n,this.x,this.y,this.width,this.height,null,null,null)!==0){h=e.polygonIntersectsSegment(t,a,r,n,this.vertices,s,o,l)}return h};return e}(e);t.PolygonBoundingBoxData=r})(dragonBones||(dragonBones={}));var dragonBones;(function(t){var e=function(t){__extends(e,t);function e(){var e=t!==null&&t.apply(this,arguments)||this;e.cachedFrames=[];e.boneTimelines={};e.slotTimelines={};e.constraintTimelines={};e.boneCachedFrameIndices={};e.slotCachedFrameIndices={};e.actionTimeline=null;e.zOrderTimeline=null;return e}e.toString=function(){return"[class dragonBones.AnimationData]"};e.prototype._onClear=function(){for(var t in this.boneTimelines){for(var e=0,i=this.boneTimelines[t];e<i.length;e++){var a=i[e];a.returnToPool()}delete this.boneTimelines[t]}for(var t in this.slotTimelines){for(var r=0,n=this.slotTimelines[t];r<n.length;r++){var a=n[r];a.returnToPool()}delete this.slotTimelines[t]}for(var t in this.constraintTimelines){for(var s=0,o=this.constraintTimelines[t];s<o.length;s++){var a=o[s];a.returnToPool()}delete this.constraintTimelines[t]}for(var t in this.boneCachedFrameIndices){delete this.boneCachedFrameIndices[t]}for(var t in this.slotCachedFrameIndices){delete this.slotCachedFrameIndices[t]}if(this.actionTimeline!==null){this.actionTimeline.returnToPool()}if(this.zOrderTimeline!==null){this.zOrderTimeline.returnToPool()}this.frameIntOffset=0;this.frameFloatOffset=0;this.frameOffset=0;this.frameCount=0;this.playTimes=0;this.duration=0;this.scale=1;this.fadeInTime=0;this.cacheFrameRate=0;this.name="";this.cachedFrames.length=0;this.actionTimeline=null;this.zOrderTimeline=null;this.parent=null};e.prototype.cacheFrames=function(t){if(this.cacheFrameRate>0){return}this.cacheFrameRate=Math.max(Math.ceil(t*this.scale),1);var e=Math.ceil(this.cacheFrameRate*this.duration)+1;this.cachedFrames.length=e;for(var i=0,a=this.cacheFrames.length;i<a;++i){this.cachedFrames[i]=false}for(var r=0,n=this.parent.sortedBones;r<n.length;r++){var s=n[r];var o=new Array(e);for(var i=0,a=o.length;i<a;++i){o[i]=-1}this.boneCachedFrameIndices[s.name]=o}for(var l=0,h=this.parent.sortedSlots;l<h.length;l++){var u=h[l];var o=new Array(e);for(var i=0,a=o.length;i<a;++i){o[i]=-1}this.slotCachedFrameIndices[u.name]=o}};e.prototype.addBoneTimeline=function(t,e){var i=t.name in this.boneTimelines?this.boneTimelines[t.name]:this.boneTimelines[t.name]=[];if(i.indexOf(e)<0){i.push(e)}};e.prototype.addSlotTimeline=function(t,e){var i=t.name in this.slotTimelines?this.slotTimelines[t.name]:this.slotTimelines[t.name]=[];if(i.indexOf(e)<0){i.push(e)}};e.prototype.addConstraintTimeline=function(t,e){var i=t.name in this.constraintTimelines?this.constraintTimelines[t.name]:this.constraintTimelines[t.name]=[];if(i.indexOf(e)<0){i.push(e)}};e.prototype.getBoneTimelines=function(t){return t in this.boneTimelines?this.boneTimelines[t]:null};e.prototype.getSlotTimelines=function(t){return t in this.slotTimelines?this.slotTimelines[t]:null};e.prototype.getConstraintTimelines=function(t){return t in this.constraintTimelines?this.constraintTimelines[t]:null};e.prototype.getBoneCachedFrameIndices=function(t){return t in this.boneCachedFrameIndices?this.boneCachedFrameIndices[t]:null};e.prototype.getSlotCachedFrameIndices=function(t){return t in this.slotCachedFrameIndices?this.slotCachedFrameIndices[t]:null};return e}(t.BaseObject);t.AnimationData=e;var i=function(t){__extends(e,t);function e(){return t!==null&&t.apply(this,arguments)||this}e.toString=function(){return"[class dragonBones.TimelineData]"};e.prototype._onClear=function(){this.type=10;this.offset=0;this.frameIndicesOffset=-1};return e}(t.BaseObject);t.TimelineData=i})(dragonBones||(dragonBones={}));var dragonBones;(function(t){var e=function(t){__extends(e,t);function e(){var e=t!==null&&t.apply(this,arguments)||this;e.boneMask=[];return e}e.toString=function(){return"[class dragonBones.AnimationConfig]"};e.prototype._onClear=function(){this.pauseFadeOut=true;this.fadeOutMode=4;this.fadeOutTweenType=1;this.fadeOutTime=-1;this.actionEnabled=true;this.additiveBlending=false;this.displayControl=true;this.pauseFadeIn=true;this.resetToPose=true;this.fadeInTweenType=1;this.playTimes=-1;this.layer=0;this.position=0;this.duration=-1;this.timeScale=-100;this.weight=1;this.fadeInTime=-1;this.autoFadeOutTime=-1;this.name="";this.animation="";this.group="";this.boneMask.length=0};e.prototype.clear=function(){this._onClear()};e.prototype.copyFrom=function(t){this.pauseFadeOut=t.pauseFadeOut;this.fadeOutMode=t.fadeOutMode;this.autoFadeOutTime=t.autoFadeOutTime;this.fadeOutTweenType=t.fadeOutTweenType;this.actionEnabled=t.actionEnabled;this.additiveBlending=t.additiveBlending;this.displayControl=t.displayControl;this.pauseFadeIn=t.pauseFadeIn;this.resetToPose=t.resetToPose;this.playTimes=t.playTimes;this.layer=t.layer;this.position=t.position;this.duration=t.duration;this.timeScale=t.timeScale;this.fadeInTime=t.fadeInTime;this.fadeOutTime=t.fadeOutTime;this.fadeInTweenType=t.fadeInTweenType;this.weight=t.weight;this.name=t.name;this.animation=t.animation;this.group=t.group;this.boneMask.length=t.boneMask.length;for(var e=0,i=this.boneMask.length;e<i;++e){this.boneMask[e]=t.boneMask[e]}};e.prototype.containsBoneMask=function(t){return this.boneMask.length===0||this.boneMask.indexOf(t)>=0};e.prototype.addBoneMask=function(t,e,i){if(i===void 0){i=true}var a=t.getBone(e);if(a===null){return}if(this.boneMask.indexOf(e)<0){this.boneMask.push(e)}if(i){for(var r=0,n=t.getBones();r<n.length;r++){var s=n[r];if(this.boneMask.indexOf(s.name)<0&&a.contains(s)){this.boneMask.push(s.name)}}}};e.prototype.removeBoneMask=function(t,e,i){if(i===void 0){i=true}var a=this.boneMask.indexOf(e);if(a>=0){this.boneMask.splice(a,1)}if(i){var r=t.getBone(e);if(r!==null){if(this.boneMask.length>0){for(var n=0,s=t.getBones();n<s.length;n++){var o=s[n];var l=this.boneMask.indexOf(o.name);if(l>=0&&r.contains(o)){this.boneMask.splice(l,1)}}}else{for(var h=0,u=t.getBones();h<u.length;h++){var o=u[h];if(o===r){continue}if(!r.contains(o)){this.boneMask.push(o.name)}}}}}};return e}(t.BaseObject);t.AnimationConfig=e})(dragonBones||(dragonBones={}));var dragonBones;(function(t){var e=function(t){__extends(e,t);function e(){var e=t!==null&&t.apply(this,arguments)||this;e.textures={};return e}e.prototype._onClear=function(){for(var t in this.textures){this.textures[t].returnToPool();delete this.textures[t]}this.autoSearch=false;this.width=0;this.height=0;this.scale=1;this.name="";this.imagePath=""};e.prototype.copyFrom=function(t){this.autoSearch=t.autoSearch;this.scale=t.scale;this.width=t.width;this.height=t.height;this.name=t.name;this.imagePath=t.imagePath;for(var e in this.textures){this.textures[e].returnToPool();delete this.textures[e]}for(var e in t.textures){var i=this.createTexture();i.copyFrom(t.textures[e]);this.textures[e]=i}};e.prototype.addTexture=function(t){if(t.name in this.textures){console.warn("Same texture: "+t.name);return}t.parent=this;this.textures[t.name]=t};e.prototype.getTexture=function(t){return t in this.textures?this.textures[t]:null};return e}(t.BaseObject);t.TextureAtlasData=e;var i=function(e){__extends(i,e);function i(){var i=e!==null&&e.apply(this,arguments)||this;i.region=new t.Rectangle;i.frame=null;return i}i.createRectangle=function(){return new t.Rectangle};i.prototype._onClear=function(){this.rotated=false;this.name="";this.region.clear();this.parent=null;this.frame=null};i.prototype.copyFrom=function(t){this.rotated=t.rotated;this.name=t.name;this.region.copyFrom(t.region);this.parent=t.parent;if(this.frame===null&&t.frame!==null){this.frame=i.createRectangle()}else if(this.frame!==null&&t.frame===null){this.frame=null}if(this.frame!==null&&t.frame!==null){this.frame.copyFrom(t.frame)}};return i}(t.BaseObject);t.TextureData=i})(dragonBones||(dragonBones={}));var dragonBones;(function(t){var e=function(e){__extends(i,e);function i(){var t=e!==null&&e.apply(this,arguments)||this;t._bones=[];t._slots=[];t._constraints=[];t._actions=[];t._animation=null;t._proxy=null;t._replaceTextureAtlasData=null;t._clock=null;return t}i.toString=function(){return"[class dragonBones.Armature]"};i._onSortSlots=function(t,e){return t._zOrder>e._zOrder?1:-1};i.prototype._onClear=function(){if(this._clock!==null){this._clock.remove(this)}for(var t=0,e=this._bones;t<e.length;t++){var i=e[t];i.returnToPool()}for(var a=0,r=this._slots;a<r.length;a++){var n=r[a];n.returnToPool()}for(var s=0,o=this._constraints;s<o.length;s++){var l=o[s];l.returnToPool()}if(this._animation!==null){this._animation.returnToPool()}if(this._proxy!==null){this._proxy.dbClear()}if(this._replaceTextureAtlasData!==null){this._replaceTextureAtlasData.returnToPool()}this.inheritAnimation=true;this.userData=null;this._lockUpdate=false;this._bonesDirty=false;this._slotsDirty=false;this._zOrderDirty=false;this._flipX=false;this._flipY=false;this._cacheFrameIndex=-1;this._bones.length=0;this._slots.length=0;this._constraints.length=0;this._actions.length=0;this._armatureData=null;this._animation=null;this._proxy=null;this._display=null;this._replaceTextureAtlasData=null;this._replacedTexture=null;this._dragonBones=null;this._clock=null;this._parent=null};i.prototype._sortBones=function(){var t=this._bones.length;if(t<=0){return}var e=this._bones.concat();var i=0;var a=0;this._bones.length=0;while(a<t){var r=e[i++];if(i>=t){i=0}if(this._bones.indexOf(r)>=0){continue}if(r._hasConstraint){var n=false;for(var s=0,o=this._constraints;s<o.length;s++){var l=o[s];if(l._bone===r&&this._bones.indexOf(l._target)<0){n=true;break}}if(n){continue}}if(r.parent!==null&&this._bones.indexOf(r.parent)<0){continue}this._bones.push(r);a++}};i.prototype._sortSlots=function(){this._slots.sort(i._onSortSlots)};i.prototype._sortZOrder=function(t,e){var i=this._armatureData.sortedSlots;var a=t===null;if(this._zOrderDirty||!a){for(var r=0,n=i.length;r<n;++r){var s=a?r:t[e+r];if(s<0||s>=n){continue}var o=i[s];var l=this.getSlot(o.name);if(l!==null){l._setZorder(r)}}this._slotsDirty=true;this._zOrderDirty=!a}};i.prototype._addBoneToBoneList=function(t){if(this._bones.indexOf(t)<0){this._bonesDirty=true;this._bones.push(t)}};i.prototype._removeBoneFromBoneList=function(t){var e=this._bones.indexOf(t);if(e>=0){this._bones.splice(e,1)}};i.prototype._addSlotToSlotList=function(t){if(this._slots.indexOf(t)<0){this._slotsDirty=true;this._slots.push(t)}};i.prototype._removeSlotFromSlotList=function(t){var e=this._slots.indexOf(t);if(e>=0){this._slots.splice(e,1)}};i.prototype._bufferAction=function(t,e){if(this._actions.indexOf(t)<0){if(e){this._actions.push(t)}else{this._actions.unshift(t)}}};i.prototype.dispose=function(){if(this._armatureData!==null){this._lockUpdate=true;this._dragonBones.bufferObject(this)}};i.prototype.init=function(e,i,a,r){if(this._armatureData!==null){return}this._armatureData=e;this._animation=t.BaseObject.borrowObject(t.Animation);this._proxy=i;this._display=a;this._dragonBones=r;this._proxy.dbInit(this);this._animation.init(this);this._animation.animations=this._armatureData.animations};i.prototype.advanceTime=function(t){if(this._lockUpdate){return}if(this._armatureData===null){console.warn("The armature has been disposed.");return}else if(this._armatureData.parent===null){console.warn("The armature data has been disposed.\nPlease make sure dispose armature before call factory.clear().");return}var e=this._cacheFrameIndex;this._animation.advanceTime(t);if(this._bonesDirty){this._bonesDirty=false;this._sortBones()}if(this._slotsDirty){this._slotsDirty=false;this._sortSlots()}if(this._cacheFrameIndex<0||this._cacheFrameIndex!==e){var i=0,a=0;for(i=0,a=this._bones.length;i<a;++i){this._bones[i].update(this._cacheFrameIndex)}for(i=0,a=this._slots.length;i<a;++i){this._slots[i].update(this._cacheFrameIndex)}}if(this._actions.length>0){this._lockUpdate=true;for(var r=0,n=this._actions;r<n.length;r++){var s=n[r];if(s.type===0){this._animation.fadeIn(s.name)}}this._actions.length=0;this._lockUpdate=false}this._proxy.dbUpdate()};i.prototype.invalidUpdate=function(t,e){if(t===void 0){t=null}if(e===void 0){e=false}if(t!==null&&t.length>0){var i=this.getBone(t);if(i!==null){i.invalidUpdate();if(e){for(var a=0,r=this._slots;a<r.length;a++){var n=r[a];if(n.parent===i){n.invalidUpdate()}}}}}else{for(var s=0,o=this._bones;s<o.length;s++){var i=o[s];i.invalidUpdate()}if(e){for(var l=0,h=this._slots;l<h.length;l++){var n=h[l];n.invalidUpdate()}}}};i.prototype.containsPoint=function(t,e){for(var i=0,a=this._slots;i<a.length;i++){var r=a[i];if(r.containsPoint(t,e)){return r}}return null};i.prototype.intersectsSegment=function(t,e,i,a,r,n,s){if(r===void 0){r=null}if(n===void 0){n=null}if(s===void 0){s=null}var o=t===i;var l=0;var h=0;var u=0;var f=0;var _=0;var m=0;var p=0;var c=0;var d=null;var y=null;for(var v=0,g=this._slots;v<g.length;v++){var b=g[v];var T=b.intersectsSegment(t,e,i,a,r,n,s);if(T>0){if(r!==null||n!==null){if(r!==null){var A=o?r.y-e:r.x-t;if(A<0){A=-A}if(d===null||A<l){l=A;u=r.x;f=r.y;d=b;if(s){p=s.x}}}if(n!==null){var A=n.x-t;if(A<0){A=-A}if(y===null||A>h){h=A;_=n.x;m=n.y;y=b;if(s!==null){c=s.y}}}}else{d=b;break}}}if(d!==null&&r!==null){r.x=u;r.y=f;if(s!==null){s.x=p}}if(y!==null&&n!==null){n.x=_;n.y=m;if(s!==null){s.y=c}}return d};i.prototype.getBone=function(t){for(var e=0,i=this._bones;e<i.length;e++){var a=i[e];if(a.name===t){return a}}return null};i.prototype.getBoneByDisplay=function(t){var e=this.getSlotByDisplay(t);return e!==null?e.parent:null};i.prototype.getSlot=function(t){for(var e=0,i=this._slots;e<i.length;e++){var a=i[e];if(a.name===t){return a}}return null};i.prototype.getSlotByDisplay=function(t){if(t!==null){for(var e=0,i=this._slots;e<i.length;e++){var a=i[e];if(a.display===t){return a}}}return null};i.prototype.addBone=function(t,e){console.assert(t!==null);t._setArmature(this);t._setParent(e.length>0?this.getBone(e):null)};i.prototype.addSlot=function(t,e){var i=this.getBone(e);console.assert(t!==null&&i!==null);t._setArmature(this);t._setParent(i)};i.prototype.addConstraint=function(t){if(this._constraints.indexOf(t)<0){this._constraints.push(t)}};i.prototype.removeBone=function(t){console.assert(t!==null&&t.armature===this);t._setParent(null);t._setArmature(null)};i.prototype.removeSlot=function(t){console.assert(t!==null&&t.armature===this);t._setParent(null);t._setArmature(null)};i.prototype.getBones=function(){return this._bones};i.prototype.getSlots=function(){return this._slots};Object.defineProperty(i.prototype,"flipX",{get:function(){return this._flipX},set:function(t){if(this._flipX===t){return}this._flipX=t;this.invalidUpdate()},enumerable:true,configurable:true});Object.defineProperty(i.prototype,"flipY",{get:function(){return this._flipY},set:function(t){if(this._flipY===t){return}this._flipY=t;this.invalidUpdate()},enumerable:true,configurable:true});Object.defineProperty(i.prototype,"cacheFrameRate",{get:function(){return this._armatureData.cacheFrameRate},set:function(t){if(this._armatureData.cacheFrameRate!==t){this._armatureData.cacheFrames(t);for(var e=0,i=this._slots;e<i.length;e++){var a=i[e];var r=a.childArmature;if(r!==null){r.cacheFrameRate=t}}}},enumerable:true,configurable:true});Object.defineProperty(i.prototype,"name",{get:function(){return this._armatureData.name},enumerable:true,configurable:true});Object.defineProperty(i.prototype,"armatureData",{get:function(){return this._armatureData},enumerable:true,configurable:true});Object.defineProperty(i.prototype,"animation",{get:function(){return this._animation},enumerable:true,configurable:true});Object.defineProperty(i.prototype,"proxy",{get:function(){return this._proxy},enumerable:true,configurable:true});Object.defineProperty(i.prototype,"eventDispatcher",{get:function(){return this._proxy},enumerable:true,configurable:true});Object.defineProperty(i.prototype,"display",{get:function(){return this._display},enumerable:true,configurable:true});Object.defineProperty(i.prototype,"replacedTexture",{get:function(){return this._replacedTexture},set:function(t){if(this._replacedTexture===t){return}if(this._replaceTextureAtlasData!==null){this._replaceTextureAtlasData.returnToPool();this._replaceTextureAtlasData=null}this._replacedTexture=t;for(var e=0,i=this._slots;e<i.length;e++){var a=i[e];a.invalidUpdate();a.update(-1)}},enumerable:true,configurable:true});Object.defineProperty(i.prototype,"clock",{get:function(){return this._clock},set:function(t){if(this._clock===t){return}if(this._clock!==null){this._clock.remove(this)}this._clock=t;if(this._clock){this._clock.add(this)}for(var e=0,i=this._slots;e<i.length;e++){var a=i[e];var r=a.childArmature;if(r!==null){r.clock=this._clock}}},enumerable:true,configurable:true});Object.defineProperty(i.prototype,"parent",{get:function(){return this._parent},enumerable:true,configurable:true});i.prototype.replaceTexture=function(t){this.replacedTexture=t};i.prototype.hasEventListener=function(t){return this._proxy.hasDBEventListener(t)};i.prototype.addEventListener=function(t,e,i){this._proxy.addDBEventListener(t,e,i)};i.prototype.removeEventListener=function(t,e,i){this._proxy.removeDBEventListener(t,e,i)};i.prototype.enableAnimationCache=function(t){this.cacheFrameRate=t};i.prototype.getDisplay=function(){return this._display};return i}(t.BaseObject);t.Armature=e})(dragonBones||(dragonBones={}));var dragonBones;(function(t){var e=function(e){__extends(i,e);function i(){var i=e!==null&&e.apply(this,arguments)||this;i.globalTransformMatrix=new t.Matrix;i.global=new t.Transform;i.offset=new t.Transform;return i}i.prototype._onClear=function(){this.globalTransformMatrix.identity();this.global.identity();this.offset.identity();this.origin=null;this.userData=null;this._globalDirty=false;this._armature=null;this._parent=null};i.prototype._setArmature=function(t){this._armature=t};i.prototype._setParent=function(t){this._parent=t};i.prototype.updateGlobalTransform=function(){if(this._globalDirty){this._globalDirty=false;this.global.fromMatrix(this.globalTransformMatrix)}};Object.defineProperty(i.prototype,"armature",{get:function(){return this._armature},enumerable:true,configurable:true});Object.defineProperty(i.prototype,"parent",{get:function(){return this._parent},enumerable:true,configurable:true});i._helpMatrix=new t.Matrix;i._helpTransform=new t.Transform;i._helpPoint=new t.Point;return i}(t.BaseObject);t.TransformObject=e})(dragonBones||(dragonBones={}));var dragonBones;(function(t){var e=function(e){__extends(i,e);function i(){var i=e!==null&&e.apply(this,arguments)||this;i.animationPose=new t.Transform;return i}i.toString=function(){return"[class dragonBones.Bone]"};i.prototype._onClear=function(){e.prototype._onClear.call(this);this.offsetMode=1;this.animationPose.identity();this._transformDirty=false;this._childrenTransformDirty=false;this._blendDirty=false;this._localDirty=true;this._hasConstraint=false;this._visible=true;this._cachedFrameIndex=-1;this._blendLayer=0;this._blendLeftWeight=1;this._blendLayerWeight=0;this._boneData=null;this._cachedFrameIndices=null};i.prototype._updateGlobalTransformMatrix=function(e){var i=this._armature.flipX;var a=this._armature.flipY===t.DragonBones.yDown;var r=this._parent!==null;var n=0;var s=this.global;var o=this.globalTransformMatrix;if(this.offsetMode===1){s.x=this.origin.x+this.offset.x+this.animationPose.x;s.y=this.origin.y+this.offset.y+this.animationPose.y;s.skew=this.origin.skew+this.offset.skew+this.animationPose.skew;s.rotation=this.origin.rotation+this.offset.rotation+this.animationPose.rotation;s.scaleX=this.origin.scaleX*this.offset.scaleX*this.animationPose.scaleX;s.scaleY=this.origin.scaleY*this.offset.scaleY*this.animationPose.scaleY}else if(this.offsetMode===0){s.copyFrom(this.origin).add(this.animationPose)}else{r=false;s.copyFrom(this.offset)}if(r){var l=this._parent.globalTransformMatrix;if(this._boneData.inheritScale){if(!this._boneData.inheritRotation){this._parent.updateGlobalTransform();if(i&&a){n=s.rotation-(this._parent.global.rotation+Math.PI)}else if(i){n=s.rotation+this._parent.global.rotation+Math.PI}else if(a){n=s.rotation+this._parent.global.rotation}else{n=s.rotation-this._parent.global.rotation}s.rotation=n}s.toMatrix(o);o.concat(l);if(this._boneData.inheritTranslation){s.x=o.tx;s.y=o.ty}else{o.tx=s.x;o.ty=s.y}if(e){s.fromMatrix(o)}else{this._globalDirty=true}}else{if(this._boneData.inheritTranslation){var h=s.x;var u=s.y;s.x=l.a*h+l.c*u+l.tx;s.y=l.d*u+l.b*h+l.ty}else{if(i){s.x=-s.x}if(a){s.y=-s.y}}if(this._boneData.inheritRotation){this._parent.updateGlobalTransform();if(this._parent.global.scaleX<0){n=s.rotation+this._parent.global.rotation+Math.PI}else{n=s.rotation+this._parent.global.rotation}if(l.a*l.d-l.b*l.c<0){n-=s.rotation*2;if(i!==a||this._boneData.inheritReflection){s.skew+=Math.PI}}s.rotation=n}else if(i||a){if(i&&a){n=s.rotation+Math.PI}else{if(i){n=Math.PI-s.rotation}else{n=-s.rotation}s.skew+=Math.PI}s.rotation=n}s.toMatrix(o)}}else{if(i||a){if(i){s.x=-s.x}if(a){s.y=-s.y}if(i&&a){n=s.rotation+Math.PI}else{if(i){n=Math.PI-s.rotation}else{n=-s.rotation}s.skew+=Math.PI}s.rotation=n}s.toMatrix(o)}};i.prototype._setArmature=function(t){if(this._armature===t){return}var e=null;var i=null;if(this._armature!==null){e=this.getSlots();i=this.getBones();this._armature._removeBoneFromBoneList(this)}this._armature=t;if(this._armature!==null){this._armature._addBoneToBoneList(this)}if(e!==null){for(var a=0,r=e;a<r.length;a++){var n=r[a];if(n.parent===this){n._setArmature(this._armature)}}}if(i!==null){for(var s=0,o=i;s<o.length;s++){var l=o[s];if(l.parent===this){l._setArmature(this._armature)}}}};i.prototype.init=function(t){if(this._boneData!==null){return}this._boneData=t;this.origin=this._boneData.transform};i.prototype.update=function(t){this._blendDirty=false;if(t>=0&&this._cachedFrameIndices!==null){var e=this._cachedFrameIndices[t];if(e>=0&&this._cachedFrameIndex===e){this._transformDirty=false}else if(e>=0){this._transformDirty=true;this._cachedFrameIndex=e}else{if(this._hasConstraint){for(var i=0,a=this._armature._constraints;i<a.length;i++){var r=a[i];if(r._bone===this){r.update()}}}if(this._transformDirty||this._parent!==null&&this._parent._childrenTransformDirty){this._transformDirty=true;this._cachedFrameIndex=-1}else if(this._cachedFrameIndex>=0){this._transformDirty=false;this._cachedFrameIndices[t]=this._cachedFrameIndex}else{this._transformDirty=true;this._cachedFrameIndex=-1}}}else{if(this._hasConstraint){for(var n=0,s=this._armature._constraints;n<s.length;n++){var r=s[n];if(r._bone===this){r.update()}}}if(this._transformDirty||this._parent!==null&&this._parent._childrenTransformDirty){t=-1;this._transformDirty=true;this._cachedFrameIndex=-1}}if(this._transformDirty){this._transformDirty=false;this._childrenTransformDirty=true;if(this._cachedFrameIndex<0){var o=t>=0;if(this._localDirty){this._updateGlobalTransformMatrix(o)}if(o&&this._cachedFrameIndices!==null){this._cachedFrameIndex=this._cachedFrameIndices[t]=this._armature._armatureData.setCacheFrame(this.globalTransformMatrix,this.global)}}else{this._armature._armatureData.getCacheFrame(this.globalTransformMatrix,this.global,this._cachedFrameIndex)}}else if(this._childrenTransformDirty){this._childrenTransformDirty=false}this._localDirty=true};i.prototype.updateByConstraint=function(){if(this._localDirty){this._localDirty=false;if(this._transformDirty||this._parent!==null&&this._parent._childrenTransformDirty){this._updateGlobalTransformMatrix(true)}this._transformDirty=true}};i.prototype.invalidUpdate=function(){this._transformDirty=true};i.prototype.contains=function(t){if(t===this){return false}var e=t;while(e!==this&&e!==null){e=e.parent}return e===this};Object.defineProperty(i.prototype,"boneData",{get:function(){return this._boneData},enumerable:true,configurable:true});Object.defineProperty(i.prototype,"visible",{get:function(){return this._visible},set:function(t){if(this._visible===t){return}this._visible=t;for(var e=0,i=this._armature.getSlots();e<i.length;e++){var a=i[e];if(a._parent===this){a._updateVisible()}}},enumerable:true,configurable:true});Object.defineProperty(i.prototype,"name",{get:function(){return this._boneData.name},enumerable:true,configurable:true});i.prototype.getBones=function(){var t=new Array;for(var e=0,i=this._armature.getBones();e<i.length;e++){var a=i[e];if(a.parent===this){t.push(a)}}return t};i.prototype.getSlots=function(){var t=new Array;for(var e=0,i=this._armature.getSlots();e<i.length;e++){var a=i[e];if(a.parent===this){t.push(a)}}return t};Object.defineProperty(i.prototype,"slot",{get:function(){for(var t=0,e=this._armature.getSlots();t<e.length;t++){var i=e[t];if(i.parent===this){return i}}return null},enumerable:true,configurable:true});return i}(t.TransformObject);t.Bone=e})(dragonBones||(dragonBones={}));var dragonBones;(function(t){var e=function(e){__extends(i,e);function i(){var i=e!==null&&e.apply(this,arguments)||this;i._localMatrix=new t.Matrix;i._colorTransform=new t.ColorTransform;i._ffdVertices=[];i._displayDatas=[];i._displayList=[];i._meshBones=[];i._rawDisplay=null;i._meshDisplay=null;return i}i.prototype._onClear=function(){e.prototype._onClear.call(this);var i=[];for(var a=0,r=this._displayList;a<r.length;a++){var n=r[a];if(n!==null&&n!==this._rawDisplay&&n!==this._meshDisplay&&i.indexOf(n)<0){i.push(n)}}for(var s=0,o=i;s<o.length;s++){var n=o[s];if(n instanceof t.Armature){n.dispose()}else{this._disposeDisplay(n)}}if(this._meshDisplay!==null&&this._meshDisplay!==this._rawDisplay){this._disposeDisplay(this._meshDisplay)}if(this._rawDisplay!==null){this._disposeDisplay(this._rawDisplay)}this.displayController=null;this._displayDirty=false;this._zOrderDirty=false;this._blendModeDirty=false;this._colorDirty=false;this._meshDirty=false;this._transformDirty=false;this._visible=true;this._blendMode=0;this._displayIndex=-1;this._animationDisplayIndex=-1;this._zOrder=0;this._cachedFrameIndex=-1;this._pivotX=0;this._pivotY=0;this._localMatrix.identity();this._colorTransform.identity();this._ffdVertices.length=0;this._displayList.length=0;this._displayDatas.length=0;this._meshBones.length=0;this._slotData=null;this._rawDisplayDatas=null;this._displayData=null;this._textureData=null;this._meshData=null;this._boundingBoxData=null;this._rawDisplay=null;this._meshDisplay=null;this._display=null;this._childArmature=null;this._cachedFrameIndices=null};i.prototype._updateDisplayData=function(){var t=this._displayData;var e=this._textureData;var a=this._meshData;var r=this._displayIndex>=0&&this._rawDisplayDatas!==null&&this._displayIndex<this._rawDisplayDatas.length?this._rawDisplayDatas[this._displayIndex]:null;if(this._displayIndex>=0&&this._displayIndex<this._displayDatas.length){this._displayData=this._displayDatas[this._displayIndex]}else{this._displayData=null}if(this._displayData!==null){if(this._displayData.type===0||this._displayData.type===2){this._textureData=this._displayData.texture;if(this._displayData.type===2){this._meshData=this._displayData}else if(r!==null&&r.type===2){this._meshData=r}else{this._meshData=null}}else{this._textureData=null;this._meshData=null}}else{this._textureData=null;this._meshData=null}if(this._displayData!==null&&this._displayData.type===3){this._boundingBoxData=this._displayData.boundingBox}else if(r!==null&&r.type===3){this._boundingBoxData=r.boundingBox}else{this._boundingBoxData=null}if(this._displayData!==t||this._textureData!==e||this._meshData!==a){if(this._meshData!==null){this._pivotX=0;this._pivotY=0}else if(this._textureData!==null){var n=this._displayData;var s=this._textureData.parent.scale*this._armature._armatureData.scale;var o=this._textureData.frame;this._pivotX=n.pivot.x;this._pivotY=n.pivot.y;var l=o!==null?o:this._textureData.region;var h=l.width;var u=l.height;if(this._textureData.rotated&&o===null){h=l.height;u=l.width}this._pivotX*=h*s;this._pivotY*=u*s;if(o!==null){this._pivotX+=o.x*s;this._pivotY+=o.y*s}}else{this._pivotX=0;this._pivotY=0}if(this._meshData!==a){if(this._meshData!==null){if(this._meshData.weight!==null){this._ffdVertices.length=this._meshData.weight.count*2;this._meshBones.length=this._meshData.weight.bones.length;for(var f=0,_=this._meshBones.length;f<_;++f){this._meshBones[f]=this._armature.getBone(this._meshData.weight.bones[f].name)}}else{var m=this._meshData.parent.parent.parent.intArray[this._meshData.offset+0];this._ffdVertices.length=m*2;this._meshBones.length=0}for(var f=0,_=this._ffdVertices.length;f<_;++f){this._ffdVertices[f]=0}this._meshDirty=true}else{this._ffdVertices.length=0;this._meshBones.length=0}}else if(this._meshData!==null&&this._textureData!==e){this._meshDirty=true}if(this._displayData!==null&&r!==null&&this._displayData!==r&&this._meshData===null){r.transform.toMatrix(i._helpMatrix);i._helpMatrix.invert();i._helpMatrix.transformPoint(0,0,i._helpPoint);this._pivotX-=i._helpPoint.x;this._pivotY-=i._helpPoint.y;this._displayData.transform.toMatrix(i._helpMatrix);i._helpMatrix.invert();i._helpMatrix.transformPoint(0,0,i._helpPoint);this._pivotX+=i._helpPoint.x;this._pivotY+=i._helpPoint.y}if(r!==null){this.origin=r.transform}else if(this._displayData!==null){this.origin=this._displayData.transform}this._displayDirty=true;this._transformDirty=true}};i.prototype._updateDisplay=function(){var e=this._display!==null?this._display:this._rawDisplay;var i=this._childArmature;if(this._displayIndex>=0&&this._displayIndex<this._displayList.length){this._display=this._displayList[this._displayIndex];if(this._display!==null&&this._display instanceof t.Armature){this._childArmature=this._display;this._display=this._childArmature.display}else{this._childArmature=null}}else{this._display=null;this._childArmature=null}var a=this._display!==null?this._display:this._rawDisplay;if(a!==e){this._onUpdateDisplay();this._replaceDisplay(e);this._visibleDirty=true;this._blendModeDirty=true;this._colorDirty=true}if(a===this._rawDisplay||a===this._meshDisplay){this._updateFrame()}if(this._childArmature!==i){if(i!==null){i._parent=null;i.clock=null;if(i.inheritAnimation){i.animation.reset()}}if(this._childArmature!==null){this._childArmature._parent=this;this._childArmature.clock=this._armature.clock;if(this._childArmature.inheritAnimation){if(this._childArmature.cacheFrameRate===0){var r=this._armature.cacheFrameRate;if(r!==0){this._childArmature.cacheFrameRate=r}}var n=null;if(this._displayData!==null&&this._displayData.type===1){n=this._displayData.actions}else{var s=this._displayIndex>=0&&this._rawDisplayDatas!==null&&this._displayIndex<this._rawDisplayDatas.length?this._rawDisplayDatas[this._displayIndex]:null;if(s!==null&&s.type===1){n=s.actions}}if(n!==null&&n.length>0){for(var o=0,l=n;o<l.length;o++){var h=l[o];this._childArmature._bufferAction(h,false)}}else{this._childArmature.animation.play()}}}}};i.prototype._updateGlobalTransformMatrix=function(t){this.globalTransformMatrix.copyFrom(this._localMatrix);this.globalTransformMatrix.concat(this._parent.globalTransformMatrix);if(t){this.global.fromMatrix(this.globalTransformMatrix)}else{this._globalDirty=true}};i.prototype._isMeshBonesUpdate=function(){for(var t=0,e=this._meshBones;t<e.length;t++){var i=e[t];if(i!==null&&i._childrenTransformDirty){return true}}return false};i.prototype._setArmature=function(t){if(this._armature===t){return}if(this._armature!==null){this._armature._removeSlotFromSlotList(this)}this._armature=t;this._onUpdateDisplay();if(this._armature!==null){this._armature._addSlotToSlotList(this);this._addDisplay()}else{this._removeDisplay()}};i.prototype._setDisplayIndex=function(t,e){if(e===void 0){e=false}if(e){if(this._animationDisplayIndex===t){return false}this._animationDisplayIndex=t}if(this._displayIndex===t){return false}this._displayIndex=t;this._displayDirty=true;this._updateDisplayData();return this._displayDirty};i.prototype._setZorder=function(t){if(this._zOrder===t){}this._zOrder=t;this._zOrderDirty=true;return this._zOrderDirty};i.prototype._setColor=function(t){this._colorTransform.copyFrom(t);this._colorDirty=true;return this._colorDirty};i.prototype._setDisplayList=function(e){if(e!==null&&e.length>0){if(this._displayList.length!==e.length){this._displayList.length=e.length}for(var i=0,a=e.length;i<a;++i){var r=e[i];if(r!==null&&r!==this._rawDisplay&&r!==this._meshDisplay&&!(r instanceof t.Armature)&&this._displayList.indexOf(r)<0){this._initDisplay(r)}this._displayList[i]=r}}else if(this._displayList.length>0){this._displayList.length=0}if(this._displayIndex>=0&&this._displayIndex<this._displayList.length){this._displayDirty=this._display!==this._displayList[this._displayIndex]}else{this._displayDirty=this._display!==null}this._updateDisplayData();return this._displayDirty};i.prototype.init=function(t,e,i,a){if(this._slotData!==null){return}this._slotData=t;this._visibleDirty=true;this._blendModeDirty=true;this._colorDirty=true;this._blendMode=this._slotData.blendMode;this._zOrder=this._slotData.zOrder;this._colorTransform.copyFrom(this._slotData.color);this._rawDisplay=i;this._meshDisplay=a;this.rawDisplayDatas=e};i.prototype.update=function(t){if(this._displayDirty){this._displayDirty=false;this._updateDisplay();if(this._transformDirty){if(this.origin!==null){this.global.copyFrom(this.origin).add(this.offset).toMatrix(this._localMatrix)}else{this.global.copyFrom(this.offset).toMatrix(this._localMatrix)}}}if(this._zOrderDirty){this._zOrderDirty=false;this._updateZOrder()}if(t>=0&&this._cachedFrameIndices!==null){var e=this._cachedFrameIndices[t];if(e>=0&&this._cachedFrameIndex===e){this._transformDirty=false}else if(e>=0){this._transformDirty=true;this._cachedFrameIndex=e}else if(this._transformDirty||this._parent._childrenTransformDirty){this._transformDirty=true;this._cachedFrameIndex=-1}else if(this._cachedFrameIndex>=0){this._transformDirty=false;this._cachedFrameIndices[t]=this._cachedFrameIndex}else{this._transformDirty=true;this._cachedFrameIndex=-1}}else if(this._transformDirty||this._parent._childrenTransformDirty){t=-1;this._transformDirty=true;this._cachedFrameIndex=-1}if(this._display===null){return}if(this._visibleDirty){this._visibleDirty=false;this._updateVisible()}if(this._blendModeDirty){this._blendModeDirty=false;this._updateBlendMode()}if(this._colorDirty){this._colorDirty=false;this._updateColor()}if(this._meshData!==null&&this._display===this._meshDisplay){var i=this._meshData.weight!==null;if(this._meshDirty||i&&this._isMeshBonesUpdate()){this._meshDirty=false;this._updateMesh()}if(i){if(this._transformDirty){this._transformDirty=false;this._updateTransform(true)}return}}if(this._transformDirty){this._transformDirty=false;if(this._cachedFrameIndex<0){var a=t>=0;this._updateGlobalTransformMatrix(a);if(a&&this._cachedFrameIndices!==null){this._cachedFrameIndex=this._cachedFrameIndices[t]=this._armature._armatureData.setCacheFrame(this.globalTransformMatrix,this.global)}}else{this._armature._armatureData.getCacheFrame(this.globalTransformMatrix,this.global,this._cachedFrameIndex)}this._updateTransform(false)}};i.prototype.updateTransformAndMatrix=function(){if(this._transformDirty){this._transformDirty=false;this._updateGlobalTransformMatrix(false)}};i.prototype.replaceDisplayData=function(t,e){if(e===void 0){e=-1}if(e<0){if(this._displayIndex<0){e=0}else{e=this._displayIndex}}if(this._displayDatas.length<=e){this._displayDatas.length=e+1;for(var i=0,a=this._displayDatas.length;i<a;++i){if(!this._displayDatas[i]){this._displayDatas[i]=null}}}this._displayDatas[e]=t};i.prototype.containsPoint=function(t,e){if(this._boundingBoxData===null){return false}this.updateTransformAndMatrix();i._helpMatrix.copyFrom(this.globalTransformMatrix);i._helpMatrix.invert();i._helpMatrix.transformPoint(t,e,i._helpPoint);return this._boundingBoxData.containsPoint(i._helpPoint.x,i._helpPoint.y)};i.prototype.intersectsSegment=function(t,e,a,r,n,s,o){if(n===void 0){n=null}if(s===void 0){s=null}if(o===void 0){o=null}if(this._boundingBoxData===null){return 0}this.updateTransformAndMatrix();i._helpMatrix.copyFrom(this.globalTransformMatrix);i._helpMatrix.invert();i._helpMatrix.transformPoint(t,e,i._helpPoint);t=i._helpPoint.x;e=i._helpPoint.y;i._helpMatrix.transformPoint(a,r,i._helpPoint);a=i._helpPoint.x;r=i._helpPoint.y;var l=this._boundingBoxData.intersectsSegment(t,e,a,r,n,s,o);if(l>0){if(l===1||l===2){if(n!==null){this.globalTransformMatrix.transformPoint(n.x,n.y,n);if(s!==null){s.x=n.x;s.y=n.y}}else if(s!==null){this.globalTransformMatrix.transformPoint(s.x,s.y,s)}}else{if(n!==null){this.globalTransformMatrix.transformPoint(n.x,n.y,n)}if(s!==null){this.globalTransformMatrix.transformPoint(s.x,s.y,s)}}if(o!==null){this.globalTransformMatrix.transformPoint(Math.cos(o.x),Math.sin(o.x),i._helpPoint,true);o.x=Math.atan2(i._helpPoint.y,i._helpPoint.x);this.globalTransformMatrix.transformPoint(Math.cos(o.y),Math.sin(o.y),i._helpPoint,true);o.y=Math.atan2(i._helpPoint.y,i._helpPoint.x)}}return l};i.prototype.invalidUpdate=function(){this._displayDirty=true;this._transformDirty=true};Object.defineProperty(i.prototype,"visible",{get:function(){return this._visible},set:function(t){if(this._visible===t){return}this._visible=t;this._updateVisible()},enumerable:true,configurable:true});Object.defineProperty(i.prototype,"displayIndex",{get:function(){return this._displayIndex},set:function(t){if(this._setDisplayIndex(t)){this.update(-1)}},enumerable:true,configurable:true});Object.defineProperty(i.prototype,"name",{get:function(){return this._slotData.name},enumerable:true,configurable:true});Object.defineProperty(i.prototype,"displayList",{get:function(){return this._displayList.concat()},set:function(e){var i=this._displayList.concat();var a=new Array;if(this._setDisplayList(e)){this.update(-1)}for(var r=0,n=i;r<n.length;r++){var s=n[r];if(s!==null&&s!==this._rawDisplay&&s!==this._meshDisplay&&this._displayList.indexOf(s)<0&&a.indexOf(s)<0){a.push(s)}}for(var o=0,l=a;o<l.length;o++){var s=l[o];if(s instanceof t.Armature){s.dispose()}else{this._disposeDisplay(s)}}},enumerable:true,configurable:true});Object.defineProperty(i.prototype,"slotData",{get:function(){return this._slotData},enumerable:true,configurable:true});Object.defineProperty(i.prototype,"rawDisplayDatas",{get:function(){return this._rawDisplayDatas},set:function(t){if(this._rawDisplayDatas===t){return}this._displayDirty=true;this._rawDisplayDatas=t;if(this._rawDisplayDatas){this._displayDatas.length=this._rawDisplayDatas.length;for(var e=0,i=this._displayDatas.length;e<i;++e){this._displayDatas[e]=this._rawDisplayDatas[e]}}else{this._displayDatas.length=0}},enumerable:true,configurable:true});Object.defineProperty(i.prototype,"boundingBoxData",{get:function(){return this._boundingBoxData},enumerable:true,configurable:true});Object.defineProperty(i.prototype,"rawDisplay",{get:function(){return this._rawDisplay},enumerable:true,configurable:true});Object.defineProperty(i.prototype,"meshDisplay",{get:function(){return this._meshDisplay},enumerable:true,configurable:true});Object.defineProperty(i.prototype,"display",{get:function(){return this._display},set:function(t){if(this._display===t){return}var e=this._displayList.length;if(this._displayIndex<0&&e===0){this._displayIndex=0}if(this._displayIndex<0){return}else{var i=this.displayList;if(e<=this._displayIndex){i.length=this._displayIndex+1}i[this._displayIndex]=t;this.displayList=i}},enumerable:true,configurable:true});Object.defineProperty(i.prototype,"childArmature",{get:function(){return this._childArmature},set:function(t){if(this._childArmature===t){return}this.display=t},enumerable:true,configurable:true});i.prototype.getDisplay=function(){return this.display};i.prototype.setDisplay=function(t){this.display=t};return i}(t.TransformObject);t.Slot=e})(dragonBones||(dragonBones={}));var dragonBones;(function(t){var e=function(e){__extends(i,e);function i(){return e!==null&&e.apply(this,arguments)||this}i.prototype._onClear=function(){this._armature=null;this._target=null;this._bone=null;this._root=null};Object.defineProperty(i.prototype,"name",{get:function(){return this._constraintData.name},enumerable:true,configurable:true});i._helpMatrix=new t.Matrix;i._helpTransform=new t.Transform;i._helpPoint=new t.Point;return i}(t.BaseObject);t.Constraint=e;var i=function(e){__extends(i,e);function i(){return e!==null&&e.apply(this,arguments)||this}i.toString=function(){return"[class dragonBones.IKConstraint]"};i.prototype._onClear=function(){e.prototype._onClear.call(this);this._scaleEnabled=false;this._bendPositive=false;this._weight=1;this._constraintData=null};i.prototype._computeA=function(){var t=this._target.global;var e=this._bone.global;var i=this._bone.globalTransformMatrix;var a=Math.atan2(t.y-e.y,t.x-e.x);if(e.scaleX<0){a+=Math.PI}e.rotation+=(a-e.rotation)*this._weight;e.toMatrix(i)};i.prototype._computeB=function(){var e=this._bone._boneData.length;var i=this._root;var a=this._target.global;var r=i.global;var n=this._bone.global;var s=this._bone.globalTransformMatrix;var o=s.a*e;var l=s.b*e;var h=o*o+l*l;var u=Math.sqrt(h);var f=n.x-r.x;var _=n.y-r.y;var m=f*f+_*_;var p=Math.sqrt(m);var c=n.rotation;var d=r.rotation;var y=Math.atan2(_,f);f=a.x-r.x;_=a.y-r.y;var v=f*f+_*_;var g=Math.sqrt(v);var b=0;if(u+p<=g||g+u<=p||g+p<=u){b=Math.atan2(a.y-r.y,a.x-r.x);if(u+p<=g){}else if(p<u){b+=Math.PI}}else{var T=(m-h+v)/(2*v);var A=Math.sqrt(m-T*T*v)/g;var D=r.x+f*T;var O=r.y+_*T;var S=-_*A;var x=f*A;var B=false;if(i._parent!==null){var P=i._parent.globalTransformMatrix;B=P.a*P.d-P.b*P.c<0}if(B!==this._bendPositive){n.x=D-S;n.y=O-x}else{n.x=D+S;n.y=O+x}b=Math.atan2(n.y-r.y,n.x-r.x)}var M=t.Transform.normalizeRadian(b-y);r.rotation=d+M*this._weight;r.toMatrix(i.globalTransformMatrix);var E=y+M*this._weight;n.x=r.x+Math.cos(E)*p;n.y=r.y+Math.sin(E)*p;var I=Math.atan2(a.y-n.y,a.x-n.x);if(n.scaleX<0){I+=Math.PI}n.rotation=r.rotation+c-d+t.Transform.normalizeRadian(I-M-c)*this._weight;n.toMatrix(s)};i.prototype.init=function(t,e){if(this._constraintData!==null){return}this._constraintData=t;this._armature=e;this._target=this._armature.getBone(this._constraintData.target.name);this._bone=this._armature.getBone(this._constraintData.bone.name);this._root=this._constraintData.root!==null?this._armature.getBone(this._constraintData.root.name):null;{var i=this._constraintData;this._scaleEnabled=i.scaleEnabled;this._bendPositive=i.bendPositive;this._weight=i.weight}this._bone._hasConstraint=true};i.prototype.update=function(){if(this._root===null){this._bone.updateByConstraint();this._computeA()}else{this._root.updateByConstraint();this._bone.updateByConstraint();this._computeB()}};i.prototype.invalidUpdate=function(){if(this._root===null){this._bone.invalidUpdate()}else{this._root.invalidUpdate();this._bone.invalidUpdate()}};return i}(e);t.IKConstraint=i})(dragonBones||(dragonBones={}));var dragonBones;(function(t){var e=function(){function t(t){if(t===void 0){t=-1}this.time=0;this.timeScale=1;this._animatebles=[];this._clock=null;if(t<0){this.time=(new Date).getTime()*.001}else{this.time=t}}t.prototype.advanceTime=function(t){if(t!==t){t=0}if(t<0){t=(new Date).getTime()*.001-this.time}if(this.timeScale!==1){t*=this.timeScale}if(t<0){this.time-=t}else{this.time+=t}if(t===0){return}var e=0,i=0,a=this._animatebles.length;for(;e<a;++e){var r=this._animatebles[e];if(r!==null){if(i>0){this._animatebles[e-i]=r;this._animatebles[e]=null}r.advanceTime(t)}else{i++}}if(i>0){a=this._animatebles.length;for(;e<a;++e){var n=this._animatebles[e];if(n!==null){this._animatebles[e-i]=n}else{i++}}this._animatebles.length-=i}};t.prototype.contains=function(t){if(t===this){return false}var e=t;while(e!==this&&e!==null){e=e.clock}return e===this};t.prototype.add=function(t){if(this._animatebles.indexOf(t)<0){this._animatebles.push(t);t.clock=this}};t.prototype.remove=function(t){var e=this._animatebles.indexOf(t);if(e>=0){this._animatebles[e]=null;t.clock=null}};t.prototype.clear=function(){for(var t=0,e=this._animatebles;t<e.length;t++){var i=e[t];if(i!==null){i.clock=null}}};Object.defineProperty(t.prototype,"clock",{get:function(){return this._clock},set:function(t){if(this._clock===t){return}if(this._clock!==null){this._clock.remove(this)}this._clock=t;if(this._clock!==null){this._clock.add(this)}},enumerable:true,configurable:true});t.clock=new t;return t}();t.WorldClock=e})(dragonBones||(dragonBones={}));var dragonBones;(function(t){var e=function(e){__extends(i,e);function i(){var t=e!==null&&e.apply(this,arguments)||this;t._animationNames=[];t._animationStates=[];t._animations={};t._animationConfig=null;return t}i.toString=function(){return"[class dragonBones.Animation]"};i.prototype._onClear=function(){for(var t=0,e=this._animationStates;t<e.length;t++){var i=e[t];i.returnToPool()}for(var a in this._animations){delete this._animations[a]}if(this._animationConfig!==null){this._animationConfig.returnToPool()}this.timeScale=1;this._animationDirty=false;this._inheritTimeScale=1;this._animationNames.length=0;this._animationStates.length=0;this._armature=null;this._animationConfig=null;this._lastAnimationState=null};i.prototype._fadeOut=function(t){switch(t.fadeOutMode){case 1:for(var e=0,i=this._animationStates;e<i.length;e++){var a=i[e];if(a.layer===t.layer){a.fadeOut(t.fadeOutTime,t.pauseFadeOut)}}break;case 2:for(var r=0,n=this._animationStates;r<n.length;r++){var a=n[r];if(a.group===t.group){a.fadeOut(t.fadeOutTime,t.pauseFadeOut)}}break;case 3:for(var s=0,o=this._animationStates;s<o.length;s++){var a=o[s];if(a.layer===t.layer&&a.group===t.group){a.fadeOut(t.fadeOutTime,t.pauseFadeOut)}}break;case 4:for(var l=0,h=this._animationStates;l<h.length;l++){var a=h[l];a.fadeOut(t.fadeOutTime,t.pauseFadeOut)}break;case 0:case 5:default:break}};i.prototype.init=function(e){if(this._armature!==null){return}this._armature=e;this._animationConfig=t.BaseObject.borrowObject(t.AnimationConfig)};i.prototype.advanceTime=function(t){if(t<0){t=-t}if(this._armature.inheritAnimation&&this._armature._parent!==null){this._inheritTimeScale=this._armature._parent._armature.animation._inheritTimeScale*this.timeScale}else{this._inheritTimeScale=this.timeScale}if(this._inheritTimeScale!==1){t*=this._inheritTimeScale}var e=this._animationStates.length;if(e===1){var i=this._animationStates[0];if(i._fadeState>0&&i._subFadeState>0){this._armature._dragonBones.bufferObject(i);this._animationStates.length=0;this._lastAnimationState=null}else{var a=i._animationData;var r=a.cacheFrameRate;if(this._animationDirty&&r>0){this._animationDirty=false;for(var n=0,s=this._armature.getBones();n<s.length;n++){var o=s[n];o._cachedFrameIndices=a.getBoneCachedFrameIndices(o.name)}for(var l=0,h=this._armature.getSlots();l<h.length;l++){var u=h[l];u._cachedFrameIndices=a.getSlotCachedFrameIndices(u.name)}}i.advanceTime(t,r)}}else if(e>1){for(var f=0,_=0;f<e;++f){var i=this._animationStates[f];if(i._fadeState>0&&i._subFadeState>0){_++;this._armature._dragonBones.bufferObject(i);this._animationDirty=true;if(this._lastAnimationState===i){this._lastAnimationState=null}}else{if(_>0){this._animationStates[f-_]=i}i.advanceTime(t,0)}if(f===e-1&&_>0){this._animationStates.length-=_;if(this._lastAnimationState===null&&this._animationStates.length>0){this._lastAnimationState=this._animationStates[this._animationStates.length-1]}}}this._armature._cacheFrameIndex=-1}else{this._armature._cacheFrameIndex=-1}};i.prototype.reset=function(){for(var t=0,e=this._animationStates;t<e.length;t++){var i=e[t];i.returnToPool()}this._animationDirty=false;this._animationConfig.clear();this._animationStates.length=0;this._lastAnimationState=null};i.prototype.stop=function(t){if(t===void 0){t=null}if(t!==null){var e=this.getState(t);if(e!==null){e.stop()}}else{for(var i=0,a=this._animationStates;i<a.length;i++){var e=a[i];e.stop()}}};i.prototype.playConfig=function(e){var i=e.animation;if(!(i in this._animations)){console.warn("Non-existent animation.\n","DragonBones name: "+this._armature.armatureData.parent.name,"Armature name: "+this._armature.name,"Animation name: "+i);return null}var a=this._animations[i];if(e.fadeOutMode===5){for(var r=0,n=this._animationStates;r<n.length;r++){var s=n[r];if(s._animationData===a){return s}}}if(this._animationStates.length===0){e.fadeInTime=0}else if(e.fadeInTime<0){e.fadeInTime=a.fadeInTime}if(e.fadeOutTime<0){e.fadeOutTime=e.fadeInTime}if(e.timeScale<=-100){e.timeScale=1/a.scale}if(a.frameCount>1){if(e.position<0){e.position%=a.duration;e.position=a.duration-e.position}else if(e.position===a.duration){e.position-=1e-6}else if(e.position>a.duration){e.position%=a.duration}if(e.duration>0&&e.position+e.duration>a.duration){e.duration=a.duration-e.position}if(e.playTimes<0){e.playTimes=a.playTimes}}else{e.playTimes=1;e.position=0;if(e.duration>0){e.duration=0}}if(e.duration===0){e.duration=-1}this._fadeOut(e);var o=t.BaseObject.borrowObject(t.AnimationState);o.init(this._armature,a,e);this._animationDirty=true;this._armature._cacheFrameIndex=-1;if(this._animationStates.length>0){var l=false;for(var h=0,u=this._animationStates.length;h<u;++h){if(o.layer>=this._animationStates[h].layer){}else{l=true;this._animationStates.splice(h+1,0,o);break}}if(!l){this._animationStates.push(o)}}else{this._animationStates.push(o)}for(var f=0,_=this._armature.getSlots();f<_.length;f++){var m=_[f];var p=m.childArmature;if(p!==null&&p.inheritAnimation&&p.animation.hasAnimation(i)&&p.animation.getState(i)===null){p.animation.fadeIn(i)}}if(e.fadeInTime<=0){this._armature.advanceTime(0)}this._lastAnimationState=o;return o};i.prototype.play=function(t,e){if(t===void 0){t=null}if(e===void 0){e=-1}this._animationConfig.clear();this._animationConfig.resetToPose=true;this._animationConfig.playTimes=e;this._animationConfig.fadeInTime=0;this._animationConfig.animation=t!==null?t:"";if(t!==null&&t.length>0){this.playConfig(this._animationConfig)}else if(this._lastAnimationState===null){var i=this._armature.armatureData.defaultAnimation;if(i!==null){this._animationConfig.animation=i.name;this.playConfig(this._animationConfig)}}else if(!this._lastAnimationState.isPlaying&&!this._lastAnimationState.isCompleted){this._lastAnimationState.play()}else{this._animationConfig.animation=this._lastAnimationState.name;this.playConfig(this._animationConfig)}return this._lastAnimationState};i.prototype.fadeIn=function(t,e,i,a,r,n){if(e===void 0){e=-1}if(i===void 0){i=-1}if(a===void 0){a=0}if(r===void 0){r=null}if(n===void 0){n=3}this._animationConfig.clear();this._animationConfig.fadeOutMode=n;this._animationConfig.playTimes=i;this._animationConfig.layer=a;this._animationConfig.fadeInTime=e;this._animationConfig.animation=t;this._animationConfig.group=r!==null?r:"";return this.playConfig(this._animationConfig)};i.prototype.gotoAndPlayByTime=function(t,e,i){if(e===void 0){e=0}if(i===void 0){i=-1}this._animationConfig.clear();this._animationConfig.resetToPose=true;this._animationConfig.playTimes=i;this._animationConfig.position=e;this._animationConfig.fadeInTime=0;this._animationConfig.animation=t;return this.playConfig(this._animationConfig)};i.prototype.gotoAndPlayByFrame=function(t,e,i){if(e===void 0){e=0}if(i===void 0){i=-1}this._animationConfig.clear();this._animationConfig.resetToPose=true;this._animationConfig.playTimes=i;this._animationConfig.fadeInTime=0;this._animationConfig.animation=t;var a=t in this._animations?this._animations[t]:null;if(a!==null){this._animationConfig.position=a.duration*e/a.frameCount}return this.playConfig(this._animationConfig)};i.prototype.gotoAndPlayByProgress=function(t,e,i){if(e===void 0){e=0}if(i===void 0){i=-1}this._animationConfig.clear();this._animationConfig.resetToPose=true;this._animationConfig.playTimes=i;this._animationConfig.fadeInTime=0;this._animationConfig.animation=t;var a=t in this._animations?this._animations[t]:null;if(a!==null){this._animationConfig.position=a.duration*(e>0?e:0)}return this.playConfig(this._animationConfig)};i.prototype.gotoAndStopByTime=function(t,e){if(e===void 0){e=0}var i=this.gotoAndPlayByTime(t,e,1);if(i!==null){i.stop()}return i};i.prototype.gotoAndStopByFrame=function(t,e){if(e===void 0){e=0}var i=this.gotoAndPlayByFrame(t,e,1);if(i!==null){i.stop()}return i};i.prototype.gotoAndStopByProgress=function(t,e){if(e===void 0){e=0}var i=this.gotoAndPlayByProgress(t,e,1);if(i!==null){i.stop()}return i};i.prototype.getState=function(t){var e=this._animationStates.length;while(e--){var i=this._animationStates[e];if(i.name===t){return i}}return null};i.prototype.hasAnimation=function(t){return t in this._animations};i.prototype.getStates=function(){return this._animationStates};Object.defineProperty(i.prototype,"isPlaying",{get:function(){for(var t=0,e=this._animationStates;t<e.length;t++){var i=e[t];if(i.isPlaying){return true}}return false},enumerable:true,configurable:true});Object.defineProperty(i.prototype,"isCompleted",{get:function(){for(var t=0,e=this._animationStates;t<e.length;t++){var i=e[t];if(!i.isCompleted){return false}}return this._animationStates.length>0},enumerable:true,configurable:true});Object.defineProperty(i.prototype,"lastAnimationName",{get:function(){return this._lastAnimationState!==null?this._lastAnimationState.name:""},enumerable:true,configurable:true});Object.defineProperty(i.prototype,"animationNames",{get:function(){return this._animationNames},enumerable:true,configurable:true});Object.defineProperty(i.prototype,"animations",{get:function(){return this._animations},set:function(t){if(this._animations===t){return}this._animationNames.length=0;for(var e in this._animations){delete this._animations[e]}for(var e in t){this._animationNames.push(e);this._animations[e]=t[e]}},enumerable:true,configurable:true});Object.defineProperty(i.prototype,"animationConfig",{get:function(){this._animationConfig.clear();return this._animationConfig},enumerable:true,configurable:true});Object.defineProperty(i.prototype,"lastAnimationState",{get:function(){return this._lastAnimationState},enumerable:true,configurable:true});i.prototype.gotoAndPlay=function(t,e,i,a,r,n,s,o,l){if(e===void 0){e=-1}if(i===void 0){i=-1}if(a===void 0){a=-1}if(r===void 0){r=0}if(n===void 0){n=null}if(s===void 0){s=3}if(o===void 0){o=true}if(l===void 0){l=true}o;l;this._animationConfig.clear();this._animationConfig.resetToPose=true;this._animationConfig.fadeOutMode=s;this._animationConfig.playTimes=a;this._animationConfig.layer=r;this._animationConfig.fadeInTime=e;this._animationConfig.animation=t;this._animationConfig.group=n!==null?n:"";var h=this._animations[t];if(h&&i>0){this._animationConfig.timeScale=h.duration/i}return this.playConfig(this._animationConfig)};i.prototype.gotoAndStop=function(t,e){if(e===void 0){e=0}return this.gotoAndStopByTime(t,e)};Object.defineProperty(i.prototype,"animationList",{get:function(){return this._animationNames},enumerable:true,configurable:true});Object.defineProperty(i.prototype,"animationDataList",{get:function(){var t=[];for(var e=0,i=this._animationNames.length;e<i;++e){t.push(this._animations[this._animationNames[e]])}return t},enumerable:true,configurable:true});return i}(t.BaseObject);t.Animation=e})(dragonBones||(dragonBones={}));var dragonBones;(function(t){var e=function(e){__extends(a,e);function a(){var t=e!==null&&e.apply(this,arguments)||this;t._boneMask=[];t._boneTimelines=[];t._slotTimelines=[];t._constraintTimelines=[];t._poseTimelines=[];t._bonePoses={};t._actionTimeline=null;t._zOrderTimeline=null;return t}a.toString=function(){return"[class dragonBones.AnimationState]"};a.prototype._onClear=function(){for(var t=0,e=this._boneTimelines;t<e.length;t++){var i=e[t];i.returnToPool()}for(var a=0,r=this._slotTimelines;a<r.length;a++){var i=r[a];i.returnToPool()}for(var n=0,s=this._constraintTimelines;n<s.length;n++){var i=s[n];i.returnToPool()}for(var o in this._bonePoses){this._bonePoses[o].returnToPool();delete this._bonePoses[o]}if(this._actionTimeline!==null){this._actionTimeline.returnToPool()}if(this._zOrderTimeline!==null){this._zOrderTimeline.returnToPool()}this.actionEnabled=false;this.additiveBlending=false;this.displayControl=false;this.resetToPose=false;this.playTimes=1;this.layer=0;this.timeScale=1;this.weight=1;this.autoFadeOutTime=0;this.fadeTotalTime=0;this.name="";this.group="";this._timelineDirty=true;this._playheadState=0;this._fadeState=-1;this._subFadeState=-1;this._position=0;this._duration=0;this._fadeTime=0;this._time=0;this._fadeProgress=0;this._weightResult=0;this._boneMask.length=0;this._boneTimelines.length=0;this._slotTimelines.length=0;this._constraintTimelines.length=0;this._poseTimelines.length=0;this._animationData=null;this._armature=null;this._actionTimeline=null;this._zOrderTimeline=null};a.prototype._updateTimelines=function(){{var e={};for(var a=0,r=this._boneTimelines;a<r.length;a++){var n=r[a];var s=n.bone.name;if(!(s in e)){e[s]=[]}e[s].push(n)}for(var o=0,l=this._armature.getBones();o<l.length;o++){var h=l[o];var s=h.name;if(!this.containsBoneMask(s)){continue}var u=this._animationData.getBoneTimelines(s);if(s in e){delete e[s]}else{var f=s in this._bonePoses?this._bonePoses[s]:this._bonePoses[s]=t.BaseObject.borrowObject(i);if(u!==null){for(var _=0,m=u;_<m.length;_++){var p=m[_];switch(p.type){case 10:{var n=t.BaseObject.borrowObject(t.BoneAllTimelineState);n.bone=h;n.bonePose=f;n.init(this._armature,this,p);this._boneTimelines.push(n);break}case 11:{var n=t.BaseObject.borrowObject(t.BoneTranslateTimelineState);n.bone=h;n.bonePose=f;n.init(this._armature,this,p);this._boneTimelines.push(n);break}case 12:{var n=t.BaseObject.borrowObject(t.BoneRotateTimelineState);n.bone=h;n.bonePose=f;n.init(this._armature,this,p);this._boneTimelines.push(n);break}case 13:{var n=t.BaseObject.borrowObject(t.BoneScaleTimelineState);n.bone=h;n.bonePose=f;n.init(this._armature,this,p);this._boneTimelines.push(n);break}default:break}}}else if(this.resetToPose){var n=t.BaseObject.borrowObject(t.BoneAllTimelineState);n.bone=h;n.bonePose=f;n.init(this._armature,this,null);this._boneTimelines.push(n);this._poseTimelines.push(n)}}}for(var c in e){for(var d=0,y=e[c];d<y.length;d++){var n=y[d];this._boneTimelines.splice(this._boneTimelines.indexOf(n),1);n.returnToPool()}}}{var v={};var g=[];for(var b=0,T=this._slotTimelines;b<T.length;b++){var n=T[b];var s=n.slot.name;if(!(s in v)){v[s]=[]}v[s].push(n)}for(var A=0,D=this._armature.getSlots();A<D.length;A++){var O=D[A];var S=O.parent.name;if(!this.containsBoneMask(S)){continue}var s=O.name;var u=this._animationData.getSlotTimelines(s);if(s in v){delete v[s]}else{var x=false;var B=false;g.length=0;if(u!==null){for(var P=0,M=u;P<M.length;P++){var p=M[P];switch(p.type){case 20:{var n=t.BaseObject.borrowObject(t.SlotDislayTimelineState);n.slot=O;n.init(this._armature,this,p);this._slotTimelines.push(n);x=true;break}case 21:{var n=t.BaseObject.borrowObject(t.SlotColorTimelineState);n.slot=O;n.init(this._armature,this,p);this._slotTimelines.push(n);B=true;break}case 22:{var n=t.BaseObject.borrowObject(t.SlotFFDTimelineState);n.slot=O;n.init(this._armature,this,p);this._slotTimelines.push(n);g.push(n.meshOffset);break}default:break}}}if(this.resetToPose){if(!x){var n=t.BaseObject.borrowObject(t.SlotDislayTimelineState);n.slot=O;n.init(this._armature,this,null);this._slotTimelines.push(n);this._poseTimelines.push(n)}if(!B){var n=t.BaseObject.borrowObject(t.SlotColorTimelineState);n.slot=O;n.init(this._armature,this,null);this._slotTimelines.push(n);this._poseTimelines.push(n)}if(O.rawDisplayDatas!==null){for(var E=0,I=O.rawDisplayDatas;E<I.length;E++){var w=I[E];if(w!==null&&w.type===2){var F=w.offset;if(g.indexOf(F)<0){var n=t.BaseObject.borrowObject(t.SlotFFDTimelineState);n.meshOffset=F;n.slot=O;n.init(this._armature,this,null);this._slotTimelines.push(n);this._poseTimelines.push(n)}}}}}}}for(var c in v){for(var C=0,R=v[c];C<R.length;C++){var n=R[C];this._slotTimelines.splice(this._slotTimelines.indexOf(n),1);n.returnToPool()}}}{var N={};for(var k=0,j=this._constraintTimelines;k<j.length;k++){var n=j[k];var s=n.constraint.name;if(!(s in N)){N[s]=[]}N[s].push(n)}for(var L=0,U=this._armature._constraints;L<U.length;L++){var Y=U[L];var s=Y.name;var u=this._animationData.getConstraintTimelines(s);if(s in N){delete N[s]}else{if(u!==null){for(var X=0,V=u;X<V.length;X++){var p=V[X];switch(p.type){case 30:{var n=t.BaseObject.borrowObject(t.IKConstraintTimelineState);n.constraint=Y;n.init(this._armature,this,p);this._constraintTimelines.push(n);break}default:break}}}else if(this.resetToPose){var n=t.BaseObject.borrowObject(t.IKConstraintTimelineState);n.constraint=Y;n.init(this._armature,this,null);this._constraintTimelines.push(n);this._poseTimelines.push(n)}}}for(var c in N){for(var H=0,z=N[c];H<z.length;H++){var n=z[H];this._constraintTimelines.splice(this._constraintTimelines.indexOf(n),1);n.returnToPool()}}}};a.prototype._advanceFadeTime=function(e){var i=this._fadeState>0;if(this._subFadeState<0){this._subFadeState=0;var a=i?t.EventObject.FADE_OUT:t.EventObject.FADE_IN;if(this._armature.eventDispatcher.hasDBEventListener(a)){var r=t.BaseObject.borrowObject(t.EventObject);r.type=a;r.armature=this._armature;r.animationState=this;this._armature._dragonBones.bufferEvent(r)}}if(e<0){e=-e}this._fadeTime+=e;if(this._fadeTime>=this.fadeTotalTime){this._subFadeState=1;this._fadeProgress=i?0:1}else if(this._fadeTime>0){this._fadeProgress=i?1-this._fadeTime/this.fadeTotalTime:this._fadeTime/this.fadeTotalTime}else{this._fadeProgress=i?1:0}if(this._subFadeState>0){if(!i){this._playheadState|=1;this._fadeState=0}var a=i?t.EventObject.FADE_OUT_COMPLETE:t.EventObject.FADE_IN_COMPLETE;if(this._armature.eventDispatcher.hasDBEventListener(a)){var r=t.BaseObject.borrowObject(t.EventObject);r.type=a;r.armature=this._armature;r.animationState=this;this._armature._dragonBones.bufferEvent(r)}}};a.prototype._blendBoneTimline=function(t){var e=this._weightResult>0?this._weightResult:-this._weightResult;var i=t.bone;var a=t.bonePose.result;var r=i.animationPose;if(!i._blendDirty){i._blendDirty=true;i._blendLayer=this.layer;i._blendLayerWeight=e;i._blendLeftWeight=1;r.x=a.x*e;r.y=a.y*e;r.rotation=a.rotation*e;r.skew=a.skew*e;r.scaleX=(a.scaleX-1)*e+1;r.scaleY=(a.scaleY-1)*e+1}else{e*=i._blendLeftWeight;i._blendLayerWeight+=e;r.x+=a.x*e;r.y+=a.y*e;r.rotation+=a.rotation*e;r.skew+=a.skew*e;r.scaleX+=(a.scaleX-1)*e;r.scaleY+=(a.scaleY-1)*e}if(this._fadeState!==0||this._subFadeState!==0){i._transformDirty=true}};a.prototype.init=function(e,i,a){if(this._armature!==null){return}this._armature=e;this._animationData=i;this.resetToPose=a.resetToPose;this.additiveBlending=a.additiveBlending;this.displayControl=a.displayControl;this.actionEnabled=a.actionEnabled;this.layer=a.layer;this.playTimes=a.playTimes;this.timeScale=a.timeScale;this.fadeTotalTime=a.fadeInTime;this.autoFadeOutTime=a.autoFadeOutTime;this.weight=a.weight;this.name=a.name.length>0?a.name:a.animation;this.group=a.group;if(a.pauseFadeIn){this._playheadState=2}else{this._playheadState=3}if(a.duration<0){this._position=0;this._duration=this._animationData.duration;if(a.position!==0){if(this.timeScale>=0){this._time=a.position}else{this._time=a.position-this._duration}}else{this._time=0}}else{this._position=a.position;this._duration=a.duration;this._time=0}if(this.timeScale<0&&this._time===0){this._time=-1e-6}if(this.fadeTotalTime<=0){this._fadeProgress=.999999}if(a.boneMask.length>0){this._boneMask.length=a.boneMask.length;for(var r=0,n=this._boneMask.length;r<n;++r){this._boneMask[r]=a.boneMask[r]}}this._actionTimeline=t.BaseObject.borrowObject(t.ActionTimelineState);this._actionTimeline.init(this._armature,this,this._animationData.actionTimeline);this._actionTimeline.currentTime=this._time;if(this._actionTimeline.currentTime<0){this._actionTimeline.currentTime=this._duration-this._actionTimeline.currentTime}if(this._animationData.zOrderTimeline!==null){this._zOrderTimeline=t.BaseObject.borrowObject(t.ZOrderTimelineState);this._zOrderTimeline.init(this._armature,this,this._animationData.zOrderTimeline)}};a.prototype.advanceTime=function(e,i){if(this._fadeState!==0||this._subFadeState!==0){this._advanceFadeTime(e)}if(this._playheadState===3){if(this.timeScale!==1){e*=this.timeScale}this._time+=e}if(this._timelineDirty){this._timelineDirty=false;this._updateTimelines()}if(this.weight===0){return}var a=this._fadeState===0&&i>0;var r=true;var n=true;var s=this._time;this._weightResult=this.weight*this._fadeProgress;if(this._actionTimeline.playState<=0){this._actionTimeline.update(s)}if(a){var o=i*2;this._actionTimeline.currentTime=Math.floor(this._actionTimeline.currentTime*o)/o}if(this._zOrderTimeline!==null&&this._zOrderTimeline.playState<=0){this._zOrderTimeline.update(s)}if(a){var l=Math.floor(this._actionTimeline.currentTime*i);if(this._armature._cacheFrameIndex===l){r=false;n=false}else{this._armature._cacheFrameIndex=l;if(this._animationData.cachedFrames[l]){n=false}else{this._animationData.cachedFrames[l]=true}}}if(r){if(n){var h=null;var u=null;for(var f=0,_=this._boneTimelines.length;f<_;++f){var m=this._boneTimelines[f];if(h!==m.bone){if(h!==null){this._blendBoneTimline(u);if(h._blendDirty){if(h._blendLeftWeight>0){if(h._blendLayer!==this.layer){if(h._blendLayerWeight>=h._blendLeftWeight){h._blendLeftWeight=0;h=null}else{h._blendLayer=this.layer;h._blendLeftWeight-=h._blendLayerWeight;h._blendLayerWeight=0}}}else{h=null}}}h=m.bone}if(h!==null){if(m.playState<=0){m.update(s)}if(f===_-1){this._blendBoneTimline(m)}else{u=m}}}}if(this.displayControl){for(var f=0,_=this._slotTimelines.length;f<_;++f){var m=this._slotTimelines[f];var p=m.slot.displayController;if(p===null||p===this.name||p===this.group){if(m.playState<=0){m.update(s)}}}}for(var f=0,_=this._constraintTimelines.length;f<_;++f){var m=this._constraintTimelines[f];if(m.playState<=0){m.update(s)}}}if(this._fadeState===0){if(this._subFadeState>0){this._subFadeState=0;if(this._poseTimelines.length>0){for(var c=0,d=this._poseTimelines;c<d.length;c++){var m=d[c];if(m instanceof t.BoneTimelineState){var y=this._boneTimelines.indexOf(m);this._boneTimelines.splice(y,1)}else if(m instanceof t.SlotTimelineState){var y=this._slotTimelines.indexOf(m);this._slotTimelines.splice(y,1)}else if(m instanceof t.ConstraintTimelineState){var y=this._constraintTimelines.indexOf(m);this._constraintTimelines.splice(y,1)}m.returnToPool()}this._poseTimelines.length=0}}if(this._actionTimeline.playState>0){if(this.autoFadeOutTime>=0){this.fadeOut(this.autoFadeOutTime)}}}};a.prototype.play=function(){this._playheadState=3};a.prototype.stop=function(){this._playheadState&=1};a.prototype.fadeOut=function(t,e){if(e===void 0){e=true}if(t<0){t=0}if(e){this._playheadState&=2}if(this._fadeState>0){if(t>this.fadeTotalTime-this._fadeTime){return}}else{this._fadeState=1;this._subFadeState=-1;if(t<=0||this._fadeProgress<=0){this._fadeProgress=1e-6}for(var i=0,a=this._boneTimelines;i<a.length;i++){var r=a[i];r.fadeOut()}for(var n=0,s=this._slotTimelines;n<s.length;n++){var r=s[n];r.fadeOut()}}this.displayControl=false;this.fadeTotalTime=this._fadeProgress>1e-6?t/this._fadeProgress:0;this._fadeTime=this.fadeTotalTime*(1-this._fadeProgress)};a.prototype.containsBoneMask=function(t){return this._boneMask.length===0||this._boneMask.indexOf(t)>=0};a.prototype.addBoneMask=function(t,e){if(e===void 0){e=true}var i=this._armature.getBone(t);if(i===null){return}if(this._boneMask.indexOf(t)<0){this._boneMask.push(t)}if(e){for(var a=0,r=this._armature.getBones();a<r.length;a++){var n=r[a];if(this._boneMask.indexOf(n.name)<0&&i.contains(n)){this._boneMask.push(n.name)}}}this._timelineDirty=true};a.prototype.removeBoneMask=function(t,e){if(e===void 0){e=true}var i=this._boneMask.indexOf(t);if(i>=0){this._boneMask.splice(i,1)}if(e){var a=this._armature.getBone(t);if(a!==null){var r=this._armature.getBones();if(this._boneMask.length>0){for(var n=0,s=r;n<s.length;n++){var o=s[n];var l=this._boneMask.indexOf(o.name);if(l>=0&&a.contains(o)){this._boneMask.splice(l,1)}}}else{for(var h=0,u=r;h<u.length;h++){var o=u[h];if(o===a){continue}if(!a.contains(o)){this._boneMask.push(o.name)}}}}}this._timelineDirty=true};a.prototype.removeAllBoneMask=function(){this._boneMask.length=0;this._timelineDirty=true};Object.defineProperty(a.prototype,"isFadeIn",{get:function(){return this._fadeState<0},enumerable:true,configurable:true});Object.defineProperty(a.prototype,"isFadeOut",{get:function(){return this._fadeState>0},enumerable:true,configurable:true});Object.defineProperty(a.prototype,"isFadeComplete",{get:function(){return this._fadeState===0},enumerable:true,configurable:true});Object.defineProperty(a.prototype,"isPlaying",{get:function(){return(this._playheadState&2)!==0&&this._actionTimeline.playState<=0},enumerable:true,configurable:true});Object.defineProperty(a.prototype,"isCompleted",{get:function(){return this._actionTimeline.playState>0},enumerable:true,configurable:true});Object.defineProperty(a.prototype,"currentPlayTimes",{get:function(){return this._actionTimeline.currentPlayTimes},enumerable:true,configurable:true});Object.defineProperty(a.prototype,"totalTime",{get:function(){return this._duration},enumerable:true,configurable:true});Object.defineProperty(a.prototype,"currentTime",{get:function(){return this._actionTimeline.currentTime},set:function(t){var e=this._actionTimeline.currentPlayTimes-(this._actionTimeline.playState>0?1:0);if(t<0||this._duration<t){t=t%this._duration+e*this._duration;if(t<0){t+=this._duration}}if(this.playTimes>0&&e===this.playTimes-1&&t===this._duration){t=this._duration-1e-6}if(this._time===t){return}this._time=t;this._actionTimeline.setCurrentTime(this._time);if(this._zOrderTimeline!==null){this._zOrderTimeline.playState=-1}for(var i=0,a=this._boneTimelines;i<a.length;i++){var r=a[i];r.playState=-1}for(var n=0,s=this._slotTimelines;n<s.length;n++){var r=s[n];r.playState=-1}},enumerable:true,configurable:true});Object.defineProperty(a.prototype,"animationData",{get:function(){return this._animationData},enumerable:true,configurable:true});return a}(t.BaseObject);t.AnimationState=e;var i=function(e){__extends(i,e);function i(){var i=e!==null&&e.apply(this,arguments)||this;i.current=new t.Transform;i.delta=new t.Transform;i.result=new t.Transform;return i}i.toString=function(){return"[class dragonBones.BonePose]"};i.prototype._onClear=function(){this.current.identity();this.delta.identity();this.result.identity()};return i}(t.BaseObject);t.BonePose=i})(dragonBones||(dragonBones={}));var dragonBones;(function(t){var e=function(t){__extends(e,t);function e(){return t!==null&&t.apply(this,arguments)||this}e.prototype._onClear=function(){this.playState=-1;this.currentPlayTimes=-1;this.currentTime=-1;this._tweenState=0;this._frameRate=0;this._frameValueOffset=0;this._frameCount=0;this._frameOffset=0;this._frameIndex=-1;this._frameRateR=0;this._position=0;this._duration=0;this._timeScale=1;this._timeOffset=0;this._dragonBonesData=null;this._animationData=null;this._timelineData=null;this._armature=null;this._animationState=null;this._actionTimeline=null;this._frameArray=null;this._frameIntArray=null;this._frameFloatArray=null;this._timelineArray=null;this._frameIndices=null};e.prototype._setCurrentTime=function(t){var e=this.playState;var i=this.currentPlayTimes;var a=this.currentTime;if(this._actionTimeline!==null&&this._frameCount<=1){this.playState=this._actionTimeline.playState>=0?1:-1;this.currentPlayTimes=1;this.currentTime=this._actionTimeline.currentTime}else if(this._actionTimeline===null||this._timeScale!==1||this._timeOffset!==0){var r=this._animationState.playTimes;var n=r*this._duration;t*=this._timeScale;if(this._timeOffset!==0){t+=this._timeOffset*this._animationData.duration}if(r>0&&(t>=n||t<=-n)){if(this.playState<=0&&this._animationState._playheadState===3){this.playState=1}this.currentPlayTimes=r;if(t<0){this.currentTime=0}else{this.currentTime=this._duration}}else{if(this.playState!==0&&this._animationState._playheadState===3){this.playState=0}if(t<0){t=-t;this.currentPlayTimes=Math.floor(t/this._duration);this.currentTime=this._duration-t%this._duration}else{this.currentPlayTimes=Math.floor(t/this._duration);this.currentTime=t%this._duration}}this.currentTime+=this._position}else{this.playState=this._actionTimeline.playState;this.currentPlayTimes=this._actionTimeline.currentPlayTimes;this.currentTime=this._actionTimeline.currentTime}if(this.currentPlayTimes===i&&this.currentTime===a){return false}if(e<0&&this.playState!==e||this.playState<=0&&this.currentPlayTimes!==i){this._frameIndex=-1}return true};e.prototype.init=function(t,e,i){this._armature=t;this._animationState=e;this._timelineData=i;this._actionTimeline=this._animationState._actionTimeline;if(this===this._actionTimeline){this._actionTimeline=null}this._animationData=this._animationState._animationData;this._frameRate=this._animationData.parent.frameRate;this._frameRateR=1/this._frameRate;this._position=this._animationState._position;this._duration=this._animationState._duration;this._dragonBonesData=this._animationData.parent.parent;if(this._timelineData!==null){this._frameIntArray=this._dragonBonesData.frameIntArray;this._frameFloatArray=this._dragonBonesData.frameFloatArray;this._frameArray=this._dragonBonesData.frameArray;this._timelineArray=this._dragonBonesData.timelineArray;this._frameIndices=this._dragonBonesData.frameIndices;this._frameCount=this._timelineArray[this._timelineData.offset+2];this._frameValueOffset=this._timelineArray[this._timelineData.offset+4];this._timeScale=100/this._timelineArray[this._timelineData.offset+0];this._timeOffset=this._timelineArray[this._timelineData.offset+1]*.01}};e.prototype.fadeOut=function(){};e.prototype.update=function(t){if(this._setCurrentTime(t)){if(this._frameCount>1){var e=Math.floor(this.currentTime*this._frameRate);var i=this._frameIndices[this._timelineData.frameIndicesOffset+e];if(this._frameIndex!==i){this._frameIndex=i;this._frameOffset=this._animationData.frameOffset+this._timelineArray[this._timelineData.offset+5+this._frameIndex];this._onArriveAtFrame()}}else if(this._frameIndex<0){this._frameIndex=0;if(this._timelineData!==null){this._frameOffset=this._animationData.frameOffset+this._timelineArray[this._timelineData.offset+5]}this._onArriveAtFrame()}if(this._tweenState!==0){this._onUpdateFrame()}}};return e}(t.BaseObject);t.TimelineState=e;var i=function(t){__extends(e,t);function e(){return t!==null&&t.apply(this,arguments)||this}e._getEasingValue=function(t,e,i){var a=e;switch(t){case 3:a=Math.pow(e,2);break;case 4:a=1-Math.pow(1-e,2);break;case 5:a=.5*(1-Math.cos(e*Math.PI));break}return(a-e)*i+e};e._getEasingCurveValue=function(t,e,i,a){if(t<=0){return 0}else if(t>=1){return 1}var r=i+1;var n=Math.floor(t*r);var s=n===0?0:e[a+n-1];var o=n===r-1?1e4:e[a+n];return(s+(o-s)*(t*r-n))*1e-4};e.prototype._onClear=function(){t.prototype._onClear.call(this);this._tweenType=0;this._curveCount=0;this._framePosition=0;this._frameDurationR=0;this._tweenProgress=0;this._tweenEasing=0};e.prototype._onArriveAtFrame=function(){if(this._frameCount>1&&(this._frameIndex!==this._frameCount-1||this._animationState.playTimes===0||this._animationState.currentPlayTimes<this._animationState.playTimes-1)){this._tweenType=this._frameArray[this._frameOffset+1];this._tweenState=this._tweenType===0?1:2;if(this._tweenType===2){this._curveCount=this._frameArray[this._frameOffset+2]}else if(this._tweenType!==0&&this._tweenType!==1){this._tweenEasing=this._frameArray[this._frameOffset+2]*.01}this._framePosition=this._frameArray[this._frameOffset]*this._frameRateR;if(this._frameIndex===this._frameCount-1){this._frameDurationR=1/(this._animationData.duration-this._framePosition)}else{var t=this._animationData.frameOffset+this._timelineArray[this._timelineData.offset+5+this._frameIndex+1];var e=this._frameArray[t]*this._frameRateR-this._framePosition;if(e>0){this._frameDurationR=1/e}else{this._frameDurationR=0}}}else{this._tweenState=1}};e.prototype._onUpdateFrame=function(){if(this._tweenState===2){this._tweenProgress=(this.currentTime-this._framePosition)*this._frameDurationR;if(this._tweenType===2){this._tweenProgress=e._getEasingCurveValue(this._tweenProgress,this._frameArray,this._curveCount,this._frameOffset+3)}else if(this._tweenType!==1){this._tweenProgress=e._getEasingValue(this._tweenType,this._tweenProgress,this._tweenEasing)}}else{this._tweenProgress=0}};return e}(e);t.TweenTimelineState=i;var a=function(t){__extends(e,t);function e(){return t!==null&&t.apply(this,arguments)||this}e.prototype._onClear=function(){t.prototype._onClear.call(this);this.bone=null;this.bonePose=null};return e}(i);t.BoneTimelineState=a;var r=function(t){__extends(e,t);function e(){return t!==null&&t.apply(this,arguments)||this}e.prototype._onClear=function(){t.prototype._onClear.call(this);this.slot=null};return e}(i);t.SlotTimelineState=r;var n=function(t){__extends(e,t);function e(){return t!==null&&t.apply(this,arguments)||this}e.prototype._onClear=function(){t.prototype._onClear.call(this);this.constraint=null};return e}(i);t.ConstraintTimelineState=n})(dragonBones||(dragonBones={}));var dragonBones;(function(t){var e=function(e){__extends(i,e);function i(){return e!==null&&e.apply(this,arguments)||this}i.toString=function(){return"[class dragonBones.ActionTimelineState]"};i.prototype._onCrossFrame=function(e){var i=this._armature.eventDispatcher;if(this._animationState.actionEnabled){var a=this._animationData.frameOffset+this._timelineArray[this._timelineData.offset+5+e];var r=this._frameArray[a+1];var n=this._animationData.parent.actions;for(var s=0;s<r;++s){var o=this._frameArray[a+2+s];var l=n[o];if(l.type===0){if(l.slot!==null){var h=this._armature.getSlot(l.slot.name);if(h!==null){var u=h.childArmature;if(u!==null){u._bufferAction(l,true)}}}else if(l.bone!==null){for(var f=0,_=this._armature.getSlots();f<_.length;f++){var h=_[f];var u=h.childArmature;if(u!==null&&h.parent._boneData===l.bone){u._bufferAction(l,true)}}}else{this._armature._bufferAction(l,true)}}else{var m=l.type===10?t.EventObject.FRAME_EVENT:t.EventObject.SOUND_EVENT;if(l.type===11||i.hasDBEventListener(m)){var p=t.BaseObject.borrowObject(t.EventObject);p.time=this._frameArray[a]/this._frameRate;p.type=m;p.name=l.name;p.data=l.data;p.armature=this._armature;p.animationState=this._animationState;if(l.bone!==null){p.bone=this._armature.getBone(l.bone.name)}if(l.slot!==null){p.slot=this._armature.getSlot(l.slot.name)}this._armature._dragonBones.bufferEvent(p)}}}}};i.prototype._onArriveAtFrame=function(){};i.prototype._onUpdateFrame=function(){};i.prototype.update=function(e){var i=this.playState;var a=this.currentPlayTimes;var r=this.currentTime;if(this._setCurrentTime(e)){var n=this._armature.eventDispatcher;if(i<0){if(this.playState!==i){if(this._animationState.displayControl&&this._animationState.resetToPose){this._armature._sortZOrder(null,0)}a=this.currentPlayTimes;if(n.hasDBEventListener(t.EventObject.START)){var s=t.BaseObject.borrowObject(t.EventObject);s.type=t.EventObject.START;s.armature=this._armature;s.animationState=this._animationState;this._armature._dragonBones.bufferEvent(s)}}else{return}}var o=this._animationState.timeScale<0;var l=null;var h=null;if(this.currentPlayTimes!==a){if(n.hasDBEventListener(t.EventObject.LOOP_COMPLETE)){l=t.BaseObject.borrowObject(t.EventObject);l.type=t.EventObject.LOOP_COMPLETE;l.armature=this._armature;l.animationState=this._animationState}if(this.playState>0){if(n.hasDBEventListener(t.EventObject.COMPLETE)){h=t.BaseObject.borrowObject(t.EventObject);h.type=t.EventObject.COMPLETE;h.armature=this._armature;h.animationState=this._animationState}}}if(this._frameCount>1){var u=this._timelineData;var f=Math.floor(this.currentTime*this._frameRate);var _=this._frameIndices[u.frameIndicesOffset+f];if(this._frameIndex!==_){var m=this._frameIndex;this._frameIndex=_;if(this._timelineArray!==null){this._frameOffset=this._animationData.frameOffset+this._timelineArray[u.offset+5+this._frameIndex];if(o){if(m<0){var p=Math.floor(r*this._frameRate);m=this._frameIndices[u.frameIndicesOffset+p];if(this.currentPlayTimes===a){if(m===_){m=-1}}}while(m>=0){var c=this._animationData.frameOffset+this._timelineArray[u.offset+5+m];var d=this._frameArray[c]/this._frameRate;if(this._position<=d&&d<=this._position+this._duration){this._onCrossFrame(m)}if(l!==null&&m===0){this._armature._dragonBones.bufferEvent(l);l=null}if(m>0){m--}else{m=this._frameCount-1}if(m===_){break}}}else{if(m<0){var p=Math.floor(r*this._frameRate);m=this._frameIndices[u.frameIndicesOffset+p];var c=this._animationData.frameOffset+this._timelineArray[u.offset+5+m];var d=this._frameArray[c]/this._frameRate;if(this.currentPlayTimes===a){if(r<=d){if(m>0){m--}else{m=this._frameCount-1}}else if(m===_){m=-1}}}while(m>=0){if(m<this._frameCount-1){m++}else{m=0}var c=this._animationData.frameOffset+this._timelineArray[u.offset+5+m];var d=this._frameArray[c]/this._frameRate;if(this._position<=d&&d<=this._position+this._duration){this._onCrossFrame(m)}if(l!==null&&m===0){this._armature._dragonBones.bufferEvent(l);l=null}if(m===_){break}}}}}}else if(this._frameIndex<0){this._frameIndex=0;if(this._timelineData!==null){this._frameOffset=this._animationData.frameOffset+this._timelineArray[this._timelineData.offset+5];var d=this._frameArray[this._frameOffset]/this._frameRate;if(this.currentPlayTimes===a){if(r<=d){this._onCrossFrame(this._frameIndex)}}else if(this._position<=d){if(!o&&l!==null){this._armature._dragonBones.bufferEvent(l);l=null}this._onCrossFrame(this._frameIndex)}}}if(l!==null){this._armature._dragonBones.bufferEvent(l)}if(h!==null){this._armature._dragonBones.bufferEvent(h)}}};i.prototype.setCurrentTime=function(t){this._setCurrentTime(t);this._frameIndex=-1};return i}(t.TimelineState);t.ActionTimelineState=e;var i=function(t){__extends(e,t);function e(){return t!==null&&t.apply(this,arguments)||this}e.toString=function(){return"[class dragonBones.ZOrderTimelineState]"};e.prototype._onArriveAtFrame=function(){if(this.playState>=0){var t=this._frameArray[this._frameOffset+1];if(t>0){this._armature._sortZOrder(this._frameArray,this._frameOffset+2)}else{this._armature._sortZOrder(null,0)}}};e.prototype._onUpdateFrame=function(){};return e}(t.TimelineState);t.ZOrderTimelineState=i;var a=function(e){__extends(i,e);function i(){return e!==null&&e.apply(this,arguments)||this}i.toString=function(){return"[class dragonBones.BoneAllTimelineState]"};i.prototype._onArriveAtFrame=function(){e.prototype._onArriveAtFrame.call(this);if(this._timelineData!==null){var t=this._animationData.frameFloatOffset+this._frameValueOffset+this._frameIndex*6;var i=this._armature._armatureData.scale;var a=this._frameFloatArray;var r=this.bonePose.current;var n=this.bonePose.delta;r.x=a[t++]*i;r.y=a[t++]*i;r.rotation=a[t++];r.skew=a[t++];r.scaleX=a[t++];r.scaleY=a[t++];if(this._tweenState===2){if(this._frameIndex===this._frameCount-1){t=this._animationData.frameFloatOffset+this._frameValueOffset}n.x=a[t++]*i-r.x;n.y=a[t++]*i-r.y;n.rotation=a[t++]-r.rotation;n.skew=a[t++]-r.skew;n.scaleX=a[t++]-r.scaleX;n.scaleY=a[t++]-r.scaleY}else{n.x=0;n.y=0;n.rotation=0;n.skew=0;n.scaleX=0;n.scaleY=0}}else{var r=this.bonePose.current;var n=this.bonePose.delta;r.x=0;r.y=0;r.rotation=0;r.skew=0;r.scaleX=1;r.scaleY=1;n.x=0;n.y=0;n.rotation=0;n.skew=0;n.scaleX=0;n.scaleY=0}};i.prototype._onUpdateFrame=function(){e.prototype._onUpdateFrame.call(this);var t=this.bonePose.current;var i=this.bonePose.delta;var a=this.bonePose.result;this.bone._transformDirty=true;if(this._tweenState!==2){this._tweenState=0}a.x=t.x+i.x*this._tweenProgress;a.y=t.y+i.y*this._tweenProgress;a.rotation=t.rotation+i.rotation*this._tweenProgress;a.skew=t.skew+i.skew*this._tweenProgress;a.scaleX=t.scaleX+i.scaleX*this._tweenProgress;a.scaleY=t.scaleY+i.scaleY*this._tweenProgress};i.prototype.fadeOut=function(){var e=this.bonePose.result;e.rotation=t.Transform.normalizeRadian(e.rotation);e.skew=t.Transform.normalizeRadian(e.skew)};return i}(t.BoneTimelineState);t.BoneAllTimelineState=a;var r=function(t){__extends(e,t);function e(){return t!==null&&t.apply(this,arguments)||this}e.toString=function(){return"[class dragonBones.BoneTranslateTimelineState]"};e.prototype._onArriveAtFrame=function(){t.prototype._onArriveAtFrame.call(this);if(this._timelineData!==null){var e=this._animationData.frameFloatOffset+this._frameValueOffset+this._frameIndex*2;var i=this._armature._armatureData.scale;var a=this._frameFloatArray;var r=this.bonePose.current;var n=this.bonePose.delta;r.x=a[e++]*i;r.y=a[e++]*i;if(this._tweenState===2){if(this._frameIndex===this._frameCount-1){e=this._animationData.frameFloatOffset+this._frameValueOffset}n.x=a[e++]*i-r.x;n.y=a[e++]*i-r.y}else{n.x=0;n.y=0}}else{var r=this.bonePose.current;var n=this.bonePose.delta;r.x=0;r.y=0;n.x=0;n.y=0}};e.prototype._onUpdateFrame=function(){t.prototype._onUpdateFrame.call(this);var e=this.bonePose.current;var i=this.bonePose.delta;var a=this.bonePose.result;this.bone._transformDirty=true;if(this._tweenState!==2){this._tweenState=0}a.x=e.x+i.x*this._tweenProgress;a.y=e.y+i.y*this._tweenProgress};return e}(t.BoneTimelineState);t.BoneTranslateTimelineState=r;var n=function(e){__extends(i,e);function i(){return e!==null&&e.apply(this,arguments)||this}i.toString=function(){return"[class dragonBones.BoneRotateTimelineState]"};i.prototype._onArriveAtFrame=function(){e.prototype._onArriveAtFrame.call(this);if(this._timelineData!==null){var t=this._animationData.frameFloatOffset+this._frameValueOffset+this._frameIndex*2;var i=this._frameFloatArray;var a=this.bonePose.current;var r=this.bonePose.delta;a.rotation=i[t++];a.skew=i[t++];if(this._tweenState===2){if(this._frameIndex===this._frameCount-1){t=this._animationData.frameFloatOffset+this._frameValueOffset}r.rotation=i[t++]-a.rotation;r.skew=i[t++]-a.skew}else{r.rotation=0;r.skew=0}}else{var a=this.bonePose.current;var r=this.bonePose.delta;a.rotation=0;a.skew=0;r.rotation=0;r.skew=0}};i.prototype._onUpdateFrame=function(){e.prototype._onUpdateFrame.call(this);var t=this.bonePose.current;var i=this.bonePose.delta;var a=this.bonePose.result;this.bone._transformDirty=true;if(this._tweenState!==2){this._tweenState=0}a.rotation=t.rotation+i.rotation*this._tweenProgress;a.skew=t.skew+i.skew*this._tweenProgress};i.prototype.fadeOut=function(){var e=this.bonePose.result;e.rotation=t.Transform.normalizeRadian(e.rotation);e.skew=t.Transform.normalizeRadian(e.skew)};return i}(t.BoneTimelineState);t.BoneRotateTimelineState=n;var s=function(t){__extends(e,t);function e(){return t!==null&&t.apply(this,arguments)||this}e.toString=function(){return"[class dragonBones.BoneScaleTimelineState]"};e.prototype._onArriveAtFrame=function(){t.prototype._onArriveAtFrame.call(this);if(this._timelineData!==null){var e=this._animationData.frameFloatOffset+this._frameValueOffset+this._frameIndex*2;var i=this._frameFloatArray;var a=this.bonePose.current;var r=this.bonePose.delta;a.scaleX=i[e++];a.scaleY=i[e++];if(this._tweenState===2){if(this._frameIndex===this._frameCount-1){e=this._animationData.frameFloatOffset+this._frameValueOffset}r.scaleX=i[e++]-a.scaleX;r.scaleY=i[e++]-a.scaleY}else{r.scaleX=0;r.scaleY=0}}else{var a=this.bonePose.current;var r=this.bonePose.delta;a.scaleX=1;a.scaleY=1;r.scaleX=0;r.scaleY=0}};e.prototype._onUpdateFrame=function(){t.prototype._onUpdateFrame.call(this);var e=this.bonePose.current;var i=this.bonePose.delta;var a=this.bonePose.result;this.bone._transformDirty=true;if(this._tweenState!==2){this._tweenState=0}a.scaleX=e.scaleX+i.scaleX*this._tweenProgress;a.scaleY=e.scaleY+i.scaleY*this._tweenProgress};return e}(t.BoneTimelineState);t.BoneScaleTimelineState=s;var o=function(t){__extends(e,t);function e(){return t!==null&&t.apply(this,arguments)||this}e.toString=function(){return"[class dragonBones.SlotDislayTimelineState]"};e.prototype._onArriveAtFrame=function(){if(this.playState>=0){var t=this._timelineData!==null?this._frameArray[this._frameOffset+1]:this.slot._slotData.displayIndex;if(this.slot.displayIndex!==t){this.slot._setDisplayIndex(t,true)}}};return e}(t.SlotTimelineState);t.SlotDislayTimelineState=o;var l=function(t){__extends(e,t);function e(){var e=t!==null&&t.apply(this,arguments)||this;e._current=[0,0,0,0,0,0,0,0];e._delta=[0,0,0,0,0,0,0,0];e._result=[0,0,0,0,0,0,0,0];return e}e.toString=function(){return"[class dragonBones.SlotColorTimelineState]"};e.prototype._onClear=function(){t.prototype._onClear.call(this);this._dirty=false};e.prototype._onArriveAtFrame=function(){t.prototype._onArriveAtFrame.call(this);if(this._timelineData!==null){var e=this._dragonBonesData.intArray;var i=this._frameIntArray;var a=this._animationData.frameIntOffset+this._frameValueOffset+this._frameIndex*1;var r=i[a];if(r<0){r+=32767}this._current[0]=e[r++];this._current[1]=e[r++];this._current[2]=e[r++];this._current[3]=e[r++];this._current[4]=e[r++];this._current[5]=e[r++];this._current[6]=e[r++];this._current[7]=e[r++];if(this._tweenState===2){if(this._frameIndex===this._frameCount-1){r=i[this._animationData.frameIntOffset+this._frameValueOffset]}else{r=i[a+1*1]}if(r<0){r+=32767}this._delta[0]=e[r++]-this._current[0];this._delta[1]=e[r++]-this._current[1];this._delta[2]=e[r++]-this._current[2];this._delta[3]=e[r++]-this._current[3];this._delta[4]=e[r++]-this._current[4];this._delta[5]=e[r++]-this._current[5];this._delta[6]=e[r++]-this._current[6];this._delta[7]=e[r++]-this._current[7]}}else{var n=this.slot._slotData.color;this._current[0]=n.alphaMultiplier*100;this._current[1]=n.redMultiplier*100;this._current[2]=n.greenMultiplier*100;this._current[3]=n.blueMultiplier*100;this._current[4]=n.alphaOffset;this._current[5]=n.redOffset;this._current[6]=n.greenOffset;this._current[7]=n.blueOffset}};e.prototype._onUpdateFrame=function(){t.prototype._onUpdateFrame.call(this);this._dirty=true;if(this._tweenState!==2){this._tweenState=0}this._result[0]=(this._current[0]+this._delta[0]*this._tweenProgress)*.01;this._result[1]=(this._current[1]+this._delta[1]*this._tweenProgress)*.01;this._result[2]=(this._current[2]+this._delta[2]*this._tweenProgress)*.01;this._result[3]=(this._current[3]+this._delta[3]*this._tweenProgress)*.01;this._result[4]=this._current[4]+this._delta[4]*this._tweenProgress;this._result[5]=this._current[5]+this._delta[5]*this._tweenProgress;this._result[6]=this._current[6]+this._delta[6]*this._tweenProgress;this._result[7]=this._current[7]+this._delta[7]*this._tweenProgress};e.prototype.fadeOut=function(){this._tweenState=0;this._dirty=false};e.prototype.update=function(e){t.prototype.update.call(this,e);if(this._tweenState!==0||this._dirty){var i=this.slot._colorTransform;if(this._animationState._fadeState!==0||this._animationState._subFadeState!==0){if(i.alphaMultiplier!==this._result[0]||i.redMultiplier!==this._result[1]||i.greenMultiplier!==this._result[2]||i.blueMultiplier!==this._result[3]||i.alphaOffset!==this._result[4]||i.redOffset!==this._result[5]||i.greenOffset!==this._result[6]||i.blueOffset!==this._result[7]){var a=Math.pow(this._animationState._fadeProgress,4);i.alphaMultiplier+=(this._result[0]-i.alphaMultiplier)*a;i.redMultiplier+=(this._result[1]-i.redMultiplier)*a;i.greenMultiplier+=(this._result[2]-i.greenMultiplier)*a;i.blueMultiplier+=(this._result[3]-i.blueMultiplier)*a;i.alphaOffset+=(this._result[4]-i.alphaOffset)*a;i.redOffset+=(this._result[5]-i.redOffset)*a;i.greenOffset+=(this._result[6]-i.greenOffset)*a;i.blueOffset+=(this._result[7]-i.blueOffset)*a;this.slot._colorDirty=true}}else if(this._dirty){this._dirty=false;if(i.alphaMultiplier!==this._result[0]||i.redMultiplier!==this._result[1]||i.greenMultiplier!==this._result[2]||i.blueMultiplier!==this._result[3]||i.alphaOffset!==this._result[4]||i.redOffset!==this._result[5]||i.greenOffset!==this._result[6]||i.blueOffset!==this._result[7]){i.alphaMultiplier=this._result[0];i.redMultiplier=this._result[1];i.greenMultiplier=this._result[2];i.blueMultiplier=this._result[3];i.alphaOffset=this._result[4];i.redOffset=this._result[5];i.greenOffset=this._result[6];i.blueOffset=this._result[7];this.slot._colorDirty=true}}}};return e}(t.SlotTimelineState);t.SlotColorTimelineState=l;var h=function(t){__extends(e,t);function e(){var e=t!==null&&t.apply(this,arguments)||this;e._current=[];e._delta=[];e._result=[];return e}e.toString=function(){return"[class dragonBones.SlotFFDTimelineState]"};e.prototype._onClear=function(){t.prototype._onClear.call(this);this.meshOffset=0;this._dirty=false;this._frameFloatOffset=0;this._valueCount=0;this._ffdCount=0;this._valueOffset=0;this._current.length=0;this._delta.length=0;this._result.length=0};e.prototype._onArriveAtFrame=function(){t.prototype._onArriveAtFrame.call(this);if(this._timelineData!==null){var e=this._animationData.frameFloatOffset+this._frameValueOffset+this._frameIndex*this._valueCount;var i=this._armature._armatureData.scale;var a=this._frameFloatArray;if(this._tweenState===2){var r=e+this._valueCount;if(this._frameIndex===this._frameCount-1){r=this._animationData.frameFloatOffset+this._frameValueOffset}for(var n=0;n<this._valueCount;++n){this._delta[n]=a[r+n]*i-(this._current[n]=a[e+n]*i)}}else{for(var n=0;n<this._valueCount;++n){this._current[n]=a[e+n]*i}}}else{for(var n=0;n<this._valueCount;++n){this._current[n]=0}}};e.prototype._onUpdateFrame=function(){t.prototype._onUpdateFrame.call(this);this._dirty=true;if(this._tweenState!==2){this._tweenState=0}for(var e=0;e<this._valueCount;++e){this._result[e]=this._current[e]+this._delta[e]*this._tweenProgress}};e.prototype.init=function(e,i,a){t.prototype.init.call(this,e,i,a);if(this._timelineData!==null){var r=this._animationData.frameIntOffset+this._timelineArray[this._timelineData.offset+3];this.meshOffset=this._frameIntArray[r+0];this._ffdCount=this._frameIntArray[r+1];this._valueCount=this._frameIntArray[r+2];this._valueOffset=this._frameIntArray[r+3];this._frameFloatOffset=this._frameIntArray[r+4]+this._animationData.frameFloatOffset}else{this._valueCount=0}this._current.length=this._valueCount;this._delta.length=this._valueCount;this._result.length=this._valueCount;for(var n=0;n<this._valueCount;++n){this._delta[n]=0}};e.prototype.fadeOut=function(){this._tweenState=0;this._dirty=false};e.prototype.update=function(e){if(this.slot._meshData===null||this.slot._meshData.offset!==this.meshOffset){return}t.prototype.update.call(this,e);if(this._tweenState!==0||this._dirty){var i=this.slot._ffdVertices;if(this._timelineData!==null){if(this._animationState._fadeState!==0||this._animationState._subFadeState!==0){var a=Math.pow(this._animationState._fadeProgress,2);for(var r=0;r<this._ffdCount;++r){if(r<this._valueOffset){i[r]+=(this._frameFloatArray[this._frameFloatOffset+r]-i[r])*a}else if(r<this._valueOffset+this._valueCount){i[r]+=(this._result[r-this._valueOffset]-i[r])*a}else{i[r]+=(this._frameFloatArray[this._frameFloatOffset+r-this._valueCount]-i[r])*a}}this.slot._meshDirty=true}else if(this._dirty){this._dirty=false;for(var r=0;r<this._ffdCount;++r){if(r<this._valueOffset){i[r]=this._frameFloatArray[this._frameFloatOffset+r]}else if(r<this._valueOffset+this._valueCount){i[r]=this._result[r-this._valueOffset]}else{i[r]=this._frameFloatArray[this._frameFloatOffset+r-this._valueCount]}}this.slot._meshDirty=true}}else{this._ffdCount=i.length;if(this._animationState._fadeState!==0||this._animationState._subFadeState!==0){var a=Math.pow(this._animationState._fadeProgress,2);for(var r=0;r<this._ffdCount;++r){i[r]+=(0-i[r])*a}this.slot._meshDirty=true}else if(this._dirty){this._dirty=false;for(var r=0;r<this._ffdCount;++r){i[r]=0}this.slot._meshDirty=true}}}};return e}(t.SlotTimelineState);t.SlotFFDTimelineState=h;var u=function(t){__extends(e,t);function e(){return t!==null&&t.apply(this,arguments)||this}e.toString=function(){return"[class dragonBones.IKConstraintTimelineState]"};e.prototype._onClear=function(){t.prototype._onClear.call(this);this._current=0;this._delta=0};e.prototype._onArriveAtFrame=function(){t.prototype._onArriveAtFrame.call(this);var e=this.constraint;if(this._timelineData!==null){var i=this._animationData.frameIntOffset+this._frameValueOffset+this._frameIndex*2;var a=this._frameIntArray;var r=a[i++]!==0;this._current=a[i++]*.01;if(this._tweenState===2){if(this._frameIndex===this._frameCount-1){i=this._animationData.frameIntOffset+this._frameValueOffset}this._delta=a[i+1]*.01-this._current}else{this._delta=0}e._bendPositive=r}else{var n=e._constraintData;this._current=n.weight;this._delta=0;e._bendPositive=n.bendPositive}e.invalidUpdate()};e.prototype._onUpdateFrame=function(){t.prototype._onUpdateFrame.call(this);if(this._tweenState!==2){this._tweenState=0}var e=this.constraint;e._weight=this._current+this._delta*this._tweenProgress;e.invalidUpdate()};return e}(t.ConstraintTimelineState);t.IKConstraintTimelineState=u})(dragonBones||(dragonBones={}));var dragonBones;(function(t){var e=function(t){__extends(e,t);function e(){return t!==null&&t.apply(this,arguments)||this}e.toString=function(){return"[class dragonBones.EventObject]"};e.prototype._onClear=function(){this.time=0;this.type="";this.name="";this.armature=null;this.bone=null;this.slot=null;this.animationState=null;this.data=null};e.START="start";e.LOOP_COMPLETE="loopComplete";e.COMPLETE="complete";e.FADE_IN="fadeIn";e.FADE_IN_COMPLETE="fadeInComplete";e.FADE_OUT="fadeOut";e.FADE_OUT_COMPLETE="fadeOutComplete";e.FRAME_EVENT="frameEvent";e.SOUND_EVENT="soundEvent";return e}(t.BaseObject);t.EventObject=e})(dragonBones||(dragonBones={}));var dragonBones;(function(t){var e=function(){function e(){}e._getArmatureType=function(t){switch(t.toLowerCase()){case"stage":return 2;case"armature":return 0;case"movieclip":return 1;default:return 0}};e._getDisplayType=function(t){switch(t.toLowerCase()){case"image":return 0;case"mesh":return 2;case"armature":return 1;case"boundingbox":return 3;default:return 0}};e._getBoundingBoxType=function(t){switch(t.toLowerCase()){case"rectangle":return 0;case"ellipse":return 1;case"polygon":return 2;default:return 0}};e._getActionType=function(t){switch(t.toLowerCase()){case"play":return 0;case"frame":return 10;case"sound":return 11;default:return 0}};e._getBlendMode=function(t){switch(t.toLowerCase()){case"normal":return 0;case"add":return 1;case"alpha":return 2;case"darken":return 3;case"difference":return 4;case"erase":return 5;case"hardlight":return 6;case"invert":return 7;case"layer":return 8;case"lighten":return 9;case"multiply":return 10;case"overlay":return 11;case"screen":return 12;case"subtract":return 13;default:return 0}};e.parseDragonBonesData=function(e){if(e instanceof ArrayBuffer){return t.BinaryDataParser.getInstance().parseDragonBonesData(e)}else{return t.ObjectDataParser.getInstance().parseDragonBonesData(e)}};e.parseTextureAtlasData=function(i,a){if(a===void 0){a=1}console.warn("已废弃");var r={};var n=i[e.SUB_TEXTURE];for(var s=0,o=n.length;s<o;s++){var l=n[s];var h=l[e.NAME];var u=new t.Rectangle;var f=null;u.x=l[e.X]/a;u.y=l[e.Y]/a;u.width=l[e.WIDTH]/a;u.height=l[e.HEIGHT]/a;if(e.FRAME_WIDTH in l){f=new t.Rectangle;f.x=l[e.FRAME_X]/a;f.y=l[e.FRAME_Y]/a;f.width=l[e.FRAME_WIDTH]/a;f.height=l[e.FRAME_HEIGHT]/a}r[h]={region:u,frame:f,rotated:false}}return r};e.DATA_VERSION_2_3="2.3";e.DATA_VERSION_3_0="3.0";e.DATA_VERSION_4_0="4.0";e.DATA_VERSION_4_5="4.5";e.DATA_VERSION_5_0="5.0";e.DATA_VERSION_5_5="5.5";e.DATA_VERSION=e.DATA_VERSION_5_5;e.DATA_VERSIONS=[e.DATA_VERSION_4_0,e.DATA_VERSION_4_5,e.DATA_VERSION_5_0,e.DATA_VERSION_5_5];e.TEXTURE_ATLAS="textureAtlas";e.SUB_TEXTURE="SubTexture";e.FORMAT="format";e.IMAGE_PATH="imagePath";e.WIDTH="width";e.HEIGHT="height";e.ROTATED="rotated";e.FRAME_X="frameX";e.FRAME_Y="frameY";e.FRAME_WIDTH="frameWidth";e.FRAME_HEIGHT="frameHeight";e.DRADON_BONES="dragonBones";e.USER_DATA="userData";e.ARMATURE="armature";e.BONE="bone";e.SLOT="slot";e.CONSTRAINT="constraint";e.IK="ik";e.SKIN="skin";e.DISPLAY="display";e.ANIMATION="animation";e.Z_ORDER="zOrder";e.FFD="ffd";e.FRAME="frame";e.TRANSLATE_FRAME="translateFrame";e.ROTATE_FRAME="rotateFrame";e.SCALE_FRAME="scaleFrame";e.DISPLAY_FRAME="displayFrame";e.COLOR_FRAME="colorFrame";e.DEFAULT_ACTIONS="defaultActions";e.ACTIONS="actions";e.EVENTS="events";e.INTS="ints";e.FLOATS="floats";e.STRINGS="strings";e.CANVAS="canvas";e.TRANSFORM="transform";e.PIVOT="pivot";e.AABB="aabb";e.COLOR="color";e.VERSION="version";e.COMPATIBLE_VERSION="compatibleVersion";e.FRAME_RATE="frameRate";e.TYPE="type";e.SUB_TYPE="subType";e.NAME="name";e.PARENT="parent";e.TARGET="target";e.STAGE="stage";e.SHARE="share";e.PATH="path";e.LENGTH="length";e.DISPLAY_INDEX="displayIndex";e.BLEND_MODE="blendMode";e.INHERIT_TRANSLATION="inheritTranslation";e.INHERIT_ROTATION="inheritRotation";e.INHERIT_SCALE="inheritScale";e.INHERIT_REFLECTION="inheritReflection";e.INHERIT_ANIMATION="inheritAnimation";e.INHERIT_FFD="inheritFFD";e.BEND_POSITIVE="bendPositive";e.CHAIN="chain";e.WEIGHT="weight";e.FADE_IN_TIME="fadeInTime";e.PLAY_TIMES="playTimes";e.SCALE="scale";e.OFFSET="offset";e.POSITION="position";e.DURATION="duration";e.TWEEN_EASING="tweenEasing";e.TWEEN_ROTATE="tweenRotate";e.TWEEN_SCALE="tweenScale";e.CLOCK_WISE="clockwise";e.CURVE="curve";e.SOUND="sound";e.EVENT="event";e.ACTION="action";e.X="x";e.Y="y";e.SKEW_X="skX";e.SKEW_Y="skY";e.SCALE_X="scX";e.SCALE_Y="scY";e.VALUE="value";e.ROTATE="rotate";e.SKEW="skew";e.ALPHA_OFFSET="aO";e.RED_OFFSET="rO";e.GREEN_OFFSET="gO";e.BLUE_OFFSET="bO";e.ALPHA_MULTIPLIER="aM";e.RED_MULTIPLIER="rM";e.GREEN_MULTIPLIER="gM";e.BLUE_MULTIPLIER="bM";e.UVS="uvs";e.VERTICES="vertices";e.TRIANGLES="triangles";e.WEIGHTS="weights";e.SLOT_POSE="slotPose";e.BONE_POSE="bonePose";e.GOTO_AND_PLAY="gotoAndPlay";e.DEFAULT_NAME="default";return e}();t.DataParser=e})(dragonBones||(dragonBones={}));var dragonBones;(function(t){var e=function(e){__extends(a,e);function a(){var i=e!==null&&e.apply(this,arguments)||this;i._rawTextureAtlasIndex=0;i._rawBones=[];i._data=null;i._armature=null;i._bone=null;i._slot=null;i._skin=null;i._mesh=null;i._animation=null;i._timeline=null;i._rawTextureAtlases=null;i._defaultColorOffset=-1;i._prevClockwise=0;i._prevRotation=0;i._helpMatrixA=new t.Matrix;i._helpMatrixB=new t.Matrix;i._helpTransform=new t.Transform;i._helpColorTransform=new t.ColorTransform;i._helpPoint=new t.Point;i._helpArray=[];i._intArray=[];i._floatArray=[];i._frameIntArray=[];i._frameFloatArray=[];i._frameArray=[];i._timelineArray=[];i._actionFrames=[];i._weightSlotPose={};i._weightBonePoses={};i._cacheBones={};i._cacheMeshs={};i._slotChildActions={};return i}a._getBoolean=function(t,e,i){if(e in t){var a=t[e];var r=typeof a;if(r==="boolean"){return a}else if(r==="string"){switch(a){case"0":case"NaN":case"":case"false":case"null":case"undefined":return false;default:return true}}else{return!!a}}return i};a._getNumber=function(t,e,i){if(e in t){var a=t[e];if(a===null||a==="NaN"){return i}return+a||0}return i};a._getString=function(e,i,a){if(i in e){var r=e[i];var n=typeof r;if(n==="string"){if(t.DragonBones.webAssembly){for(var s=0,o=r.length;s<o;++s){if(r.charCodeAt(s)>255){return encodeURI(r)}}}return r}return String(r)}return a};a.prototype._getCurvePoint=function(t,e,i,a,r,n,s,o,l,h){var u=1-l;var f=u*u;var _=l*l;var m=u*f;var p=3*l*f;var c=3*u*_;var d=l*_;h.x=m*t+p*i+c*r+d*s;h.y=m*e+p*a+c*n+d*o};a.prototype._samplingEasingCurve=function(t,e){var i=t.length;var a=-2;for(var r=0,n=e.length;r<n;++r){var s=(r+1)/(n+1);while((a+6<i?t[a+6]:1)<s){a+=6}var o=a>=0&&a+6<i;var l=o?t[a]:0;var h=o?t[a+1]:0;var u=t[a+2];var f=t[a+3];var _=t[a+4];var m=t[a+5];var p=o?t[a+6]:1;var c=o?t[a+7]:1;var d=0;var y=1;while(y-d>1e-4){var v=(y+d)*.5;this._getCurvePoint(l,h,u,f,_,m,p,c,v,this._helpPoint);if(s-this._helpPoint.x>0){d=v}else{y=v}}e[r]=this._helpPoint.y}};a.prototype._parseActionDataInFrame=function(t,e,i,r){if(a.EVENT in t){this._mergeActionFrame(t[a.EVENT],e,10,i,r)}if(a.SOUND in t){this._mergeActionFrame(t[a.SOUND],e,11,i,r)}if(a.ACTION in t){this._mergeActionFrame(t[a.ACTION],e,0,i,r)}if(a.EVENTS in t){this._mergeActionFrame(t[a.EVENTS],e,10,i,r)}if(a.ACTIONS in t){this._mergeActionFrame(t[a.ACTIONS],e,0,i,r)}};a.prototype._mergeActionFrame=function(e,a,r,n,s){var o=t.DragonBones.webAssembly?this._armature.actions.size():this._armature.actions.length;var l=this._parseActionData(e,r,n,s);var h=0;var u=null;for(var f=0,_=l;f<_.length;f++){var m=_[f];this._armature.addAction(m,false)}if(this._actionFrames.length===0){u=new i;u.frameStart=0;this._actionFrames.push(u);u=null}for(var p=0,c=this._actionFrames;p<c.length;p++){var d=c[p];if(d.frameStart===a){u=d;break}else if(d.frameStart>a){break}h++}if(u===null){u=new i;u.frameStart=a;this._actionFrames.splice(h+1,0,u)}for(var y=0;y<l.length;++y){u.actions.push(o+y)}};a.prototype._parseArmature=function(e,i){var r=t.BaseObject.borrowObject(t.ArmatureData);r.name=a._getString(e,a.NAME,"");r.frameRate=a._getNumber(e,a.FRAME_RATE,this._data.frameRate);r.scale=i;if(a.TYPE in e&&typeof e[a.TYPE]==="string"){r.type=a._getArmatureType(e[a.TYPE])}else{r.type=a._getNumber(e,a.TYPE,0)}if(r.frameRate===0){r.frameRate=24}this._armature=r;if(a.CANVAS in e){var n=e[a.CANVAS];var s=t.BaseObject.borrowObject(t.CanvasData);if(a.COLOR in n){s.hasBackground=true}else{s.hasBackground=false}s.color=a._getNumber(n,a.COLOR,0);s.x=a._getNumber(n,a.X,0)*r.scale;s.y=a._getNumber(n,a.Y,0)*r.scale;s.width=a._getNumber(n,a.WIDTH,0)*r.scale;s.height=a._getNumber(n,a.HEIGHT,0)*r.scale;r.canvas=s}if(a.AABB in e){var o=e[a.AABB];r.aabb.x=a._getNumber(o,a.X,0)*r.scale;r.aabb.y=a._getNumber(o,a.Y,0)*r.scale;r.aabb.width=a._getNumber(o,a.WIDTH,0)*r.scale;r.aabb.height=a._getNumber(o,a.HEIGHT,0)*r.scale}if(a.BONE in e){var l=e[a.BONE];for(var h=0,u=l;h<u.length;h++){var f=u[h];var _=a._getString(f,a.PARENT,"");var m=this._parseBone(f);if(_.length>0){var p=r.getBone(_);if(p!==null){m.parent=p}else{if(!(_ in this._cacheBones)){this._cacheBones[_]=[]}this._cacheBones[_].push(m)}}if(m.name in this._cacheBones){for(var c=0,d=this._cacheBones[m.name];c<d.length;c++){var y=d[c];y.parent=m}delete this._cacheBones[m.name]}r.addBone(m);this._rawBones.push(m)}}if(a.IK in e){var v=e[a.IK];for(var g=0,b=v;g<b.length;g++){var T=b[g];var A=this._parseIKConstraint(T);if(A){r.addConstraint(A)}}}r.sortBones();if(a.SLOT in e){var D=0;var O=e[a.SLOT];for(var S=0,x=O;S<x.length;S++){var B=x[S];r.addSlot(this._parseSlot(B,D++))}}if(a.SKIN in e){var P=e[a.SKIN];for(var M=0,E=P;M<E.length;M++){var I=E[M];r.addSkin(this._parseSkin(I))}}for(var w in this._cacheMeshs){var F=r.getSkin(w);if(F===null){continue}var C=this._cacheMeshs[w];for(var R in C){var N=C[R];for(var k in N){var j=F.getDisplay(R,k);if(j===null){continue}for(var L=0,U=N[k];L<U.length;L++){var Y=U[L];Y.offset=j.offset;Y.weight=j.weight}}}}if(a.ANIMATION in e){var X=e[a.ANIMATION];for(var V=0,H=X;V<H.length;V++){var z=H[V];var G=this._parseAnimation(z);r.addAnimation(G)}}if(a.DEFAULT_ACTIONS in e){var W=this._parseActionData(e[a.DEFAULT_ACTIONS],0,null,null);for(var K=0,Z=W;K<Z.length;K++){var q=Z[K];r.addAction(q,true);if(q.type===0){var G=r.getAnimation(q.name);if(G!==null){r.defaultAnimation=G}}}}if(a.ACTIONS in e){var W=this._parseActionData(e[a.ACTIONS],0,null,null);for(var Q=0,J=W;Q<J.length;Q++){var q=J[Q];r.addAction(q,false)}}this._rawBones.length=0;this._armature=null;for(var $ in this._weightSlotPose){delete this._weightSlotPose[$]}for(var $ in this._weightBonePoses){delete this._weightBonePoses[$]}for(var $ in this._cacheMeshs){delete this._cacheMeshs[$]}for(var $ in this._cacheBones){delete this._cacheBones[$]}for(var $ in this._slotChildActions){delete this._slotChildActions[$]}return r};a.prototype._parseBone=function(e){var i=t.BaseObject.borrowObject(t.BoneData);i.inheritTranslation=a._getBoolean(e,a.INHERIT_TRANSLATION,true);i.inheritRotation=a._getBoolean(e,a.INHERIT_ROTATION,true);i.inheritScale=a._getBoolean(e,a.INHERIT_SCALE,true);i.inheritReflection=a._getBoolean(e,a.INHERIT_REFLECTION,true);i.length=a._getNumber(e,a.LENGTH,0)*this._armature.scale;i.name=a._getString(e,a.NAME,"");if(a.TRANSFORM in e){this._parseTransform(e[a.TRANSFORM],i.transform,this._armature.scale)}return i};a.prototype._parseIKConstraint=function(e){var i=this._armature.getBone(a._getString(e,a.BONE,""));if(i===null){return null}var r=this._armature.getBone(a._getString(e,a.TARGET,""));if(r===null){return null}var n=t.BaseObject.borrowObject(t.IKConstraintData);n.scaleEnabled=a._getBoolean(e,a.SCALE,false);n.bendPositive=a._getBoolean(e,a.BEND_POSITIVE,true);n.weight=a._getNumber(e,a.WEIGHT,1);n.name=a._getString(e,a.NAME,"");n.bone=i;n.target=r;var s=a._getNumber(e,a.CHAIN,0);if(s>0){n.root=i.parent}return n};a.prototype._parseSlot=function(e,i){var r=t.BaseObject.borrowObject(t.SlotData);r.displayIndex=a._getNumber(e,a.DISPLAY_INDEX,0);r.zOrder=i;r.name=a._getString(e,a.NAME,"");r.parent=this._armature.getBone(a._getString(e,a.PARENT,""));if(a.BLEND_MODE in e&&typeof e[a.BLEND_MODE]==="string"){r.blendMode=a._getBlendMode(e[a.BLEND_MODE])}else{r.blendMode=a._getNumber(e,a.BLEND_MODE,0)}if(a.COLOR in e){r.color=t.SlotData.createColor();this._parseColorTransform(e[a.COLOR],r.color)}else{r.color=t.SlotData.DEFAULT_COLOR}if(a.ACTIONS in e){this._slotChildActions[r.name]=this._parseActionData(e[a.ACTIONS],0,null,null)}return r};a.prototype._parseSkin=function(e){var i=t.BaseObject.borrowObject(t.SkinData);i.name=a._getString(e,a.NAME,a.DEFAULT_NAME);if(i.name.length===0){i.name=a.DEFAULT_NAME}if(a.SLOT in e){var r=e[a.SLOT];this._skin=i;for(var n=0,s=r;n<s.length;n++){var o=s[n];var l=a._getString(o,a.NAME,"");var h=this._armature.getSlot(l);if(h!==null){this._slot=h;if(a.DISPLAY in o){var u=o[a.DISPLAY];for(var f=0,_=u;f<_.length;f++){var m=_[f];i.addDisplay(l,this._parseDisplay(m))}}this._slot=null}}this._skin=null}return i};a.prototype._parseDisplay=function(e){var i=a._getString(e,a.NAME,"");var r=a._getString(e,a.PATH,"");var n=0;var s=null;if(a.TYPE in e&&typeof e[a.TYPE]==="string"){n=a._getDisplayType(e[a.TYPE])}else{n=a._getNumber(e,a.TYPE,n)}switch(n){case 0:var o=s=t.BaseObject.borrowObject(t.ImageDisplayData);o.name=i;o.path=r.length>0?r:i;this._parsePivot(e,o);break;case 1:var l=s=t.BaseObject.borrowObject(t.ArmatureDisplayData);l.name=i;l.path=r.length>0?r:i;l.inheritAnimation=true;if(a.ACTIONS in e){var h=this._parseActionData(e[a.ACTIONS],0,null,null);for(var u=0,f=h;u<f.length;u++){var _=f[u];l.addAction(_)}}else if(this._slot.name in this._slotChildActions){var m=this._skin.getDisplays(this._slot.name);if(m===null?this._slot.displayIndex===0:this._slot.displayIndex===m.length){for(var p=0,c=this._slotChildActions[this._slot.name];p<c.length;p++){var _=c[p];l.addAction(_)}delete this._slotChildActions[this._slot.name]}}break;case 2:var d=a._getString(e,a.SHARE,"");var y=s=t.BaseObject.borrowObject(t.MeshDisplayData);y.name=i;y.path=r.length>0?r:i;y.inheritAnimation=a._getBoolean(e,a.INHERIT_FFD,true);this._parsePivot(e,y);if(d.length>0){var v=a._getString(e,a.SKIN,"");var g=this._slot.name;if(v.length===0){v=a.DEFAULT_NAME}if(!(v in this._cacheMeshs)){this._cacheMeshs[v]={}}var b=this._cacheMeshs[v];if(!(g in b)){b[g]={}}var T=b[g];if(!(d in T)){T[d]=[]}T[d].push(y)}else{this._parseMesh(e,y)}break;case 3:var A=this._parseBoundingBox(e);if(A!==null){var D=s=t.BaseObject.borrowObject(t.BoundingBoxDisplayData);D.name=i;D.path=r.length>0?r:i;D.boundingBox=A}break}if(s!==null){if(a.TRANSFORM in e){this._parseTransform(e[a.TRANSFORM],s.transform,this._armature.scale)}}return s};a.prototype._parsePivot=function(t,e){if(a.PIVOT in t){var i=t[a.PIVOT];e.pivot.x=a._getNumber(i,a.X,0);e.pivot.y=a._getNumber(i,a.Y,0)}else{e.pivot.x=.5;e.pivot.y=.5}};a.prototype._parseMesh=function(e,i){var r=e[a.VERTICES];var n=e[a.UVS];var s=e[a.TRIANGLES];var o=Math.floor(r.length/2);var l=Math.floor(s.length/3);var h=this._floatArray.length;var u=h+o*2;var f=this._intArray.length;i.offset=f;this._intArray.length+=1+1+1+1+l*3;this._intArray[f+0]=o;this._intArray[f+1]=l;this._intArray[f+2]=h;for(var _=0,m=l*3;_<m;++_){this._intArray[f+4+_]=s[_]}this._floatArray.length+=o*2+o*2;for(var _=0,m=o*2;_<m;++_){this._floatArray[h+_]=r[_];this._floatArray[u+_]=n[_]}if(a.WEIGHTS in e){var p=e[a.WEIGHTS];var c=e[a.SLOT_POSE];var d=e[a.BONE_POSE];var y=this._armature.sortedBones;var v=new Array;var g=Math.floor(d.length/7);var b=this._floatArray.length;var T=Math.floor(p.length-o)/2;var A=this._intArray.length;var D=t.BaseObject.borrowObject(t.WeightData);D.count=T;D.offset=A;v.length=g;this._intArray.length+=1+1+g+o+T;this._intArray[A+1]=b;for(var _=0;_<g;++_){var O=d[_*7];var S=this._rawBones[O];D.addBone(S);v[_]=O;this._intArray[A+2+_]=y.indexOf(S)}this._floatArray.length+=T*3;this._helpMatrixA.copyFromArray(c,0);for(var _=0,x=0,B=A+2+g,P=b;_<o;++_){var M=_*2;var E=this._intArray[B++]=p[x++];var I=this._floatArray[h+M];var w=this._floatArray[h+M+1];this._helpMatrixA.transformPoint(I,w,this._helpPoint);I=this._helpPoint.x;w=this._helpPoint.y;for(var F=0;F<E;++F){var O=p[x++];var C=v.indexOf(O);this._helpMatrixB.copyFromArray(d,C*7+1);this._helpMatrixB.invert();this._helpMatrixB.transformPoint(I,w,this._helpPoint);this._intArray[B++]=C;this._floatArray[P++]=p[x++];this._floatArray[P++]=this._helpPoint.x;this._floatArray[P++]=this._helpPoint.y}}i.weight=D;var R=this._skin.name+"_"+this._slot.name+"_"+i.name;this._weightSlotPose[R]=c;this._weightBonePoses[R]=d}};a.prototype._parseBoundingBox=function(e){var i=null;var r=0;if(a.SUB_TYPE in e&&typeof e[a.SUB_TYPE]==="string"){r=a._getBoundingBoxType(e[a.SUB_TYPE])}else{r=a._getNumber(e,a.SUB_TYPE,r)}switch(r){case 0:i=t.BaseObject.borrowObject(t.RectangleBoundingBoxData);break;case 1:i=t.BaseObject.borrowObject(t.EllipseBoundingBoxData);break;case 2:i=this._parsePolygonBoundingBox(e);break}if(i!==null){i.color=a._getNumber(e,a.COLOR,0);if(i.type===0||i.type===1){i.width=a._getNumber(e,a.WIDTH,0);i.height=a._getNumber(e,a.HEIGHT,0)}}return i};a.prototype._parsePolygonBoundingBox=function(e){var i=t.BaseObject.borrowObject(t.PolygonBoundingBoxData);if(a.VERTICES in e){var r=e[a.VERTICES];var n=i.vertices;if(t.DragonBones.webAssembly){n.resize(r.length,0)}else{n.length=r.length}for(var s=0,o=r.length;s<o;s+=2){var l=r[s];var h=r[s+1];if(t.DragonBones.webAssembly){n.set(s,l);n.set(s+1,h)}else{n[s]=l;n[s+1]=h}if(s===0){i.x=l;i.y=h;i.width=l;i.height=h}else{if(l<i.x){i.x=l}else if(l>i.width){i.width=l}if(h<i.y){i.y=h}else if(h>i.height){i.height=h}}}}else{console.warn("Data error.\n Please reexport DragonBones Data to fixed the bug.")}return i};a.prototype._parseAnimation=function(e){var i=t.BaseObject.borrowObject(t.AnimationData);i.frameCount=Math.max(a._getNumber(e,a.DURATION,1),1);i.playTimes=a._getNumber(e,a.PLAY_TIMES,1);i.duration=i.frameCount/this._armature.frameRate;i.fadeInTime=a._getNumber(e,a.FADE_IN_TIME,0);i.scale=a._getNumber(e,a.SCALE,1);i.name=a._getString(e,a.NAME,a.DEFAULT_NAME);if(i.name.length===0){i.name=a.DEFAULT_NAME}i.frameIntOffset=this._frameIntArray.length;i.frameFloatOffset=this._frameFloatArray.length;i.frameOffset=this._frameArray.length;this._animation=i;if(a.FRAME in e){var r=e[a.FRAME];var n=r.length;if(n>0){for(var s=0,o=0;s<n;++s){var l=r[s];this._parseActionDataInFrame(l,o,null,null);o+=a._getNumber(l,a.DURATION,1)}}}if(a.Z_ORDER in e){this._animation.zOrderTimeline=this._parseTimeline(e[a.Z_ORDER],null,a.FRAME,1,false,false,0,this._parseZOrderFrame)}if(a.BONE in e){var h=e[a.BONE];for(var u=0,f=h;u<f.length;u++){var _=f[u];this._parseBoneTimeline(_)}}if(a.SLOT in e){var h=e[a.SLOT];for(var m=0,p=h;m<p.length;m++){var _=p[m];this._parseSlotTimeline(_)}}if(a.FFD in e){var h=e[a.FFD];for(var c=0,d=h;c<d.length;c++){var _=d[c];var y=a._getString(_,a.SKIN,"");var v=a._getString(_,a.SLOT,"");var g=a._getString(_,a.NAME,"");if(y.length===0){y=a.DEFAULT_NAME}this._skin=this._armature.getSkin(y);if(this._skin===null){continue}this._slot=this._armature.getSlot(v);this._mesh=this._skin.getDisplay(v,g);if(this._skin===null||this._slot===null||this._mesh===null){continue}var b=this._parseTimeline(_,null,a.FRAME,22,false,true,0,this._parseSlotFFDFrame);if(b!==null){this._animation.addSlotTimeline(this._slot,b)}this._skin=null;this._slot=null;this._mesh=null}}if(a.IK in e){var h=e[a.IK];for(var T=0,A=h;T<A.length;T++){var _=A[T];var D=a._getString(_,a.NAME,"");var O=this._armature.getConstraint(D);if(O===null){continue}var S=this._parseTimeline(_,null,a.FRAME,30,true,false,2,this._parseIKConstraintFrame);if(S!==null){this._animation.addConstraintTimeline(O,S)}}}if(this._actionFrames.length>0){this._animation.actionTimeline=this._parseTimeline(null,this._actionFrames,"",0,false,false,0,this._parseActionFrame);this._actionFrames.length=0}this._animation=null;return i};a.prototype._parseTimeline=function(e,r,n,s,o,l,h,u){if(e!==null&&n.length>0&&n in e){r=e[n]}if(r===null){return null}var f=r.length;if(f===0){return null}var _=this._frameIntArray.length;var m=this._frameFloatArray.length;var p=t.BaseObject.borrowObject(t.TimelineData);var c=this._timelineArray.length;this._timelineArray.length+=1+1+1+1+1+f;if(e!==null){this._timelineArray[c+0]=Math.round(a._getNumber(e,a.SCALE,1)*100);this._timelineArray[c+1]=Math.round(a._getNumber(e,a.OFFSET,0)*100)}else{this._timelineArray[c+0]=100;this._timelineArray[c+1]=0}this._timelineArray[c+2]=f;this._timelineArray[c+3]=h;if(o){this._timelineArray[c+4]=_-this._animation.frameIntOffset}else if(l){this._timelineArray[c+4]=m-this._animation.frameFloatOffset}else{this._timelineArray[c+4]=0}this._timeline=p;p.type=s;p.offset=c;if(f===1){p.frameIndicesOffset=-1;this._timelineArray[c+5+0]=u.call(this,r[0],0,0)-this._animation.frameOffset}else{var d=this._animation.frameCount+1;var y=this._data.frameIndices;var v=0;if(t.DragonBones.webAssembly){v=y.size();y.resize(v+d,0)}else{v=y.length;y.length+=d}p.frameIndicesOffset=v;for(var g=0,b=0,T=0,A=0;g<d;++g){if(T+A<=g&&b<f){var D=r[b];T=g;if(b===f-1){A=this._animation.frameCount-T}else{if(D instanceof i){A=this._actionFrames[b+1].frameStart-T}else{A=a._getNumber(D,a.DURATION,1)}}this._timelineArray[c+5+b]=u.call(this,D,T,A)-this._animation.frameOffset;b++}if(t.DragonBones.webAssembly){y.set(v+g,b-1)}else{y[v+g]=b-1}}}this._timeline=null;return p};a.prototype._parseBoneTimeline=function(t){var e=this._armature.getBone(a._getString(t,a.NAME,""));if(e===null){return}this._bone=e;this._slot=this._armature.getSlot(this._bone.name);if(a.TRANSLATE_FRAME in t){var i=this._parseTimeline(t,null,a.TRANSLATE_FRAME,11,false,true,2,this._parseBoneTranslateFrame);if(i!==null){this._animation.addBoneTimeline(e,i)}}if(a.ROTATE_FRAME in t){var i=this._parseTimeline(t,null,a.ROTATE_FRAME,12,false,true,2,this._parseBoneRotateFrame);if(i!==null){this._animation.addBoneTimeline(e,i)}}if(a.SCALE_FRAME in t){var i=this._parseTimeline(t,null,a.SCALE_FRAME,13,false,true,2,this._parseBoneScaleFrame);if(i!==null){this._animation.addBoneTimeline(e,i)}}if(a.FRAME in t){var i=this._parseTimeline(t,null,a.FRAME,10,false,true,6,this._parseBoneAllFrame);if(i!==null){this._animation.addBoneTimeline(e,i)}}this._bone=null;this._slot=null};a.prototype._parseSlotTimeline=function(t){var e=this._armature.getSlot(a._getString(t,a.NAME,""));if(e===null){return}this._slot=e;var i=null;if(a.DISPLAY_FRAME in t){i=this._parseTimeline(t,null,a.DISPLAY_FRAME,20,false,false,0,this._parseSlotDisplayFrame)}else{i=this._parseTimeline(t,null,a.FRAME,20,false,false,0,this._parseSlotDisplayFrame)}if(i!==null){this._animation.addSlotTimeline(e,i)}var r=null;if(a.COLOR_FRAME in t){r=this._parseTimeline(t,null,a.COLOR_FRAME,21,true,false,1,this._parseSlotColorFrame)}else{r=this._parseTimeline(t,null,a.FRAME,21,true,false,1,this._parseSlotColorFrame)}if(r!==null){this._animation.addSlotTimeline(e,r)}this._slot=null};a.prototype._parseFrame=function(t,e,i){t;i;var a=this._frameArray.length;this._frameArray.length+=1;this._frameArray[a+0]=e;return a};a.prototype._parseTweenFrame=function(t,e,i){var r=this._parseFrame(t,e,i);if(i>0){if(a.CURVE in t){var n=i+1;this._helpArray.length=n;this._samplingEasingCurve(t[a.CURVE],this._helpArray);this._frameArray.length+=1+1+this._helpArray.length;this._frameArray[r+1]=2;this._frameArray[r+2]=n;for(var s=0;s<n;++s){this._frameArray[r+3+s]=Math.round(this._helpArray[s]*1e4)}}else{var o=-2;var l=o;if(a.TWEEN_EASING in t){l=a._getNumber(t,a.TWEEN_EASING,o)}if(l===o){this._frameArray.length+=1;this._frameArray[r+1]=0}else if(l===0){this._frameArray.length+=1;this._frameArray[r+1]=1}else if(l<0){this._frameArray.length+=1+1;this._frameArray[r+1]=3;this._frameArray[r+2]=Math.round(-l*100)}else if(l<=1){this._frameArray.length+=1+1;this._frameArray[r+1]=4;this._frameArray[r+2]=Math.round(l*100)}else{this._frameArray.length+=1+1;this._frameArray[r+1]=5;this._frameArray[r+2]=Math.round(l*100-100)}}}else{this._frameArray.length+=1;this._frameArray[r+1]=0}return r};a.prototype._parseActionFrame=function(t,e,i){i;var a=this._frameArray.length;var r=t.actions.length;this._frameArray.length+=1+1+r;this._frameArray[a+0]=e;this._frameArray[a+0+1]=r;for(var n=0;n<r;++n){this._frameArray[a+0+2+n]=t.actions[n]}return a};a.prototype._parseZOrderFrame=function(t,e,i){var r=this._parseFrame(t,e,i);if(a.Z_ORDER in t){var n=t[a.Z_ORDER];if(n.length>0){var s=this._armature.sortedSlots.length;var o=new Array(s-n.length/2);var l=new Array(s);for(var h=0;h<o.length;++h){o[h]=0}for(var u=0;u<s;++u){l[u]=-1}var f=0;var _=0;for(var m=0,p=n.length;m<p;m+=2){var c=n[m];var d=n[m+1];while(f!==c){o[_++]=f++}l[f+d]=f++}while(f<s){o[_++]=f++}this._frameArray.length+=1+s;this._frameArray[r+1]=s;var y=s;while(y--){if(l[y]===-1){this._frameArray[r+2+y]=o[--_]||0}else{this._frameArray[r+2+y]=l[y]||0}}return r}}this._frameArray.length+=1;this._frameArray[r+1]=0;return r};a.prototype._parseBoneAllFrame=function(e,i,r){this._helpTransform.identity();if(a.TRANSFORM in e){this._parseTransform(e[a.TRANSFORM],this._helpTransform,1)}var n=this._helpTransform.rotation;if(i!==0){if(this._prevClockwise===0){n=this._prevRotation+t.Transform.normalizeRadian(n-this._prevRotation)}else{if(this._prevClockwise>0?n>=this._prevRotation:n<=this._prevRotation){this._prevClockwise=this._prevClockwise>0?this._prevClockwise-1:this._prevClockwise+1}n=this._prevRotation+n-this._prevRotation+t.Transform.PI_D*this._prevClockwise}}this._prevClockwise=a._getNumber(e,a.TWEEN_ROTATE,0);this._prevRotation=n;var s=this._parseTweenFrame(e,i,r);var o=this._frameFloatArray.length;this._frameFloatArray.length+=6;this._frameFloatArray[o++]=this._helpTransform.x;this._frameFloatArray[o++]=this._helpTransform.y;this._frameFloatArray[o++]=n;this._frameFloatArray[o++]=this._helpTransform.skew;this._frameFloatArray[o++]=this._helpTransform.scaleX;this._frameFloatArray[o++]=this._helpTransform.scaleY;this._parseActionDataInFrame(e,i,this._bone,this._slot);return s};a.prototype._parseBoneTranslateFrame=function(t,e,i){var r=this._parseTweenFrame(t,e,i);var n=this._frameFloatArray.length;this._frameFloatArray.length+=2;this._frameFloatArray[n++]=a._getNumber(t,a.X,0);this._frameFloatArray[n++]=a._getNumber(t,a.Y,0);return r};a.prototype._parseBoneRotateFrame=function(e,i,r){var n=a._getNumber(e,a.ROTATE,0)*t.Transform.DEG_RAD;if(i!==0){if(this._prevClockwise===0){n=this._prevRotation+t.Transform.normalizeRadian(n-this._prevRotation)}else{if(this._prevClockwise>0?n>=this._prevRotation:n<=this._prevRotation){this._prevClockwise=this._prevClockwise>0?this._prevClockwise-1:this._prevClockwise+1}n=this._prevRotation+n-this._prevRotation+t.Transform.PI_D*this._prevClockwise}}this._prevClockwise=a._getNumber(e,a.CLOCK_WISE,0);this._prevRotation=n;var s=this._parseTweenFrame(e,i,r);var o=this._frameFloatArray.length;this._frameFloatArray.length+=2;this._frameFloatArray[o++]=n;this._frameFloatArray[o++]=a._getNumber(e,a.SKEW,0)*t.Transform.DEG_RAD;return s};a.prototype._parseBoneScaleFrame=function(t,e,i){var r=this._parseTweenFrame(t,e,i);var n=this._frameFloatArray.length;this._frameFloatArray.length+=2;this._frameFloatArray[n++]=a._getNumber(t,a.X,1);this._frameFloatArray[n++]=a._getNumber(t,a.Y,1);return r};a.prototype._parseSlotDisplayFrame=function(t,e,i){var r=this._parseFrame(t,e,i);this._frameArray.length+=1;if(a.VALUE in t){this._frameArray[r+1]=a._getNumber(t,a.VALUE,0)}else{this._frameArray[r+1]=a._getNumber(t,a.DISPLAY_INDEX,0)}this._parseActionDataInFrame(t,e,this._slot.parent,this._slot);return r};a.prototype._parseSlotColorFrame=function(t,e,i){var r=this._parseTweenFrame(t,e,i);var n=-1;if(a.VALUE in t||a.COLOR in t){var s=a.VALUE in t?t[a.VALUE]:t[a.COLOR];for(var o in s){o;this._parseColorTransform(s,this._helpColorTransform);n=this._intArray.length;this._intArray.length+=8;this._intArray[n++]=Math.round(this._helpColorTransform.alphaMultiplier*100);this._intArray[n++]=Math.round(this._helpColorTransform.redMultiplier*100);this._intArray[n++]=Math.round(this._helpColorTransform.greenMultiplier*100);this._intArray[n++]=Math.round(this._helpColorTransform.blueMultiplier*100);this._intArray[n++]=Math.round(this._helpColorTransform.alphaOffset);this._intArray[n++]=Math.round(this._helpColorTransform.redOffset);this._intArray[n++]=Math.round(this._helpColorTransform.greenOffset);this._intArray[n++]=Math.round(this._helpColorTransform.blueOffset);n-=8;break}}if(n<0){if(this._defaultColorOffset<0){this._defaultColorOffset=n=this._intArray.length;this._intArray.length+=8;this._intArray[n++]=100;this._intArray[n++]=100;this._intArray[n++]=100;this._intArray[n++]=100;this._intArray[n++]=0;this._intArray[n++]=0;this._intArray[n++]=0;this._intArray[n++]=0}n=this._defaultColorOffset}var l=this._frameIntArray.length;this._frameIntArray.length+=1;this._frameIntArray[l]=n;return r};a.prototype._parseSlotFFDFrame=function(t,e,i){var r=this._frameFloatArray.length;var n=this._parseTweenFrame(t,e,i);var s=a.VERTICES in t?t[a.VERTICES]:null;var o=a._getNumber(t,a.OFFSET,0);var l=this._intArray[this._mesh.offset+0];var h=this._skin.name+"_"+this._slot.name+"_"+this._mesh.name;var u=0;var f=0;var _=0;var m=0;if(this._mesh.weight!==null){var p=this._weightSlotPose[h];this._helpMatrixA.copyFromArray(p,0);this._frameFloatArray.length+=this._mesh.weight.count*2;_=this._mesh.weight.offset+2+this._mesh.weight.bones.length}else{this._frameFloatArray.length+=l*2}for(var c=0;c<l*2;c+=2){if(s===null){u=0;f=0}else{if(c<o||c-o>=s.length){u=0}else{u=s[c-o]}if(c+1<o||c+1-o>=s.length){f=0}else{f=s[c+1-o]}}if(this._mesh.weight!==null){var d=this._weightBonePoses[h];var y=this._intArray[_++];this._helpMatrixA.transformPoint(u,f,this._helpPoint,true);u=this._helpPoint.x;f=this._helpPoint.y;for(var v=0;v<y;++v){var g=this._intArray[_++];this._helpMatrixB.copyFromArray(d,g*7+1);this._helpMatrixB.invert();this._helpMatrixB.transformPoint(u,f,this._helpPoint,true);this._frameFloatArray[r+m++]=this._helpPoint.x;this._frameFloatArray[r+m++]=this._helpPoint.y}}else{this._frameFloatArray[r+c]=u;this._frameFloatArray[r+c+1]=f}}if(e===0){var b=this._frameIntArray.length;this._frameIntArray.length+=1+1+1+1+1;this._frameIntArray[b+0]=this._mesh.offset;this._frameIntArray[b+1]=this._frameFloatArray.length-r;this._frameIntArray[b+2]=this._frameFloatArray.length-r;this._frameIntArray[b+3]=0;this._frameIntArray[b+4]=r;this._timelineArray[this._timeline.offset+3]=b-this._animation.frameIntOffset}return n};a.prototype._parseIKConstraintFrame=function(t,e,i){var r=this._parseTweenFrame(t,e,i);var n=this._frameIntArray.length;this._frameIntArray.length+=2;this._frameIntArray[n++]=a._getBoolean(t,a.BEND_POSITIVE,true)?1:0;this._frameIntArray[n++]=Math.round(a._getNumber(t,a.WEIGHT,1)*100);return r};a.prototype._parseActionData=function(e,i,r,n){var s=new Array;if(typeof e==="string"){var o=t.BaseObject.borrowObject(t.ActionData);o.type=i;o.name=e;o.bone=r;o.slot=n;s.push(o)}else if(e instanceof Array){for(var l=0,h=e;l<h.length;l++){var u=h[l];var o=t.BaseObject.borrowObject(t.ActionData);if(a.GOTO_AND_PLAY in u){o.type=0;o.name=a._getString(u,a.GOTO_AND_PLAY,"")}else{if(a.TYPE in u&&typeof u[a.TYPE]==="string"){o.type=a._getActionType(u[a.TYPE])}else{o.type=a._getNumber(u,a.TYPE,i)}o.name=a._getString(u,a.NAME,"")}if(a.BONE in u){var f=a._getString(u,a.BONE,"");o.bone=this._armature.getBone(f)}else{o.bone=r}if(a.SLOT in u){var _=a._getString(u,a.SLOT,"");o.slot=this._armature.getSlot(_)}else{o.slot=n}var m=null;if(a.INTS in u){if(m===null){m=t.BaseObject.borrowObject(t.UserData)}var p=u[a.INTS];for(var c=0,d=p;c<d.length;c++){var y=d[c];m.addInt(y)}}if(a.FLOATS in u){if(m===null){m=t.BaseObject.borrowObject(t.UserData)}var v=u[a.FLOATS];for(var g=0,b=v;g<b.length;g++){var y=b[g];m.addFloat(y)}}if(a.STRINGS in u){if(m===null){m=t.BaseObject.borrowObject(t.UserData)}var T=u[a.STRINGS];for(var A=0,D=T;A<D.length;A++){var y=D[A];m.addString(y)}}o.data=m;s.push(o)}}return s};a.prototype._parseTransform=function(e,i,r){i.x=a._getNumber(e,a.X,0)*r;i.y=a._getNumber(e,a.Y,0)*r;if(a.ROTATE in e||a.SKEW in e){i.rotation=t.Transform.normalizeRadian(a._getNumber(e,a.ROTATE,0)*t.Transform.DEG_RAD);i.skew=t.Transform.normalizeRadian(a._getNumber(e,a.SKEW,0)*t.Transform.DEG_RAD)}else if(a.SKEW_X in e||a.SKEW_Y in e){i.rotation=t.Transform.normalizeRadian(a._getNumber(e,a.SKEW_Y,0)*t.Transform.DEG_RAD);i.skew=t.Transform.normalizeRadian(a._getNumber(e,a.SKEW_X,0)*t.Transform.DEG_RAD)-i.rotation}i.scaleX=a._getNumber(e,a.SCALE_X,1);i.scaleY=a._getNumber(e,a.SCALE_Y,1)};a.prototype._parseColorTransform=function(t,e){e.alphaMultiplier=a._getNumber(t,a.ALPHA_MULTIPLIER,100)*.01;e.redMultiplier=a._getNumber(t,a.RED_MULTIPLIER,100)*.01;e.greenMultiplier=a._getNumber(t,a.GREEN_MULTIPLIER,100)*.01;e.blueMultiplier=a._getNumber(t,a.BLUE_MULTIPLIER,100)*.01;e.alphaOffset=a._getNumber(t,a.ALPHA_OFFSET,0);e.redOffset=a._getNumber(t,a.RED_OFFSET,0);e.greenOffset=a._getNumber(t,a.GREEN_OFFSET,0);e.blueOffset=a._getNumber(t,a.BLUE_OFFSET,0)};a.prototype._parseArray=function(t){t;this._intArray.length=0;this._floatArray.length=0;this._frameIntArray.length=0;this._frameFloatArray.length=0;this._frameArray.length=0;this._timelineArray.length=0};a.prototype._modifyArray=function(){if(this._intArray.length%Int16Array.BYTES_PER_ELEMENT!==0){this._intArray.push(0)}if(this._frameIntArray.length%Int16Array.BYTES_PER_ELEMENT!==0){this._frameIntArray.push(0)}if(this._frameArray.length%Int16Array.BYTES_PER_ELEMENT!==0){this._frameArray.push(0)}if(this._timelineArray.length%Uint16Array.BYTES_PER_ELEMENT!==0){this._timelineArray.push(0)}var e=this._intArray.length*Int16Array.BYTES_PER_ELEMENT;var i=this._floatArray.length*Float32Array.BYTES_PER_ELEMENT;var a=this._frameIntArray.length*Int16Array.BYTES_PER_ELEMENT;var r=this._frameFloatArray.length*Float32Array.BYTES_PER_ELEMENT;var n=this._frameArray.length*Int16Array.BYTES_PER_ELEMENT;var s=this._timelineArray.length*Uint16Array.BYTES_PER_ELEMENT;var o=e+i+a+r+n+s;if(t.DragonBones.webAssembly){var l=t.webAssemblyModule.HEAP16.buffer;var h=t.webAssemblyModule._malloc(o);var u=new Int16Array(l,h,this._intArray.length);var f=new Float32Array(l,h+e,this._floatArray.length);var _=new Int16Array(l,h+e+i,this._frameIntArray.length);var m=new Float32Array(l,h+e+i+a,this._frameFloatArray.length);var p=new Int16Array(l,h+e+i+a+r,this._frameArray.length);var c=new Uint16Array(l,h+e+i+a+r+n,this._timelineArray.length);for(var d=0,y=this._intArray.length;d<y;++d){u[d]=this._intArray[d]}for(var d=0,y=this._floatArray.length;d<y;++d){f[d]=this._floatArray[d]}for(var d=0,y=this._frameIntArray.length;d<y;++d){_[d]=this._frameIntArray[d]}for(var d=0,y=this._frameFloatArray.length;d<y;++d){m[d]=this._frameFloatArray[d]}for(var d=0,y=this._frameArray.length;d<y;++d){p[d]=this._frameArray[d]}for(var d=0,y=this._timelineArray.length;d<y;++d){c[d]=this._timelineArray[d]}t.webAssemblyModule.setDataBinary(this._data,h,e,i,a,r,n,s)}else{var v=new ArrayBuffer(o);var u=new Int16Array(v,0,this._intArray.length);var f=new Float32Array(v,e,this._floatArray.length);var _=new Int16Array(v,e+i,this._frameIntArray.length);var m=new Float32Array(v,e+i+a,this._frameFloatArray.length);var p=new Int16Array(v,e+i+a+r,this._frameArray.length);var c=new Uint16Array(v,e+i+a+r+n,this._timelineArray.length);for(var d=0,y=this._intArray.length;d<y;++d){u[d]=this._intArray[d]}for(var d=0,y=this._floatArray.length;d<y;++d){f[d]=this._floatArray[d]}for(var d=0,y=this._frameIntArray.length;d<y;++d){_[d]=this._frameIntArray[d]}for(var d=0,y=this._frameFloatArray.length;d<y;++d){m[d]=this._frameFloatArray[d]}for(var d=0,y=this._frameArray.length;d<y;++d){p[d]=this._frameArray[d]}for(var d=0,y=this._timelineArray.length;d<y;++d){c[d]=this._timelineArray[d]}this._data.binary=v;this._data.intArray=u;this._data.floatArray=f;this._data.frameIntArray=_;this._data.frameFloatArray=m;this._data.frameArray=p;this._data.timelineArray=c}this._defaultColorOffset=-1};a.prototype.parseDragonBonesData=function(e,i){if(i===void 0){i=1}console.assert(e!==null&&e!==undefined,"Data error.");var r=a._getString(e,a.VERSION,"");var n=a._getString(e,a.COMPATIBLE_VERSION,"");if(a.DATA_VERSIONS.indexOf(r)>=0||a.DATA_VERSIONS.indexOf(n)>=0){var s=t.BaseObject.borrowObject(t.DragonBonesData);s.version=r;s.name=a._getString(e,a.NAME,"");s.frameRate=a._getNumber(e,a.FRAME_RATE,24);if(s.frameRate===0){s.frameRate=24}if(a.ARMATURE in e){this._data=s;this._parseArray(e);var o=e[a.ARMATURE];for(var l=0,h=o;l<h.length;l++){var u=h[l];s.addArmature(this._parseArmature(u,i))}if(!this._data.binary){this._modifyArray()}if(a.STAGE in e){s.stage=s.getArmature(a._getString(e,a.STAGE,""))}else if(s.armatureNames.length>0){s.stage=s.getArmature(s.armatureNames[0])}this._data=null}if(a.TEXTURE_ATLAS in e){this._rawTextureAtlases=e[a.TEXTURE_ATLAS]}return s}else{console.assert(false,"Nonsupport data version: "+r+"\n"+"Please convert DragonBones data to support version.\n"+"Read more: https://github.com/DragonBones/Tools/")}return null};a.prototype.parseTextureAtlasData=function(e,i,r){if(r===void 0){r=1}console.assert(e!==undefined);if(e===null){if(this._rawTextureAtlases===null||this._rawTextureAtlases.length===0){return false}var n=this._rawTextureAtlases[this._rawTextureAtlasIndex++];this.parseTextureAtlasData(n,i,r);if(this._rawTextureAtlasIndex>=this._rawTextureAtlases.length){this._rawTextureAtlasIndex=0;this._rawTextureAtlases=null}return true}i.width=a._getNumber(e,a.WIDTH,0);i.height=a._getNumber(e,a.HEIGHT,0);i.scale=r===1?1/a._getNumber(e,a.SCALE,1):r;i.name=a._getString(e,a.NAME,"");i.imagePath=a._getString(e,a.IMAGE_PATH,"");if(a.SUB_TEXTURE in e){var s=e[a.SUB_TEXTURE];for(var o=0,l=s.length;o<l;++o){var h=s[o];var u=i.createTexture();u.rotated=a._getBoolean(h,a.ROTATED,false);u.name=a._getString(h,a.NAME,"");u.region.x=a._getNumber(h,a.X,0);u.region.y=a._getNumber(h,a.Y,0);u.region.width=a._getNumber(h,a.WIDTH,0);u.region.height=a._getNumber(h,a.HEIGHT,0);var f=a._getNumber(h,a.FRAME_WIDTH,-1);var _=a._getNumber(h,a.FRAME_HEIGHT,-1);if(f>0&&_>0){u.frame=t.TextureData.createRectangle();u.frame.x=a._getNumber(h,a.FRAME_X,0);u.frame.y=a._getNumber(h,a.FRAME_Y,0);u.frame.width=f;u.frame.height=_}i.addTexture(u)}}return true};a.getInstance=function(){if(a._objectDataParserInstance===null){a._objectDataParserInstance=new a}return a._objectDataParserInstance};a._objectDataParserInstance=null;return a}(t.DataParser);t.ObjectDataParser=e;var i=function(){function t(){this.frameStart=0;this.actions=[]}return t}();t.ActionFrame=i})(dragonBones||(dragonBones={}));var dragonBones;(function(t){var e=function(e){__extends(i,e);function i(){return e!==null&&e.apply(this,arguments)||this}i.prototype._inRange=function(t,e,i){return e<=t&&t<=i};i.prototype._decodeUTF8=function(t){var e=-1;var i=-1;var a=65533;var r=0;var n="";var s;var o=0;var l=0;var h=0;var u=0;while(t.length>r){var f=t[r++];if(f===e){if(l!==0){s=a}else{s=i}}else{if(l===0){if(this._inRange(f,0,127)){s=f}else{if(this._inRange(f,194,223)){l=1;u=128;o=f-192}else if(this._inRange(f,224,239)){l=2;u=2048;o=f-224}else if(this._inRange(f,240,244)){l=3;u=65536;o=f-240}else{}o=o*Math.pow(64,l);s=null}}else if(!this._inRange(f,128,191)){o=0;l=0;h=0;u=0;r--;s=f}else{h+=1;o=o+(f-128)*Math.pow(64,l-h);if(h!==l){s=null}else{var _=o;var m=u;o=0;l=0;h=0;u=0;if(this._inRange(_,m,1114111)&&!this._inRange(_,55296,57343)){s=_}else{s=f}}}}if(s!==null&&s!==i){if(s<=65535){if(s>0)n+=String.fromCharCode(s)}else{s-=65536;n+=String.fromCharCode(55296+(s>>10&1023));n+=String.fromCharCode(56320+(s&1023))}}}return n};i.prototype._getUTF16Key=function(t){for(var e=0,i=t.length;e<i;++e){if(t.charCodeAt(e)>255){return encodeURI(t)}}return t};i.prototype._parseBinaryTimeline=function(e,i,a){if(a===void 0){a=null}var r=a!==null?a:t.BaseObject.borrowObject(t.TimelineData);r.type=e;r.offset=i;this._timeline=r;var n=this._timelineArrayBuffer[r.offset+2];if(n===1){r.frameIndicesOffset=-1}else{var s=0;var o=this._animation.frameCount+1;var l=this._data.frameIndices;if(t.DragonBones.webAssembly){s=l.size();l.resize(s+o,0)}else{s=l.length;l.length+=o}r.frameIndicesOffset=s;for(var h=0,u=0,f=0,_=0;h<o;++h){if(f+_<=h&&u<n){f=this._frameArrayBuffer[this._animation.frameOffset+this._timelineArrayBuffer[r.offset+5+u]];if(u===n-1){_=this._animation.frameCount-f}else{_=this._frameArrayBuffer[this._animation.frameOffset+this._timelineArrayBuffer[r.offset+5+u+1]]-f}u++}if(t.DragonBones.webAssembly){l.set(s+h,u-1)}else{l[s+h]=u-1}}}this._timeline=null;return r};i.prototype._parseMesh=function(e,i){i.offset=e[t.ObjectDataParser.OFFSET];var a=this._intArrayBuffer[i.offset+3];if(a>=0){var r=t.BaseObject.borrowObject(t.WeightData);var n=this._intArrayBuffer[i.offset+0];var s=this._intArrayBuffer[a+0];r.offset=a;for(var o=0;o<s;++o){var l=this._intArrayBuffer[a+2+o];r.addBone(this._rawBones[l])}var h=a+2+s;var u=0;for(var o=0,f=n;o<f;++o){var _=this._intArrayBuffer[h++];u+=_;h+=_}r.count=u;i.weight=r}};i.prototype._parseAnimation=function(e){var i=t.BaseObject.borrowObject(t.AnimationData);i.frameCount=Math.max(t.ObjectDataParser._getNumber(e,t.ObjectDataParser.DURATION,1),1);i.playTimes=t.ObjectDataParser._getNumber(e,t.ObjectDataParser.PLAY_TIMES,1);i.duration=i.frameCount/this._armature.frameRate;i.fadeInTime=t.ObjectDataParser._getNumber(e,t.ObjectDataParser.FADE_IN_TIME,0);i.scale=t.ObjectDataParser._getNumber(e,t.ObjectDataParser.SCALE,1);i.name=t.ObjectDataParser._getString(e,t.ObjectDataParser.NAME,t.ObjectDataParser.DEFAULT_NAME);if(i.name.length===0){i.name=t.ObjectDataParser.DEFAULT_NAME}var a=e[t.ObjectDataParser.OFFSET];i.frameIntOffset=a[0];i.frameFloatOffset=a[1];i.frameOffset=a[2];this._animation=i;if(t.ObjectDataParser.ACTION in e){i.actionTimeline=this._parseBinaryTimeline(0,e[t.ObjectDataParser.ACTION])}if(t.ObjectDataParser.Z_ORDER in e){i.zOrderTimeline=this._parseBinaryTimeline(1,e[t.ObjectDataParser.Z_ORDER])}if(t.ObjectDataParser.BONE in e){var r=e[t.ObjectDataParser.BONE];for(var n in r){var s=r[n];if(t.DragonBones.webAssembly){n=this._getUTF16Key(n)}var o=this._armature.getBone(n);if(o===null){continue}for(var l=0,h=s.length;l<h;l+=2){var u=s[l];var f=s[l+1];var _=this._parseBinaryTimeline(u,f);this._animation.addBoneTimeline(o,_)}}}if(t.ObjectDataParser.SLOT in e){var r=e[t.ObjectDataParser.SLOT];for(var n in r){var s=r[n];if(t.DragonBones.webAssembly){n=this._getUTF16Key(n)}var m=this._armature.getSlot(n);if(m===null){continue}for(var l=0,h=s.length;l<h;l+=2){var u=s[l];var f=s[l+1];var _=this._parseBinaryTimeline(u,f);this._animation.addSlotTimeline(m,_)}}}if(t.ObjectDataParser.CONSTRAINT in e){var r=e[t.ObjectDataParser.CONSTRAINT];for(var n in r){var s=r[n];if(t.DragonBones.webAssembly){n=this._getUTF16Key(n)}var p=this._armature.getConstraint(n);if(p===null){continue}for(var l=0,h=s.length;l<h;l+=2){var u=s[l];var f=s[l+1];var _=this._parseBinaryTimeline(u,f);this._animation.addConstraintTimeline(p,_)}}}this._animation=null;return i};i.prototype._parseArray=function(e){var i=e[t.ObjectDataParser.OFFSET];var a=i[1];var r=i[3];var n=i[5];var s=i[7];var o=i[9];var l=i[11];var h=new Int16Array(this._binary,this._binaryOffset+i[0],a/Int16Array.BYTES_PER_ELEMENT);var u=new Float32Array(this._binary,this._binaryOffset+i[2],r/Float32Array.BYTES_PER_ELEMENT);var f=new Int16Array(this._binary,this._binaryOffset+i[4],n/Int16Array.BYTES_PER_ELEMENT);var _=new Float32Array(this._binary,this._binaryOffset+i[6],s/Float32Array.BYTES_PER_ELEMENT);var m=new Int16Array(this._binary,this._binaryOffset+i[8],o/Int16Array.BYTES_PER_ELEMENT);var p=new Uint16Array(this._binary,this._binaryOffset+i[10],l/Uint16Array.BYTES_PER_ELEMENT);if(t.DragonBones.webAssembly){var c=a+r+n+s+o+l;var d=t.webAssemblyModule._malloc(c);var y=new Uint8Array(this._binary,this._binaryOffset,c/Uint8Array.BYTES_PER_ELEMENT);var v=new Uint8Array(t.webAssemblyModule.HEAP16.buffer,d,y.length);for(var g=0,b=y.length;g<b;++g){v[g]=y[g]}t.webAssemblyModule.setDataBinary(this._data,d,a,r,n,s,o,l);this._intArrayBuffer=h;this._floatArrayBuffer=u;this._frameIntArrayBuffer=f;this._frameFloatArrayBuffer=_;this._frameArrayBuffer=m;this._timelineArrayBuffer=p}else{this._data.binary=this._binary;this._data.intArray=this._intArrayBuffer=h;this._data.floatArray=this._floatArrayBuffer=u;this._data.frameIntArray=this._frameIntArrayBuffer=f;this._data.frameFloatArray=this._frameFloatArrayBuffer=_;this._data.frameArray=this._frameArrayBuffer=m;this._data.timelineArray=this._timelineArrayBuffer=p}};i.prototype.parseDragonBonesData=function(t,i){if(i===void 0){i=1}console.assert(t!==null&&t!==undefined&&t instanceof ArrayBuffer,"Data error.");var a=new Uint8Array(t,0,8);if(a[0]!=="D".charCodeAt(0)||a[1]!=="B".charCodeAt(0)||a[2]!=="D".charCodeAt(0)||a[3]!=="T".charCodeAt(0)){console.assert(false,"Nonsupport data.");return null}var r=new Uint32Array(t,8,1)[0];var n=new Uint8Array(t,8+4,r);var s=this._decodeUTF8(n);var o=JSON.parse(s);this._binaryOffset=8+4+r;this._binary=t;return e.prototype.parseDragonBonesData.call(this,o,i)};i.getInstance=function(){if(i._binaryDataParserInstance===null){i._binaryDataParserInstance=new i}return i._binaryDataParserInstance};i._binaryDataParserInstance=null;return i}(t.ObjectDataParser);t.BinaryDataParser=e})(dragonBones||(dragonBones={}));var dragonBones;(function(t){var e=function(){function e(i){if(i===void 0){i=null}this.autoSearch=false;this._dragonBonesDataMap={};this._textureAtlasDataMap={};this._dragonBones=null;this._dataParser=null;if(e._objectParser===null){e._objectParser=new t.ObjectDataParser}if(e._binaryParser===null){e._binaryParser=new t.BinaryDataParser}this._dataParser=i!==null?i:e._objectParser}e.prototype._isSupportMesh=function(){return true};e.prototype._getTextureData=function(t,e){if(t in this._textureAtlasDataMap){for(var i=0,a=this._textureAtlasDataMap[t];i<a.length;i++){var r=a[i];var n=r.getTexture(e);if(n!==null){return n}}}if(this.autoSearch){for(var s in this._textureAtlasDataMap){for(var o=0,l=this._textureAtlasDataMap[s];o<l.length;o++){var r=l[o];if(r.autoSearch){var n=r.getTexture(e);if(n!==null){return n}}}}}return null};e.prototype._fillBuildArmaturePackage=function(t,e,i,a,r){var n=null;var s=null;if(e.length>0){if(e in this._dragonBonesDataMap){n=this._dragonBonesDataMap[e];s=n.getArmature(i)}}if(s===null&&(e.length===0||this.autoSearch)){for(var o in this._dragonBonesDataMap){n=this._dragonBonesDataMap[o];if(e.length===0||n.autoSearch){s=n.getArmature(i);if(s!==null){e=o;break}}}}if(s!==null){t.dataName=e;t.textureAtlasName=r;t.data=n;t.armature=s;t.skin=null;if(a.length>0){t.skin=s.getSkin(a);if(t.skin===null&&this.autoSearch){for(var o in this._dragonBonesDataMap){var l=this._dragonBonesDataMap[o];var h=l.getArmature(a);if(h!==null){t.skin=h.defaultSkin;break}}}}if(t.skin===null){t.skin=s.defaultSkin}return true}return false};e.prototype._buildBones=function(e,i){for(var a=0,r=e.armature.sortedBones;a<r.length;a++){var n=r[a];var s=t.BaseObject.borrowObject(t.Bone);s.init(n);if(n.parent!==null){i.addBone(s,n.parent.name)}else{i.addBone(s,"")}}var o=e.armature.constraints;for(var l in o){var h=o[l];var u=t.BaseObject.borrowObject(t.IKConstraint);u.init(h,i);i.addConstraint(u)}};e.prototype._buildSlots=function(e,i){var a=e.skin;var r=e.armature.defaultSkin;if(a===null||r===null){return}var n={};for(var s in r.displays){var o=r.getDisplays(s);n[s]=o}if(a!==r){for(var s in a.displays){var o=a.getDisplays(s);n[s]=o}}for(var l=0,h=e.armature.sortedSlots;l<h.length;l++){var u=h[l];var o=u.name in n?n[u.name]:null;var f=this._buildSlot(e,u,o,i);i.addSlot(f,u.parent.name);if(o!==null){var _=new Array;for(var m=0,p=t.DragonBones.webAssembly?o.size():o.length;m<p;++m){var c=t.DragonBones.webAssembly?o.get(m):o[m];if(c!==null){_.push(this._getSlotDisplay(e,c,null,f))}else{_.push(null)}}f._setDisplayList(_)}f._setDisplayIndex(u.displayIndex,true)}};e.prototype._buildChildArmature=function(t,e,i){e;return this.buildArmature(i.path,t!==null?t.dataName:"","",t!==null?t.textureAtlasName:"")};e.prototype._getSlotDisplay=function(t,e,i,a){var r=t!==null?t.dataName:e.parent.parent.parent.name;var n=null;switch(e.type){case 0:{var s=e;if(s.texture===null){s.texture=this._getTextureData(r,e.path)}else if(t!==null&&t.textureAtlasName.length>0){s.texture=this._getTextureData(t.textureAtlasName,e.path)}if(i!==null&&i.type===2&&this._isSupportMesh()){n=a.meshDisplay}else{n=a.rawDisplay}break}case 2:{var o=e;if(o.texture===null){o.texture=this._getTextureData(r,o.path)}else if(t!==null&&t.textureAtlasName.length>0){o.texture=this._getTextureData(t.textureAtlasName,o.path)}if(this._isSupportMesh()){n=a.meshDisplay}else{n=a.rawDisplay}break}case 1:{var l=e;var h=this._buildChildArmature(t,a,e);if(h!==null){h.inheritAnimation=l.inheritAnimation;if(!h.inheritAnimation){var u=l.actions.length>0?l.actions:h.armatureData.defaultActions;if(u.length>0){for(var f=0,_=u;f<_.length;f++){var m=_[f];h._bufferAction(m,true)}}else{h.animation.play()}}l.armature=h.armatureData}n=h;break}case 3:break;default:break}return n};e.prototype.parseDragonBonesData=function(t,i,a){if(i===void 0){i=null}if(a===void 0){a=1}var r=t instanceof ArrayBuffer?e._binaryParser:this._dataParser;var n=r.parseDragonBonesData(t,a);while(true){var s=this._buildTextureAtlasData(null,null);if(r.parseTextureAtlasData(null,s,a)){this.addTextureAtlasData(s,i)}else{s.returnToPool();break}}if(n!==null){this.addDragonBonesData(n,i)}return n};e.prototype.parseTextureAtlasData=function(t,e,i,a){if(i===void 0){i=null}if(a===void 0){a=1}var r=this._buildTextureAtlasData(null,null);this._dataParser.parseTextureAtlasData(t,r,a);this._buildTextureAtlasData(r,e||null);this.addTextureAtlasData(r,i);return r};e.prototype.updateTextureAtlasData=function(t,e){var i=this.getTextureAtlasData(t);if(i!==null){for(var a=0,r=i.length;a<r;++a){if(a<e.length){this._buildTextureAtlasData(i[a],e[a])}}}};e.prototype.getDragonBonesData=function(t){return t in this._dragonBonesDataMap?this._dragonBonesDataMap[t]:null};e.prototype.addDragonBonesData=function(t,e){if(e===void 0){e=null}e=e!==null?e:t.name;if(e in this._dragonBonesDataMap){if(this._dragonBonesDataMap[e]===t){return}console.warn("Can not add same name data: "+e);return}this._dragonBonesDataMap[e]=t};e.prototype.removeDragonBonesData=function(t,e){if(e===void 0){e=true}if(t in this._dragonBonesDataMap){if(e){this._dragonBones.bufferObject(this._dragonBonesDataMap[t])}delete this._dragonBonesDataMap[t]}};e.prototype.getTextureAtlasData=function(t){return t in this._textureAtlasDataMap?this._textureAtlasDataMap[t]:null};e.prototype.addTextureAtlasData=function(t,e){if(e===void 0){e=null}e=e!==null?e:t.name;var i=e in this._textureAtlasDataMap?this._textureAtlasDataMap[e]:this._textureAtlasDataMap[e]=[];if(i.indexOf(t)<0){i.push(t)}};e.prototype.removeTextureAtlasData=function(t,e){if(e===void 0){e=true}if(t in this._textureAtlasDataMap){var i=this._textureAtlasDataMap[t];if(e){for(var a=0,r=i;a<r.length;a++){var n=r[a];this._dragonBones.bufferObject(n)}}delete this._textureAtlasDataMap[t]}};e.prototype.getArmatureData=function(t,e){if(e===void 0){e=""}var a=new i;if(!this._fillBuildArmaturePackage(a,e,t,"","")){return null}return a.armature};e.prototype.clear=function(t){if(t===void 0){t=true}for(var e in this._dragonBonesDataMap){if(t){this._dragonBones.bufferObject(this._dragonBonesDataMap[e])}delete this._dragonBonesDataMap[e]}for(var e in this._textureAtlasDataMap){if(t){var i=this._textureAtlasDataMap[e];for(var a=0,r=i;a<r.length;a++){var n=r[a];this._dragonBones.bufferObject(n)}}delete this._textureAtlasDataMap[e]}};e.prototype.buildArmature=function(t,e,a,r){if(e===void 0){e=""}if(a===void 0){a=""}if(r===void 0){r=""}var n=new i;if(!this._fillBuildArmaturePackage(n,e||"",t,a||"",r||"")){console.warn("No armature data: "+t+", "+(e!==null?e:""));return null}var s=this._buildArmature(n);this._buildBones(n,s);this._buildSlots(n,s);s.invalidUpdate(null,true);s.advanceTime(0);return s};e.prototype.replaceDisplay=function(e,i,a){if(a===void 0){a=-1}if(a<0){a=e.displayIndex}if(a<0){a=0}e.replaceDisplayData(i,a);var r=e.displayList;if(r.length<=a){r.length=a+1;for(var n=0,s=r.length;n<s;++n){if(!r[n]){r[n]=null}}}if(i!==null){var o=e.rawDisplayDatas;var l=null;if(o){if(t.DragonBones.webAssembly){if(a<o.size()){l=o.get(a)}}else{if(a<o.length){l=o[a]}}}r[a]=this._getSlotDisplay(null,i,l,e)}else{r[a]=null}e.displayList=r};e.prototype.replaceSlotDisplay=function(t,e,i,a,r,n){if(n===void 0){n=-1}var s=this.getArmatureData(e,t||"");if(!s||!s.defaultSkin){return false}var o=s.defaultSkin.getDisplay(i,a);if(!o){return false}this.replaceDisplay(r,o,n);return true};e.prototype.replaceSlotDisplayList=function(e,i,a,r){var n=this.getArmatureData(i,e||"");if(!n||!n.defaultSkin){return false}var s=n.defaultSkin.getDisplays(a);if(!s){return false}var o=0;for(var l=0,h=t.DragonBones.webAssembly?s.size():s.length;l<h;++l){var u=t.DragonBones.webAssembly?s.get(l):s[l];this.replaceDisplay(r,u,o++)}return true};e.prototype.replaceSkin=function(e,i,a,r){if(a===void 0){a=false}if(r===void 0){r=null}var n=false;for(var s=0,o=e.getSlots();s<o.length;s++){var l=o[s];if(r!==null&&r.indexOf(l.name)>=0){continue}var h=i.getDisplays(l.name);if(!h){if(a){l.rawDisplayDatas=null;l.displayList=[]}continue}var u=t.DragonBones.webAssembly?h.size():h.length;var f=l.displayList;f.length=u;for(var _=0,m=u;_<m;++_){var p=t.DragonBones.webAssembly?h.get(_):h[_];if(p!==null){f[_]=this._getSlotDisplay(null,p,null,l)}else{f[_]=null}}n=true;l.rawDisplayDatas=h;l.displayList=f}return n};e.prototype.replaceAnimation=function(e,i,a){if(a===void 0){a=true}var r=i.defaultSkin;if(r===null){return false}if(a){e.animation.animations=i.animations}else{var n=e.animation.animations;var s={};for(var o in n){s[o]=n[o]}for(var o in i.animations){s[o]=i.animations[o]}e.animation.animations=s}for(var l=0,h=e.getSlots();l<h.length;l++){var u=h[l];var f=0;for(var _=0,m=u.displayList;_<m.length;_++){var p=m[_];if(p instanceof t.Armature){var c=r.getDisplays(u.name);if(c!==null&&f<(t.DragonBones.webAssembly?c.size():c.length)){var d=t.DragonBones.webAssembly?c.get(f):c[f];if(d!==null&&d.type===1){var y=this.getArmatureData(d.path,d.parent.parent.parent.name);if(y){this.replaceAnimation(p,y,a)}}}}f++}}return true};e.prototype.getAllDragonBonesData=function(){return this._dragonBonesDataMap};e.prototype.getAllTextureAtlasData=function(){return this._textureAtlasDataMap};Object.defineProperty(e.prototype,"clock",{get:function(){return this._dragonBones.clock},enumerable:true,configurable:true});Object.defineProperty(e.prototype,"dragonBones",{get:function(){return this._dragonBones},enumerable:true,configurable:true});e.prototype.changeSkin=function(t,e,i){if(i===void 0){i=null}return this.replaceSkin(t,e,false,i)};e.prototype.copyAnimationsToArmature=function(t,e,i,a,r){if(i===void 0){i=""}if(a===void 0){a=""}if(r===void 0){r=true}i;var n=this.getArmatureData(e,a);if(!n){return false}return this.replaceAnimation(t,n,r)};e._objectParser=null;e._binaryParser=null;return e}();t.BaseFactory=e;var i=function(){function t(){this.dataName="";this.textureAtlasName="";this.skin=null}return t}();t.BuildArmaturePackage=i})(dragonBones||(dragonBones={}));var dragonBones;(function(t){var e=function(e){__extends(a,e);function a(){var t=e!==null&&e.apply(this,arguments)||this;t._renderTexture=null;return t}a.toString=function(){return"[class dragonBones.PhaserTextureAtlasData]"};a.prototype._onClear=function(){e.prototype._onClear.call(this);if(this._renderTexture!==null){}this._renderTexture=null};a.prototype.createTexture=function(){return t.BaseObject.borrowObject(i)};Object.defineProperty(a.prototype,"renderTexture",{get:function(){return this._renderTexture},set:function(t){if(this._renderTexture===t){return}this._renderTexture=t;if(this._renderTexture!==null){for(var e in this.textures){var i=this.textures[e];i.renderTexture=new PIXI.Texture(this._renderTexture,i.region,i.region,new PIXI.Rectangle(0,0,i.region.width,i.region.height))}}else{for(var e in this.textures){var i=this.textures[e];i.renderTexture=null}}},enumerable:true,configurable:true});return a}(t.TextureAtlasData);t.PhaserTextureAtlasData=e;var i=function(t){__extends(e,t);function e(){var e=t!==null&&t.apply(this,arguments)||this;e.renderTexture=null;return e}e.toString=function(){return"[class dragonBones.PhaserTextureData]"};e.prototype._onClear=function(){t.prototype._onClear.call(this);if(this.renderTexture!==null){this.renderTexture.destroy(false)}this.renderTexture=null};return e}(t.TextureData);t.PhaserTextureData=i})(dragonBones||(dragonBones={}));var dragonBones;(function(t){var e=function(e){__extends(i,e);function i(){var i=e.call(this,t.PhaserFactory._game,0,0)||this;i._debugDraw=false;i._disposeProxy=false;i._armature=null;i._signals={};return i}i.prototype.dbInit=function(t){this._armature=t};i.prototype.dbClear=function(){for(var t in this._signals){var i=this._signals[t];i.removeAll();i.dispose();delete this._signals[t]}this._disposeProxy=false;this._armature=null;e.prototype.destroy.call(this,false)};i.prototype.dbUpdate=function(){var e=t.DragonBones.debugDraw;if(e||this._debugDraw){this._debugDraw=e}};i.prototype.dispose=function(t){if(t===void 0){t=true}this._disposeProxy=t;if(this._armature!==null){this._armature.dispose();this._armature=null}};i.prototype.destroy=function(){this.dispose()};i.prototype.dispatchDBEvent=function(t,e){if(!(t in this._signals)){this._signals[t]=new Phaser.Signal}var i=this._signals[t];i.dispatch(e)};i.prototype.hasDBEventListener=function(t){return t in this._signals&&this._signals[t].getNumListeners()>0};i.prototype.addDBEventListener=function(t,e,i){if(!(t in this._signals)){this._signals[t]=new Phaser.Signal}var a=this._signals[t];a.add(e,i)};i.prototype.removeDBEventListener=function(t,e,i){if(t in this._signals){var a=this._signals[t];a.remove(e,i)}};Object.defineProperty(i.prototype,"armature",{get:function(){return this._armature},enumerable:true,configurable:true});Object.defineProperty(i.prototype,"animation",{get:function(){return this._armature.animation},enumerable:true,configurable:true});i.prototype.hasEvent=function(t){return this.hasDBEventListener(t)};i.prototype.addEvent=function(t,e,i){this.addDBEventListener(t,e,i)};i.prototype.removeEvent=function(t,e,i){this.removeDBEventListener(t,e,i)};return i}(Phaser.Sprite);t.PhaserArmatureDisplay=e;Phaser.Image.prototype.updateTransform=function(e){if(!e&&!this.parent&&!this.game){return this}var i=this.parent;if(e){i=e}else if(!this.parent){i=this.game.world}var a=i.worldTransform;var r=this.worldTransform;var n,s,o,l,h,u;if(this.rotation%Phaser.Math.PI2){if(this.rotation!==this.rotationCache){this.rotationCache=this.rotation;this._sr=Math.sin(this.rotation);this._cr=Math.cos(this.rotation)}var f=this.skew%t.Transform.PI_D;if(f>.01||f<-.01){n=this._cr*this.scale.x;s=this._sr*this.scale.x;o=-Math.sin(f+this.rotation)*this.scale.y;l=Math.cos(f+this.rotation)*this.scale.y;h=this.position.x;u=this.position.y}else{n=this._cr*this.scale.x;s=this._sr*this.scale.x;o=-this._sr*this.scale.y;l=this._cr*this.scale.y;h=this.position.x;u=this.position.y}if(this.pivot.x||this.pivot.y){h-=this.pivot.x*n+this.pivot.y*o;u-=this.pivot.x*s+this.pivot.y*l}r.a=n*a.a+s*a.c;r.b=n*a.b+s*a.d;r.c=o*a.a+l*a.c;r.d=o*a.b+l*a.d;r.tx=h*a.a+u*a.c+a.tx;r.ty=h*a.b+u*a.d+a.ty}else{n=this.scale.x;s=0;o=0;l=this.scale.y;h=this.position.x-this.pivot.x*n;u=this.position.y-this.pivot.y*l;r.a=n*a.a;r.b=n*a.b;r.c=l*a.c;r.d=l*a.d;r.tx=h*a.a+u*a.c+a.tx;r.ty=h*a.b+u*a.d+a.ty}n=r.a;s=r.b;o=r.c;l=r.d;var _=n*l-s*o;if(n||s){var m=Math.sqrt(n*n+s*s);this.worldRotation=s>0?Math.acos(n/m):-Math.acos(n/m);this.worldScale.x=m;this.worldScale.y=_/m}else if(o||l){var p=Math.sqrt(o*o+l*l);this.worldRotation=Phaser.Math.HALF_PI-(l>0?Math.acos(-o/p):-Math.acos(o/p));this.worldScale.x=_/p;this.worldScale.y=p}else{this.worldScale.x=0;this.worldScale.y=0}this.worldAlpha=this.alpha*i.worldAlpha;this.worldPosition.x=r.tx;this.worldPosition.y=r.ty;this._currentBounds=null;if(this.transformCallback){this.transformCallback.call(this.transformCallbackContext,r,a)}return this}})(dragonBones||(dragonBones={}));var dragonBones;(function(t){var e=function(e){__extends(i,e);function i(){return e!==null&&e.apply(this,arguments)||this}i.toString=function(){return"[class dragonBones.PhaserSlot]"};i.prototype._onClear=function(){e.prototype._onClear.call(this);this._textureScale=1;this._renderDisplay=null};i.prototype._initDisplay=function(t){t};i.prototype._disposeDisplay=function(t){t.destroy(true)};i.prototype._onUpdateDisplay=function(){this._renderDisplay=this._display?this._display:this._rawDisplay};i.prototype._addDisplay=function(){var t=this._armature.display;t.addChild(this._renderDisplay)};i.prototype._replaceDisplay=function(t){var e=this._armature.display;var i=t;e.addChild(this._renderDisplay);e.swapChildren(this._renderDisplay,i);e.removeChild(i);this._textureScale=1};i.prototype._removeDisplay=function(){this._renderDisplay.parent.removeChild(this._renderDisplay)};i.prototype._updateZOrder=function(){var t=this._armature.display;var e=t.getChildIndex(this._renderDisplay);if(e===this._zOrder){return}t.addChildAt(this._renderDisplay,this._zOrder)};i.prototype._updateVisible=function(){this._renderDisplay.visible=this._parent.visible&&this._visible};i.prototype._updateBlendMode=function(){if(this._renderDisplay instanceof PIXI.Sprite){switch(this._blendMode){case 0:this._renderDisplay.blendMode=PIXI.blendModes.NORMAL;break;case 1:this._renderDisplay.blendMode=PIXI.blendModes.ADD;break;case 3:this._renderDisplay.blendMode=PIXI.blendModes.DARKEN;break;case 4:this._renderDisplay.blendMode=PIXI.blendModes.DIFFERENCE;break;case 6:this._renderDisplay.blendMode=PIXI.blendModes.HARD_LIGHT;break;case 9:this._renderDisplay.blendMode=PIXI.blendModes.LIGHTEN;break;case 10:this._renderDisplay.blendMode=PIXI.blendModes.MULTIPLY;break;case 11:this._renderDisplay.blendMode=PIXI.blendModes.OVERLAY;break;case 12:this._renderDisplay.blendMode=PIXI.blendModes.SCREEN;break;default:break}}};i.prototype._updateColor=function(){this._renderDisplay.alpha=this._colorTransform.alphaMultiplier;if(this._renderDisplay instanceof PIXI.Sprite){var t=(Math.round(this._colorTransform.redMultiplier*255)<<16)+(Math.round(this._colorTransform.greenMultiplier*255)<<8)+Math.round(this._colorTransform.blueMultiplier*255);this._renderDisplay.tint=t}};i.prototype._updateFrame=function(){var e=this._display===this._meshDisplay?this._meshData:null;var i=this._textureData;if(this._displayIndex>=0&&this._display!==null&&i!==null){var a=i.parent;if(this._armature.replacedTexture!==null&&this._rawDisplayDatas!==null&&this._rawDisplayDatas.indexOf(this._displayData)>=0){if(this._armature._replaceTextureAtlasData===null){a=t.BaseObject.borrowObject(t.PhaserTextureAtlasData);a.copyFrom(i.parent);a.renderTexture=this._armature.replacedTexture;this._armature._replaceTextureAtlasData=a}else{a=this._armature._replaceTextureAtlasData}i=a.getTexture(i.name)}var r=i.renderTexture;if(r!==null){if(e!==null){}else{this._textureScale=i.parent.scale*this._armature._armatureData.scale;var n=this._renderDisplay;n.setTexture(r)}this._visibleDirty=true;return}}if(e!==null){}else{var n=this._renderDisplay;n.x=0;n.y=0;n.visible=false}};i.prototype._updateMesh=function(){};i.prototype._updateTransform=function(t){if(t){this._renderDisplay.x=0;this._renderDisplay.y=0;this._renderDisplay.rotation=0;this._renderDisplay.skew=0;this._renderDisplay.scale.x=1;this._renderDisplay.scale.y=1}else{this.updateGlobalTransform();var e=this.global;if(this._renderDisplay===this._rawDisplay||this._renderDisplay===this._meshDisplay){var i=e.x-(this.globalTransformMatrix.a*this._pivotX+this.globalTransformMatrix.c*this._pivotY);var a=e.y-(this.globalTransformMatrix.b*this._pivotX+this.globalTransformMatrix.d*this._pivotY);this._renderDisplay.x=i;this._renderDisplay.y=a}else{this._renderDisplay.x=e.x;this._renderDisplay.y=e.y}this._renderDisplay.rotation=e.rotation;this._renderDisplay.skew=e.skew;this._renderDisplay.scale.x=e.scaleX*this._textureScale;this._renderDisplay.scale.y=e.scaleY*this._textureScale}};return i}(t.Slot);t.PhaserSlot=e})(dragonBones||(dragonBones={}));var dragonBones;(function(t){var e=function(e){__extends(i,e);function i(t){if(t===void 0){t=null}var a=e.call(this,t)||this;a._dragonBones=i._dragonBonesInstance;return a}i.init=function(e){if(i._game!==null){return}i._game=e;var a=new t.PhaserArmatureDisplay;i._dragonBonesInstance=new t.DragonBones(a)};Object.defineProperty(i,"factory",{get:function(){if(i._factory===null){i._factory=new i}return i._factory},enumerable:true,configurable:true});i.prototype._isSupportMesh=function(){console.warn("Phaser-ce can not support mesh.");return false};i.prototype._buildTextureAtlasData=function(e,i){if(e){e.renderTexture=i}else{e=t.BaseObject.borrowObject(t.PhaserTextureAtlasData)}return e};i.prototype._buildArmature=function(e){var i=t.BaseObject.borrowObject(t.Armature);var a=new t.PhaserArmatureDisplay;i.init(e.armature,a,a,this._dragonBones);return i};i.prototype._buildSlot=function(e,a,r,n){e;n;var s=t.BaseObject.borrowObject(t.PhaserSlot);var o=new Phaser.Image(i._game,0,0,Phaser.Cache.DEFAULT);s.init(a,r,o,o);return s};i.prototype.buildArmatureDisplay=function(t,e,i,a){if(e===void 0){e=""}if(i===void 0){i=""}if(a===void 0){a=""}var r=this.buildArmature(t,e||"",i||"",a||"");if(r!==null){this._dragonBones.clock.add(r);return r.display}return null};i.prototype.getTextureDisplay=function(t,e){if(e===void 0){e=null}var a=this._getTextureData(e!==null?e:"",t);if(a!==null&&a.renderTexture!==null){return new Phaser.Sprite(i._game,0,0)}return null};Object.defineProperty(i.prototype,"soundEventManager",{get:function(){return this._dragonBones.eventManager},enumerable:true,configurable:true});i._game=null;i._dragonBonesInstance=null;i._factory=null;return i}(t.BaseFactory);t.PhaserFactory=e})(dragonBones||(dragonBones={}));