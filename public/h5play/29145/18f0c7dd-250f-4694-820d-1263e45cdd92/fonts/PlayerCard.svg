<svg width="218" height="52" viewBox="0 0 218 52" fill="none" xmlns="http://www.w3.org/2000/svg">
<g opacity="0.7" filter="url(#filter0_d_312_8732)">
<rect width="217.361" height="46.8829" rx="23.4414" fill="white"/>
</g>
<defs>
<filter id="filter0_d_312_8732" x="0" y="0" width="217.361" height="51.9966" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="5.11379"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_312_8732"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_312_8732" result="shape"/>
</filter>
</defs>
</svg>
