[1, ["ecpdLyjvZBwrvm+cedCcQy", "70pvVPm05GrLMIkpImYSEt", "027Nl8Lk1KuKK2/gFz9end", "81wf6T63dGN7gLG0wRKD/X", "a2IPstILRDR5dMQwdwmilR", "0d0Fi8UwpEobV6IvQr2zGK", "9fAs6hcmFPLrsiHB4TdSZp", "6axQVxtGdCWb+PxhEpw0Lw", "8aRfew0hRME5OVs8awAYLy", "37IDYOJElKHb6TAUEQ1GTw", "f60KaEprhCpIy65NFyitJR", "f66B7mj4xMnIunZrwhRTPs", "78rp4zMDZCrbkkB+3RrBAQ", "fbg1yH8fNPJbQOGV0OMXaL", "18o0/fJ4pM+LWjCLi4WN17", "7aNP48trpLu7Qjzw8Z3p5B", "5b3mWeX81PXaSFWaqFC29P", "6fte43ZoBHG6E1YRSpLMa+", "46x+gmMHZBd6jEL77Tcb8m", "1877c1353"], ["node", "_spriteFrame", "_textureSetter", "_parent", "_texture", "progressBar", "scene", "_N$file", "_N$dragonAsset", "_N$dragonAtlasAsset", "spriteFrame"], [["cc.Node", ["_name", "_id", "_active", "_components", "_contentSize", "_parent", "_trs", "_children", "_color", "_eulerAngles", "_anchorPoint"], 0, 9, 5, 1, 7, 2, 5, 5, 5], "cc.SpriteFrame", ["cc.Widget", ["_alignFlags", "_right", "_left", "_originalWidth", "_originalHeight", "alignMode", "_top", "node"], -4, 1], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Node", ["_name", "_parent", "_children", "_components", "_contentSize", "_trs", "_color", "_anchorPoint"], 2, 1, 2, 2, 5, 7, 5, 5], ["cc.Mask", ["_type", "node", "_materials", "_spriteFrame"], 2, 1, 3, 6], ["dragonBones.DragonBonesAtlasAsset", ["_name", "_atlasJson"], 1], ["cc.SceneAsset", ["_name", "asyncLoadAssets"], 1], ["cc.<PERSON>", ["node", "_designResolution"], 3, 1, 5], ["4457dhUfHZN4rtvzzbBn7jg", ["node", "progressBar"], 3, 1, 1], ["cc.Scene", ["_name", "_children", "_anchorPoint", "_trs"], 2, 2, 5, 7], ["cc.Camera", ["_clearFlags", "_depth", "_nearClip", "node"], 0, 1], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials", "_N$file"], -3, 1, 3, 6], ["cc.ProgressBar", ["_N$totalLength", "_N$mode", "_N$progress", "node", "_N$barSprite"], 0, 1, 1], ["dragonBones.ArmatureDisplay", ["_armatureName", "_animationName", "_preCacheMode", "_armatureKey", "node", "_materials", "_N$dragonAsset", "_N$dragonAtlasAsset"], -1, 1, 3, 6, 6], ["cc.BitmapFont", ["_name", "fontSize", "_fntConfig"], 0], ["dragonBones.DragonBonesAsset", ["_name", "_dragonBones<PERSON>son"], 1]], [[0, 0, 5, 3, 4, 6, 2], [3, 1, 0, 2, 3, 4, 3], [0, 0, 5, 3, 4, 2], [3, 2, 3, 4, 1], [3, 0, 2, 3, 4, 2], [2, 5, 0, 1, 6, 7, 5], [5, 0, 1, 2, 3, 2], [6, 0, 1, 3], [7, 0, 1, 3], [0, 0, 7, 3, 4, 2], [0, 0, 1, 7, 3, 4, 6, 3], [0, 0, 5, 7, 8, 4, 6, 9, 2], [0, 0, 5, 7, 3, 4, 2], [0, 0, 5, 3, 8, 4, 6, 2], [0, 0, 5, 7, 3, 4, 6, 2], [0, 0, 5, 7, 3, 4, 10, 6, 9, 2], [0, 0, 5, 3, 6, 2], [0, 0, 2, 5, 3, 4, 6, 3], [0, 0, 5, 3, 8, 4, 10, 6, 2], [4, 0, 1, 2, 3, 6, 4, 5, 2], [4, 0, 1, 2, 3, 4, 7, 5, 2], [2, 0, 2, 1, 7, 4], [2, 0, 7, 2], [2, 0, 3, 4, 7, 4], [5, 1, 2, 1], [8, 0, 1, 1], [9, 0, 1, 1], [3, 1, 0, 2, 3, 3], [10, 0, 1, 2, 3, 2], [11, 0, 1, 2, 3, 4], [12, 0, 1, 2, 3, 4, 5, 6, 7, 8, 7], [13, 0, 1, 2, 3, 4, 4], [14, 0, 1, 2, 3, 4, 5, 6, 7, 5], [15, 0, 1, 2, 4], [16, 0, 1, 3]], [[[{"name": "nuoc", "rect": [0, 0, 143, 437], "offset": [0, 0], "originalSize": [143, 437], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [6]], [[[7, "loading_tex", "{\"width\":512,\"imagePath\":\"loading_tex.png\",\"height\":64,\"name\":\"loading\",\"SubTexture\":[{\"width\":348,\"y\":1,\"height\":60,\"name\":\"loading/loading\",\"x\":1},{\"width\":27,\"y\":1,\"height\":26,\"name\":\"loading/cham\",\"x\":351}]}"]], 0, 0, [0], [4], [7]], [[[8, "Preload", null], [9, "View", [-3, -4, -5, -6, -7], [[21, 5, 426.5, 426.5, -1], [24, -2, [32]]], [5, 900, 480]], [10, "<PERSON><PERSON>", "43m2905KJGg7cC/jqW0zVf", [-12, -13, 1], [[25, -8, [5, 853, 480]], [26, -10, -9], [22, 45, -11]], [5, 853, 480], [426.5, 240, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "ProgressBar", 1, [-15, -16, -17, -18, -19], [-14], [4, 4294046193], [5, 104, 341], [-68.844, -48.216, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "Tube2", 1, [-20, -21, -22], [4, 4294046193], [5, 104, 341], [93.088, 126.04, 0, 0, 0, 0.3826834323650898, 0.9238795325112867, 1, 1, 1], [1, 0, 0, 45]], [2, "bg_poki", 2, [[3, -23, [0], 1], [23, 45, 853, 480, -24]], [5, 853, 480]], [12, "mask", 3, [-26], [[6, 2, -25, [13], 14]], [5, 102, 337]], [20, "bar", 6, [-28], [-27], [5, 101, 0], [0, 0.5, 0], [0, -183.64, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "thumb", 7, [[4, 0, -29, [10], 11], [5, 2, 1, -15.009, -15, -30]], [4, 4294959539], [5, 100, 29], [0, 0.5, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "mask", 4, [-32], [[6, 2, -31, [25], 26]], [5, 102, 337], [-1.0048591735576161e-14, 3.014577520672848e-14, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "bar", 9, [-34], [[1, 1, 0, -33, [23], 24]], [5, 1000, 224], [0, 0.5, 0], [-24.87499832837787, -160.17800000438575, 0, 0, 0, -0.3826834323650898, 0.9238795325112867, 1, 1, 1], [1, 0, 0, -45]], [0, "thumb", 10, [[4, 0, -35, [21], 22], [5, 2, 1, -15.009, -15, -36]], [5, 100, 29], [-2.842170943040401e-14, 224.5, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "New Node", [2], [0, 0, 0], [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "Main Camera", 2, [[29, 7, -1, 0.1, -37]], [0, 0, 537.8017757501365, 0, 0, 0, 1, 1, 1, 1]], [2, "1", 1, [[3, -38, [2], 3]], [5, 1010, 1650]], [17, "logo", false, 1, [[3, -39, [4], 5]], [5, 323, 147], [0, 214.712, 0, 0, 0, 0, 1, 1, 1, 1]], [0, "sprite1", 3, [[1, 1, 0, -40, [6], 7]], [5, 104, 23], [0, 167, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "dongnuoc", 3, [[4, 0, -41, [8], 9]], [4, 4294959539], [5, 17, 434], [0, 1, 1], [-2.32, 264.264, 0, 0, 0, 0, 1, -1, 1, 1]], [27, 2, 0, 7, [12]], [2, "sprite2", 3, [[1, 1, 0, -42, [15], 16]], [5, 104, 341]], [2, "lb", 3, [[30, "100%", 20, 32, false, 1, 1, -43, [17], 18]], [5, 89.38, 32]], [31, 307, 1, 0, 3, 18], [0, "sprite1", 4, [[1, 1, 0, -44, [19], 20]], [5, 104, 23], [2.842170943040401e-14, 166.99999652364608, 0, 0, 0, 0, 1, 1, 1, 1]], [0, "sprite2", 4, [[1, 1, 0, -45, [27], 28]], [5, 104, 341], [-1.0048591735576161e-14, 3.014577520672848e-14, 0, 0, 0, 0, 1, 1, 1, 1]], [0, "Loading", 1, [[32, "Armature", "animtion0", 0, "fb835c87-f1f3-4f25-b40e-195d0e31768b#18a34fdf-278a-4cf8-b5a3-08b8b858dd7b", -46, [29], 30, 31]], [5, 443.55009869514447, 407.14264769370493], [-39.034, -277.46, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 0, 1, 0, 0, 1, 0, -1, 14, 0, -2, 15, 0, -3, 3, 0, -4, 4, 0, -5, 24, 0, 0, 2, 0, 5, 21, 0, 0, 2, 0, 0, 2, 0, -1, 13, 0, -2, 5, 0, -1, 21, 0, -1, 16, 0, -2, 17, 0, -3, 6, 0, -4, 19, 0, -5, 20, 0, -1, 22, 0, -2, 9, 0, -3, 23, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, -1, 7, 0, -1, 18, 0, -1, 8, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, -1, 10, 0, 0, 10, 0, -1, 11, 0, 0, 11, 0, 0, 11, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, 0, 16, 0, 0, 17, 0, 0, 19, 0, 0, 20, 0, 0, 22, 0, 0, 23, 0, 0, 24, 0, 6, 12, 1, 3, 2, 2, 3, 12, 46], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 7, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 8, 9, -1, 1], [0, 8, 0, 9, 0, 10, 0, 1, 0, 11, 0, 2, 0, 0, 3, 0, 4, 0, 12, 0, 1, 0, 2, 0, 5, 0, 3, 0, 4, 0, 13, 14, 0, 5]], [[[33, "font", 32, {"commonHeight": 34, "fontSize": 32, "atlasName": "font.png", "fontDefDictionary": {"9": {"xOffset": 0, "yOffset": 0, "xAdvance": 168, "rect": {"x": 0, "y": 0, "width": 0, "height": 0}}, "32": {"xOffset": 0, "yOffset": 0, "xAdvance": 21, "rect": {"x": 0, "y": 0, "width": 0, "height": 0}}, "37": {"xOffset": 0, "yOffset": 0, "xAdvance": 46, "rect": {"x": 0, "y": 0, "width": 42, "height": 34}}, "48": {"xOffset": 0, "yOffset": 1, "xAdvance": 38, "rect": {"x": 0, "y": 35, "width": 34, "height": 33}}, "49": {"xOffset": 0, "yOffset": 1, "xAdvance": 25, "rect": {"x": 98, "y": 34, "width": 21, "height": 33}}, "50": {"xOffset": 0, "yOffset": 1, "xAdvance": 35, "rect": {"x": 43, "y": 0, "width": 31, "height": 33}}, "51": {"xOffset": 0, "yOffset": 1, "xAdvance": 33, "rect": {"x": 75, "y": 0, "width": 29, "height": 33}}, "52": {"xOffset": 0, "yOffset": 0, "xAdvance": 36, "rect": {"x": 0, "y": 69, "width": 32, "height": 34}}, "53": {"xOffset": 0, "yOffset": 1, "xAdvance": 34, "rect": {"x": 65, "y": 69, "width": 30, "height": 33}}, "54": {"xOffset": 0, "yOffset": 1, "xAdvance": 35, "rect": {"x": 35, "y": 35, "width": 31, "height": 33}}, "55": {"xOffset": 0, "yOffset": 1, "xAdvance": 33, "rect": {"x": 96, "y": 68, "width": 29, "height": 33}}, "56": {"xOffset": 0, "yOffset": 1, "xAdvance": 34, "rect": {"x": 67, "y": 34, "width": 30, "height": 33}}, "57": {"xOffset": 0, "yOffset": 1, "xAdvance": 35, "rect": {"x": 33, "y": 69, "width": 31, "height": 33}}}, "kerningDict": {}}]], 0, 0, [0], [10], [15]], [[{"name": "font", "rect": [0, 0, 125, 103], "offset": [-1.5, 12.5], "originalSize": [128, 128], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [16]], [[{"name": "bg_poki", "rect": [0, 0, 853, 480], "offset": [0, 0], "originalSize": [853, 480], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [17]], [[{"name": "logo", "rect": [3, 5, 323, 147], "offset": [-0.5, 1.5], "originalSize": [330, 160], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [18]], [[{"name": "dongnuoc", "rect": [213, 331, 47, 500], "offset": [0, 0], "originalSize": [47, 500], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [19]], [[[34, "loading_ske", "{\"frameRate\":24,\"isGlobal\":0,\"armature\":[{\"bone\":[{\"name\":\"root\",\"transform\":{}},{\"length\":13,\"transform\":{\"y\":364.4,\"skX\":-90,\"skY\":-90,\"x\":178.6},\"parent\":\"root\",\"name\":\"cham\"},{\"length\":13,\"transform\":{\"y\":364.4,\"skX\":-86.0548,\"skY\":-86.0548,\"x\":207.85},\"parent\":\"root\",\"name\":\"cham1\"},{\"length\":12,\"transform\":{\"y\":364.85,\"skX\":-87.8789,\"skY\":-87.8789,\"x\":240.7},\"parent\":\"root\",\"name\":\"cham2\"}],\"type\":\"Armature\",\"name\":\"Armature\",\"animation\":[{\"playTimes\":0,\"bone\":[{\"rotateFrame\":[],\"scaleFrame\":[],\"name\":\"root\",\"translateFrame\":[]},{\"rotateFrame\":[{\"duration\":38}],\"scaleFrame\":[{\"duration\":38}],\"name\":\"cham\",\"translateFrame\":[{\"y\":-345.9115,\"curve\":[0.5,0,1,1],\"duration\":15,\"x\":2.5973},{\"y\":-357.2885,\"curve\":[0,0,0.5,1],\"duration\":12,\"x\":2.6},{\"y\":-345.9115,\"duration\":11,\"x\":2.5973}]},{\"rotateFrame\":[{\"tweenEasing\":0,\"duration\":12},{\"duration\":26}],\"scaleFrame\":[{\"tweenEasing\":0,\"duration\":12},{\"duration\":26}],\"name\":\"cham1\",\"translateFrame\":[{\"y\":-345.9115,\"tweenEasing\":0,\"duration\":12,\"x\":2.5973},{\"y\":-345.9115,\"tweenEasing\":0,\"duration\":8,\"x\":2.5973},{\"y\":-360,\"curve\":[0,0,0.5,1],\"duration\":12,\"x\":2.6},{\"y\":-345.91,\"duration\":6,\"x\":2.6}]},{\"rotateFrame\":[{\"tweenEasing\":0,\"duration\":15},{\"duration\":23}],\"scaleFrame\":[{\"tweenEasing\":0,\"duration\":15},{\"duration\":23}],\"name\":\"cham2\",\"translateFrame\":[{\"y\":-345.9115,\"tweenEasing\":0,\"duration\":15},{\"y\":-346.36,\"curve\":[0.5,0,1,1],\"duration\":7},{\"y\":-362,\"curve\":[0,0,0.5,1],\"duration\":16},{\"y\":-346.36,\"duration\":0}]}],\"slot\":[{\"colorFrame\":[],\"displayFrame\":[],\"name\":\"loading\"},{\"colorFrame\":[],\"displayFrame\":[],\"name\":\"cham\"},{\"colorFrame\":[],\"displayFrame\":[],\"name\":\"cham1\"},{\"colorFrame\":[],\"displayFrame\":[],\"name\":\"cham2\"}],\"ffd\":[],\"ik\":[],\"frame\":[],\"name\":\"animtion0\",\"duration\":38}],\"skin\":[{\"slot\":[{\"display\":[{\"path\":\"loading/loading\",\"type\":\"image\",\"name\":\"loading/loading\",\"transform\":{\"x\":-15.3447}}],\"name\":\"loading\"},{\"display\":[{\"path\":\"loading/cham\",\"type\":\"image\",\"name\":\"loading/cham\",\"transform\":{\"y\":-0.0208,\"skX\":87.8789,\"skY\":87.8789,\"x\":0.7071}}],\"name\":\"cham2\"},{\"display\":[{\"path\":\"loading/cham\",\"type\":\"image\",\"name\":\"loading/cham\",\"transform\":{\"y\":0.0924,\"skX\":90,\"skY\":90,\"x\":0.2574}}],\"name\":\"cham\"},{\"display\":[{\"path\":\"loading/cham\",\"type\":\"image\",\"name\":\"loading/cham\",\"transform\":{\"y\":0.943,\"skX\":86.0548,\"skY\":86.0548,\"x\":0.323}}],\"name\":\"cham1\"}],\"name\":\"\"}],\"defaultActions\":[{\"gotoAndPlay\":\"animtion0\"}],\"aabb\":{\"width\":443.55009869514447,\"y\":-30,\"height\":407.14264769370493,\"x\":-189.34471488083886},\"slot\":[{\"name\":\"loading\",\"parent\":\"root\",\"color\":{}},{\"z\":1,\"name\":\"cham\",\"parent\":\"cham\",\"color\":{}},{\"z\":2,\"name\":\"cham1\",\"parent\":\"cham1\",\"color\":{}},{\"z\":3,\"name\":\"cham2\",\"parent\":\"cham2\",\"color\":{}}],\"ik\":[],\"frameRate\":24}],\"name\":\"loading\",\"version\":\"5.5\"}"]], 0, 0, [], [], []]]]