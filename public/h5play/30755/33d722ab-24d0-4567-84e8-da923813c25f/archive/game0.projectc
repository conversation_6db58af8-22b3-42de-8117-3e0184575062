[project]
title = blocky-puzzle
version = 1.0
write_log = 0
minimum_log_level = 1
compress_archive = 1
publisher = unnamed
developer = OPlay Games
custom_resources = res/
title_as_file_name = blockypuzzle

[display]
width = 590
height = 960
high_dpi = 1
samples = 0
fullscreen = 0
update_frequency = 0
swap_interval = 1
vsync = 0
display_profiles = /builtins/render/default.display_profilesc
dynamic_orientation = 0
display_device_info = 0

[render]
clear_color_red = 0
clear_color_green = 0.0
clear_color_blue = 0
clear_color_alpha = 0

[physics]
type = 2D
max_collision_object_count = 16
use_fixed_timestep = 0
gravity_y = -10
debug = 0
debug_alpha = 0.9
world_count = 9
gravity_x = 0
gravity_z = 0
scale = 1
allow_dynamic_transforms = 1
debug_scale = 30
max_collisions = 64
max_contacts = 128
contact_impulse_limit = 0
ray_cast_limit_2d = 64
ray_cast_limit_3d = 128
trigger_overlap_capacity = 16
velocity_threshold = 1
max_fixed_timesteps = 2

[bootstrap]
main_collection = /main/main.collectionc
render = /render/my_render.renderc

[graphics]
default_texture_min_filter = linear
default_texture_mag_filter = linear
max_draw_calls = 1024
max_characters = 8192
max_font_batches = 128
max_debug_vertices = 10000
texture_profiles = /tex_profiles.texture_profiles
verify_graphics_calls = 1
opengl_version_hint = 33
opengl_core_profile_hint = true
memory_size = 512

[shader]
output_spirv = 0

[sound]
gain = 1
max_sound_data = 32
max_sound_buffers = 32
max_sound_sources = 16
max_sound_instances = 256
max_component_count = 32
use_thread = 1

[resource]
http_cache = 0
max_resources = 1024

[input]
repeat_delay = 0.5
repeat_interval = 0.2
gamepads = /builtins/input/default.gamepadsc
game_binding = /input/game.input_bindingc
use_accelerometer = 1

[sprite]
max_count = 340
subpixels = 1

[model]
max_count = 0
split_meshes = 0

[mesh]
max_count = 0

[gui]
max_count = 16
max_particlefx_count = 32
max_particle_count = 512
max_animation_count = 128

[collection]
max_instances = 1024
max_input_stack_entries = 16

[collection_proxy]
max_count = 12

[collectionfactory]
max_count = 4

[factory]
max_count = 32

[ios]
launch_screen = /builtins/manifests/ios/LaunchScreen.storyboardc
pre_renderered_icons = 0
bundle_identifier = example.unnamed
bundle_name = 
infoplist = /builtins/manifests/ios/Info.plist
privacymanifest = /builtins/manifests/ios/PrivacyInfo.xcprivacy
default_language = en
localizations = en

[android]
version_code = 1
minimum_sdk_version = 19
target_sdk_version = 34
package = com.example.todo
gcm_sender_id = 
manifest = /builtins/manifests/android/AndroidManifest.xml
iap_provider = GooglePlay
input_method = HiddenInputField
immersive_mode = 0
display_cutout = 1
debuggable = 0
proguard = 
extract_native_libs = 1

[osx]
app_icon = 
infoplist = /builtins/manifests/osx/Info.plist
privacymanifest = /builtins/manifests/osx/PrivacyInfo.xcprivacy
bundle_identifier = example.unnamed
bundle_name = 
bundle_version = 1
default_language = en
localizations = en

[windows]
app_icon = 

[html5]
custom_heap_size = 0
heap_size = 32
htmlfile = /html_engine_template.html
cssfile = /html_css_styles.css
archive_location_prefix = archive
archive_location_suffix = 
engine_arguments = --verify-graphics-calls=false
wasm_streaming = 1
show_fullscreen_button = 0
show_made_with_defold = 0
show_console_banner = 0
scale_mode = stretch

[particle_fx]
max_count = 256
max_emitter_count = 350
max_particle_count = 1024

[network]
http_timeout = 0
ssl_certificates = 
http_thread_count = 4
http_cache_enabled = 1

[library]
include_dirs = 

[script]
shared_state = 1

[label]
max_count = 16
subpixels = 1

[profiler]
track_cpu = 0
sleep_between_server_updates = 0

[liveupdate]
settings = /liveupdate.settings
enabled = 1
mount_on_start = 1

[tilemap]
max_count = 0
max_tile_count = 0

[engine]
run_while_iconified = 0
fixed_update_frequency = 60
max_time_step = 0.5

[native_extension]
app_manifest = /main.appmanifest

[gameanalytics]
game_key_html5 = 396c9e9cda4b7ece44a365ac6c118696
secret_key_html5 = 7223ee1e293e897a08afdbd76e44d6d413d18d91
game_key_osx = 396c9e9cda4b7ece44a365ac6c118696
secret_key_osx = 7223ee1e293e897a08afdbd76e44d6d413d18d91
build_html5 = 1.13.2
build_osx = 1.13.2

