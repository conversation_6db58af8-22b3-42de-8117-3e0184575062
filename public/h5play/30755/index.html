<!doctype html>
<html lang="en">
 <head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0, minimal-ui, shrink-to-fit=no">
  <meta name="apple-mobile-web-app-capable" content="yes"> <!-- The above 4 meta tags *must* come first in the head; any other head content must come *after* these tags -->
  <title>blocky-puzzle 1.0</title>
  <style type="text/css">
	/* Disable user selection to avoid strange bug in Chrome on Windows:
	* Selecting a text outside the canvas, then clicking+draging would
	* drag the selected text but block mouse down/up events to the engine.
	*/
	body {
	
		position: fixed; /* Prevent overscroll */
	
		margin:0;
		padding:0;
	}

	.canvas-app-container {
		width: 100%;
		height: 100%;
		position: absolute;
		align-items: center;
		justify-content: center;
		overflow: hidden;
	}

	.canvas-app-container:-webkit-full-screen {
		/* Auto width and height in Safari/Chrome fullscreen. */
		width: auto;
		height: auto;
	}

	#canvas {
		outline: none;
		border: 0;
		width: 100%;
		vertical-align: bottom;
	}

	#canvas-container {
		position: relative;
	}

	canvas:focus, canvas:active {
		outline: none;
		border: 0;
		ie-dummy: expression(this.hideFocus=true);
		-moz-outline-style: none;
	}

	body, div {
		-webkit-tap-highlight-color: rgba(0,0,0,0);
		-webkit-touch-callout: none;
		-webkit-user-select: none;
		-khtml-user-select: none;
		-moz-user-select: none;
		-ms-user-select: none;
		user-select: none;
	}

	.loading-screen-container {
		position: absolute;
		top: 0;
		left: 0;
        height: 100%;
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
    
    .message {
        height: 30px;
        display: flex;
        align-items: center;
        font-family: Verdana, Helvetica, sans-serif;
    }

	.logo-small {
		width: 100%;
		max-width: 180px;
	}
    
    .logo {
		width: 100%;
		max-width: 256px;
		height: auto;
		animation: scalePulse 1.2s infinite;
	}

	@keyframes scalePulse {
		0% {
			transform: scale(1); /* Начальный размер */
		}
		50% {
			transform: scale(1.1);
		}
		100% {
			transform: scale(1);
		}
	}
    
    .loading-bar {
        margin-top: 0px;
        width: 180px;
      display: flex;
      background: rgba(255,255,255,0.1);
    }

    .main-container {
        flex-grow: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
    }

	.block {
		background: linear-gradient(#FF6B6B, #FF3B3B); /* Градиент от светлого красного к тёмному красному */
		border-top: 5px solid #FF8C8C; /* Светло-красный */
		border-bottom: 5px solid #B20000; /* Тёмно-красный */
		border-left: 5px solid #FF5A5A; /* Средний красный */
		border-right: 5px solid #CC0000; /* Более глубокий красный */
		width: 20px;
		min-width: 20px;
		height: 20px;
		min-height: 20px;
		box-shadow: 1px 2px 36px #FFAAAA7A; /* Красное свечение */
		opacity: 0;
		animation: fadeInOut 3s linear 0s infinite;
	}
    
    .block:nth-child(2) { 
        animation: fadeInOut1 3s linear 0s infinite;
    }
    
    .block:nth-child(3) {
        animation: fadeInOut2 3s linear 0s infinite;
    }
    
    .block:nth-child(4) {
        animation: fadeInOut3 3s linear 0s infinite;
    }
    
    .block:nth-child(5) {
        animation: fadeInOut4 3s linear 0s infinite;
    }
    
    .block:nth-child(6) {
        animation: fadeInOut5 3s linear 0s infinite;
    }
    
    @keyframes fadeInOut {
      0%, 15% {
        opacity: 0;
        transform: scale(0.5);
      }
      20%, 100% {
        opacity: 1;
        transform: scale(1);
      }
    }
    @keyframes fadeInOut1 {
      0%, 30% {
        opacity: 0;
        transform: scale(0.5);
      }
      35%, 100% {
        opacity: 1;
        transform: scale(1);
      }
    }
    @keyframes fadeInOut2 {
      0%, 45% {
        opacity: 0;
        transform: scale(0.5);
      }
      50%, 100% {
        opacity: 1;
        transform: scale(1);
      }
    }
    @keyframes fadeInOut3 {
      0%, 60% {
        opacity: 0;
        transform: scale(0.5);
      }
      66%, 100% {
        opacity: 1;
        transform: scale(1);
      }
    }
    @keyframes fadeInOut4 {
      0%, 75% {
        opacity: 0;
        transform: scale(0.5);
      }
      81%, 100% {
        opacity: 1;
        transform: scale(1);
      }
    }
    @keyframes fadeInOut5 {
      0%, 90% {
        opacity: 0;
        transform: scale(0.5);
      }
      96%, 100% {
        opacity: 1;
        transform: scale(1);
      }
    }

		.canvas-app-progress {
		position: absolute;
		background-color: #d1dbeb;
		height: 6px;
		margin-top: -6px;
		width: 100%;
	}

	.canvas-app-progress-bar {
		font-size: 12px;
		height: 6px;
		color: rgb(255, 255, 255);
		background-color: #1a72eb;
		text-align: center;
		line-height: 20px;
		transition: transform 1s ease;
		transform-origin: left;
		transform: scaleX(1.0);
	}


	.link, .button {
		font-family: sans-serif;
		font-size: 14px;
		font-weight: normal;
		font-style: normal;
		font-stretch: normal;
		line-height: normal;
		letter-spacing: 0px;
		padding-top: 12px;
	}

	.buttons-background {
		background-color: #ffffff;
		width: 100%;
		height: 42px;
	}

	body {
		background-color: #52AAEC;
	}

	.canvas-app-canvas {

	}

	.canvas-app-container {
		background: #52AAEC;
	}
	</style>
  <script src="/html/def/def-sdk.js"></script>
  <script type="text/javascript" src="GameAnalytics.js"></script>
 </head>
 <body>
  <div id="app-container" class="canvas-app-container">
   <div id="running-from-file-warning" style="display: none; margin: 3em;">
    <h1>Running from local file ⚠️</h1>
    <p>It seems like you have opened this file by double-clicking on it. In order to test your build in a browser <b>you need to load this file from a web server</b>. You can either upload this file and the rest of the files from a Defold HTML5 bundle to a web hosting service OR host them using a local web server on your home network.</p>
    <p><a href="https://defold.com/manuals/html5/#testing-html5-build" target="_blank">Learn more about running a local web server in the Defold HTML5 manual</a>.</p>
   </div>
   <div id="webgl-not-supported" style="display: none; margin: 3em;">
    <h1>WebGL not supported ⚠️</h1>
    <p>WebGL is not supported by your browser - visit <a href="https://get.webgl.org/">https://get.webgl.org/</a> to learn more.</p>
   </div>
   <div id="canvas-container" class="canvas-app-canvas-container">
    <canvas id="canvas" class="canvas-app-canvas" tabindex="1" width="590" height="960"></canvas>
   </div>
   <div class="buttons-background">
   </div>
  </div> <!-- -->
  <script id="engine-loader" type="text/javascript" src="dmloader.js"></script>
  <script id="engine-setup" type="text/javascript"></script>
  <script id="custom-progress-bar" type="text/javascript">
		ProgressView.addProgress = function(canvas) {
			canvas.insertAdjacentHTML('afterend',
			`
				<div class="loading-screen-container" id="loading-screen-container">
       				<div class="main-container">
       				    <img src="game_logo.png" class="logo">
						<div class="message" id="percentage" style="margin-top: 6px;">0%</div>
       				    <div class="loading-bar">
       				        <div class="block"></div>
       				        <div class="block"></div>
       				        <div class="block"></div>
       				        <div class="block"></div>
       				        <div class="block"></div>
       				        <div class="block"></div>
       				      </div>
       				    <div class="message" id="message"></div>
       				</div>
				
       				<img src="oplay_logo.png" class="logo-small" style="width: 100%; max-width: 160px; height: auto;">
    			</div>
			`)

			ProgressView.progress = document.getElementById("loading-screen-container");
			document.getElementById("percentage").style.fontFamily = "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif";
			function updateProgress(percentage) {
				let percentageText = document.getElementById("percentage")
				if (percentageText){
					percentageText.innerHTML = Math.min(Math.ceil(percentage), 100) + "%";
				}
			}
			Progress.addListener(updateProgress);

			const phrases = ["Polishing blocks...", "Preparing gems...", "Loading effects...", "Loading game..."];
        	let index = 0;

        	function changeText() {
				let messageText = document.getElementById("message")
				if (messageText) {
					messageText.innerHTML = phrases[index];
				}
        	    index++;
        	    if (index < phrases.length) {
        	        setTimeout(changeText, 2000);
        	    }
        	}
        	changeText();
		};
	</script>
  <script id="engine-start" type="text/javascript">
		var runningFromFileWarning = document.getElementById("running-from-file-warning");
		if (window.location.href.startsWith("file://")) {
			runningFromFileWarning.style.display = "block";
		}
		else {
			EngineLoader.load("canvas", "blockypuzzle");
			runningFromFileWarning.parentNode.removeChild(runningFromFileWarning);
		}
	</script>
  <script id="poki-sdk-setup" type="text/javascript">
		var data = {};
		var isLoadFinished = false;
		Progress.addListener(function(percentage){
			data.percentageDone = percentage / 100;
			if (!isLoadFinished)
				PokiSDK.gameLoadingProgress(data);
			if (percentage == 100 && !isLoadFinished) {
				PokiSDK.gameLoadingFinished();
				isLoadFinished = true;
			}
		});
		Module['onRuntimeInitialized'] = function() {
			PokiSDK.init().then(()=>{
				Module.runApp("canvas");
			}, ()=>{
				Module.runApp("canvas");
			});
		};
	</script>
 </body>
</html>