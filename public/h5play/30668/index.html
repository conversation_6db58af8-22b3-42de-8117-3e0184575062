<!DOCTYPE html>
<html lang="en">
<head>
	
	<meta charset="utf-8">
	
	<title>Level Devil</title>
	
	<meta id="viewport" name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
	<meta name="apple-mobile-web-app-capable" content="yes">
	
	
	<link rel="shortcut icon" type="image/png" href="./favicon.png">

	
	<script src="/html/def/def-sdk.js"></script>
	
	<script src="./Level Devil.js"></script>

	<script>
		window.addEventListener ("touchmove", function (event) { event.preventDefault (); }, { capture: false, passive: false });
		if (typeof window.devicePixelRatio != 'undefined' && window.devicePixelRatio > 2) {
			var meta = document.getElementById ("viewport");
			meta.setAttribute ('content', 'width=device-width, initial-scale=' + (2 / window.devicePixelRatio) + ', user-scalable=no');
		}
	</script>
	
	<style>
		html,body { margin: 0; padding: 0; height: 100%; overflow: hidden; }
		#openfl-content { background: #000000; width: 100%; height: 100%; }
		#progress { position: relative; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 50%; }

	</style>
	
</head>
<body>
	
	<noscript>This webpage makes extensive use of JavaScript. Please enable JavaScript in your web browser to view this page.</noscript>
	
	<div id="openfl-content"></div>
	
	<script>
		lime.embed ("Level Devil", "openfl-content", 854, 480, { parameters: {} });
	</script>
	
</body>
</html>
